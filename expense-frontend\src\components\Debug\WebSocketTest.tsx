import React, { useState } from 'react';
import { useWebSocketContext } from '../../contexts/WebSocketContext';
import { Wifi, WifiOff, Send, Trash2, RefreshCw } from 'lucide-react';

const WebSocketTest: React.FC = () => {
  const {
    isConnected,
    isConnecting,
    connectionError,
    lastMessage,
    messageHistory,
    sendMessage,
    connect,
    disconnect,
    reconnectAttempts,
    clearMessageHistory,
  } = useWebSocketContext();

  const [testMessage, setTestMessage] = useState('');

  const handleSendTestMessage = () => {
    if (testMessage.trim()) {
      sendMessage({
        type: 'test_message',
        message: testMessage,
        timestamp: new Date().toISOString(),
      });
      setTestMessage('');
    }
  };

  const handleSendPing = () => {
    sendMessage({
      type: 'ping',
      timestamp: new Date().toISOString(),
    });
  };

  const getConnectionStatusColor = () => {
    if (isConnected) return 'text-green-600';
    if (isConnecting) return 'text-yellow-600';
    if (connectionError) return 'text-red-600';
    return 'text-gray-600';
  };

  const getConnectionStatusText = () => {
    if (isConnected) return 'Connected';
    if (isConnecting) return 'Connecting...';
    if (connectionError) return `Error: ${connectionError}`;
    return 'Disconnected';
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          WebSocket Connection Test
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Test the real-time notification WebSocket connection
        </p>
      </div>

      {/* Connection Status */}
      <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            {isConnected ? (
              <Wifi className="w-6 h-6 text-green-600" />
            ) : (
              <WifiOff className="w-6 h-6 text-red-600" />
            )}
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Connection Status
              </h3>
              <p className={`text-sm ${getConnectionStatusColor()}`}>
                {getConnectionStatusText()}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {reconnectAttempts > 0 && (
              <span className="text-sm text-yellow-600">
                Reconnect attempts: {reconnectAttempts}
              </span>
            )}
            <button
              onClick={isConnected ? disconnect : connect}
              className={`px-4 py-2 rounded-lg font-medium ${
                isConnected
                  ? 'bg-red-100 text-red-700 hover:bg-red-200'
                  : 'bg-green-100 text-green-700 hover:bg-green-200'
              }`}
            >
              {isConnected ? 'Disconnect' : 'Connect'}
            </button>
          </div>
        </div>

        {/* Connection Details */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-500 dark:text-gray-400">Status:</span>
            <span className={`ml-2 font-medium ${getConnectionStatusColor()}`}>
              {isConnected ? 'Online' : 'Offline'}
            </span>
          </div>
          <div>
            <span className="text-gray-500 dark:text-gray-400">Connecting:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {isConnecting ? 'Yes' : 'No'}
            </span>
          </div>
          <div>
            <span className="text-gray-500 dark:text-gray-400">Messages:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {messageHistory.length}
            </span>
          </div>
          <div>
            <span className="text-gray-500 dark:text-gray-400">Reconnects:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {reconnectAttempts}
            </span>
          </div>
        </div>
      </div>

      {/* Test Message Sender */}
      <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
          Send Test Message
        </h3>
        <div className="flex items-center space-x-3">
          <input
            type="text"
            value={testMessage}
            onChange={(e) => setTestMessage(e.target.value)}
            placeholder="Enter test message..."
            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            onKeyPress={(e) => e.key === 'Enter' && handleSendTestMessage()}
          />
          <button
            onClick={handleSendTestMessage}
            disabled={!isConnected || !testMessage.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            <Send className="w-4 h-4" />
            <span>Send</span>
          </button>
          <button
            onClick={handleSendPing}
            disabled={!isConnected}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Ping</span>
          </button>
        </div>
      </div>

      {/* Last Message */}
      {lastMessage && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
            Last Message Received
          </h3>
          <div className="bg-white dark:bg-gray-700 p-3 rounded border">
            <pre className="text-sm text-gray-900 dark:text-white overflow-x-auto">
              {JSON.stringify(lastMessage, null, 2)}
            </pre>
          </div>
        </div>
      )}

      {/* Message History */}
      <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-gray-900 dark:text-white">
            Message History ({messageHistory.length})
          </h3>
          <button
            onClick={clearMessageHistory}
            className="px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 flex items-center space-x-1"
          >
            <Trash2 className="w-4 h-4" />
            <span>Clear</span>
          </button>
        </div>
        
        <div className="max-h-96 overflow-y-auto space-y-2">
          {messageHistory.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400 text-center py-4">
              No messages received yet
            </p>
          ) : (
            messageHistory.map((message, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-600 p-3 rounded border text-sm"
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-blue-600 dark:text-blue-400">
                    {message.type}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {message.timestamp ? new Date(message.timestamp).toLocaleTimeString() : 'No timestamp'}
                  </span>
                </div>
                {message.title && (
                  <div className="font-medium text-gray-900 dark:text-white mb-1">
                    {message.title}
                  </div>
                )}
                {message.message && (
                  <div className="text-gray-700 dark:text-gray-300 mb-2">
                    {message.message}
                  </div>
                )}
                {(message.action_url || message.category || message.priority) && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 space-x-2">
                    {message.category && <span>Category: {message.category}</span>}
                    {message.priority && <span>Priority: {message.priority}</span>}
                    {message.action_url && <span>Action: {message.action_url}</span>}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default WebSocketTest;
