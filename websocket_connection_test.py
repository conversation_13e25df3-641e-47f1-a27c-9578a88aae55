#!/usr/bin/env python3
"""
WebSocket Connection Test Script
Tests the WebSocket endpoint to verify single connection behavior
"""

import asyncio
import websockets
import json
import time
from datetime import datetime

# Configuration
WS_URL = "ws://localhost:8000/ws/notifications"
TEST_TOKEN = "test_token_here"  # Replace with actual token

async def test_single_connection():
    """Test single WebSocket connection"""
    print(f"🔗 Testing single WebSocket connection...")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        ws_url = f"{WS_URL}?token={TEST_TOKEN}"
        print(f"🌐 Connecting to: {ws_url}")
        
        async with websockets.connect(ws_url) as websocket:
            print("✅ WebSocket connected successfully")
            
            # Wait for welcome message
            try:
                welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                welcome_data = json.loads(welcome_msg)
                print(f"📨 Welcome message: {welcome_data.get('message', 'Connected')}")
            except asyncio.TimeoutError:
                print("⚠️ No welcome message received within 5 seconds")
            
            # Send a ping message
            ping_msg = {
                "type": "ping",
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send(json.dumps(ping_msg))
            print("📤 Sent ping message")
            
            # Wait for pong response
            try:
                pong_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                pong_data = json.loads(pong_msg)
                print(f"📥 Received: {pong_data.get('type', 'unknown')} message")
            except asyncio.TimeoutError:
                print("⚠️ No pong response received within 5 seconds")
            
            print("✅ Single connection test completed successfully")
            
    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ WebSocket connection closed: {e}")
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")

async def test_multiple_connections():
    """Test multiple simultaneous WebSocket connections to detect spam"""
    print(f"\n🔗 Testing multiple WebSocket connections...")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    connections = []
    connection_count = 3
    
    try:
        ws_url = f"{WS_URL}?token={TEST_TOKEN}"
        
        # Create multiple connections simultaneously
        for i in range(connection_count):
            print(f"🌐 Creating connection {i+1}/{connection_count}...")
            try:
                websocket = await websockets.connect(ws_url)
                connections.append(websocket)
                print(f"✅ Connection {i+1} established")
                
                # Small delay between connections
                await asyncio.sleep(0.1)
                
            except Exception as e:
                print(f"❌ Connection {i+1} failed: {e}")
        
        print(f"📊 Successfully created {len(connections)} connections")
        
        # Keep connections alive for a short time
        await asyncio.sleep(2)
        
        # Close all connections
        for i, websocket in enumerate(connections):
            try:
                await websocket.close()
                print(f"🔒 Closed connection {i+1}")
            except Exception as e:
                print(f"⚠️ Error closing connection {i+1}: {e}")
        
        print("✅ Multiple connection test completed")
        
    except Exception as e:
        print(f"❌ Multiple connection test failed: {e}")

async def test_rapid_reconnections():
    """Test rapid reconnection attempts to verify debouncing"""
    print(f"\n🔗 Testing rapid reconnection attempts...")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    ws_url = f"{WS_URL}?token={TEST_TOKEN}"
    reconnect_count = 5
    
    for i in range(reconnect_count):
        try:
            print(f"🌐 Rapid connection attempt {i+1}/{reconnect_count}...")
            
            async with websockets.connect(ws_url) as websocket:
                print(f"✅ Connection {i+1} established")
                
                # Very short connection time
                await asyncio.sleep(0.1)
                
            print(f"🔒 Connection {i+1} closed")
            
            # No delay between attempts to test debouncing
            
        except Exception as e:
            print(f"❌ Rapid connection {i+1} failed: {e}")
    
    print("✅ Rapid reconnection test completed")

async def main():
    """Run all WebSocket tests"""
    print("🚀 WebSocket Connection Test Suite")
    print("=" * 50)
    
    # Test 1: Single connection
    await test_single_connection()
    
    # Test 2: Multiple connections
    await test_multiple_connections()
    
    # Test 3: Rapid reconnections
    await test_rapid_reconnections()
    
    print("\n" + "=" * 50)
    print("🎉 All WebSocket tests completed!")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    print("⚠️ Note: Replace TEST_TOKEN with a valid JWT token before running")
    print("💡 You can get a token by logging into the frontend and checking localStorage")
    print()
    
    # Uncomment the line below to run the tests
    # asyncio.run(main())
    
    print("🔧 To run the tests:")
    print("1. Get a valid JWT token from the frontend")
    print("2. Replace TEST_TOKEN in this script")
    print("3. Uncomment the asyncio.run(main()) line")
    print("4. Run: python websocket_connection_test.py")
