@echo off
echo Starting Expense Tracker Backend Server...
echo.

REM Change to the expense-app directory
cd expense-app

REM Activate virtual environment (Windows)
call ..\.venv\Scripts\activate.bat

REM Install dependencies if needed
echo Installing/updating dependencies...
pip install -r requirements.txt

REM Start the FastAPI server
echo.
echo Starting FastAPI server...
echo Server will be available at: http://localhost:8000
echo API documentation at: http://localhost:8000/docs
echo.
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

pause
