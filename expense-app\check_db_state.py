#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to check current database state and migration status
"""

import sqlite3
import os
from alembic.config import Config
from alembic import command
from alembic.script import ScriptDirectory
from alembic.runtime.environment import EnvironmentContext

def check_sqlite_schema():
    """Check the current SQLite database schema"""
    db_path = "./dev.db"
    
    if not os.path.exists(db_path):
        print("❌ Database file 'dev.db' does not exist!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if users table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users';")
        users_table = cursor.fetchone()
        
        if not users_table:
            print("❌ Users table does not exist!")
            return False
        
        print("✅ Users table exists")
        
        # Get users table schema
        cursor.execute("PRAGMA table_info(users);")
        columns = cursor.fetchall()
        
        print("\n📋 Current users table schema:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        
        # Check for new profile columns
        column_names = [col[1] for col in columns]
        required_columns = [
            'full_name', 'avatar_url', 'phone', 'timezone', 'currency', 'language',
            'email_notifications', 'expense_notifications', 'approval_notifications', 
            'settlement_notifications', 'created_at', 'updated_at'
        ]
        
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"\n❌ Missing columns: {', '.join(missing_columns)}")
            return False
        else:
            print("\n✅ All required profile columns exist")
            return True
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def check_alembic_state():
    """Check current Alembic migration state"""
    try:
        alembic_cfg = Config("alembic.ini")
        
        # Get current revision
        script_dir = ScriptDirectory.from_config(alembic_cfg)
        
        def get_current_revision():
            with EnvironmentContext(alembic_cfg, script_dir) as env_context:
                env_context.configure(connection=None, target_metadata=None)
                return env_context.get_current_revision()
        
        current_rev = get_current_revision()
        print(f"\n📍 Current database revision: {current_rev}")
        
        # Get available revisions
        revisions = list(script_dir.walk_revisions())
        print(f"\n📚 Available migrations:")
        for rev in reversed(revisions):
            print(f"  - {rev.revision}: {rev.doc}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking Alembic state: {e}")
        return False

def main():
    """Main function"""
    print("🔍 Checking Database State")
    print("=" * 50)
    
    # Check SQLite schema
    schema_ok = check_sqlite_schema()
    
    # Check Alembic state
    alembic_ok = check_alembic_state()
    
    print("\n" + "=" * 50)
    if schema_ok and alembic_ok:
        print("✅ Database state looks good!")
    else:
        print("❌ Database needs migration!")
        print("\nRecommended actions:")
        print("1. Run: alembic upgrade head")
        print("2. Or recreate database: rm dev.db && alembic upgrade head")

if __name__ == "__main__":
    main()
