#!/usr/bin/env python3
"""
Quick fix script to resolve the "no such column: users.full_name" error
This script will add the missing columns directly to the SQLite database
"""

import sqlite3
import os
import sys

def add_missing_columns():
    """Add missing columns to the users table"""
    db_path = "./dev.db"
    
    if not os.path.exists(db_path):
        print("❌ Database file 'dev.db' does not exist!")
        print("Please run the application first to create the initial database.")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current schema
        cursor.execute("PRAGMA table_info(users);")
        columns = cursor.fetchall()
        existing_columns = [col[1] for col in columns]
        
        print("📋 Current users table columns:")
        for col in existing_columns:
            print(f"  - {col}")
        
        # Define new columns to add
        new_columns = [
            ("full_name", "TEXT"),
            ("avatar_url", "TEXT"),
            ("phone", "TEXT"),
            ("timezone", "TEXT DEFAULT 'UTC'"),
            ("currency", "TEXT DEFAULT 'PKR'"),
            ("language", "TEXT DEFAULT 'en'"),
            ("email_notifications", "BOOLEAN DEFAULT 1"),
            ("expense_notifications", "BOOLEAN DEFAULT 1"),
            ("approval_notifications", "BOOLEAN DEFAULT 1"),
            ("settlement_notifications", "BOOLEAN DEFAULT 1"),
            ("created_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
            ("updated_at", "DATETIME DEFAULT CURRENT_TIMESTAMP")
        ]
        
        # Add missing columns
        added_columns = []
        for col_name, col_type in new_columns:
            if col_name not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE users ADD COLUMN {col_name} {col_type}")
                    added_columns.append(col_name)
                    print(f"✅ Added column: {col_name}")
                except sqlite3.Error as e:
                    print(f"❌ Error adding column {col_name}: {e}")
        
        if added_columns:
            # Update existing users with default values
            cursor.execute("""
                UPDATE users SET 
                    timezone = COALESCE(timezone, 'UTC'),
                    currency = COALESCE(currency, 'PKR'),
                    language = COALESCE(language, 'en'),
                    email_notifications = COALESCE(email_notifications, 1),
                    expense_notifications = COALESCE(expense_notifications, 1),
                    approval_notifications = COALESCE(approval_notifications, 1),
                    settlement_notifications = COALESCE(settlement_notifications, 1),
                    created_at = COALESCE(created_at, datetime('now')),
                    updated_at = COALESCE(updated_at, datetime('now'))
            """)
            
            conn.commit()
            print(f"✅ Successfully added {len(added_columns)} columns to users table")
        else:
            print("ℹ️ All required columns already exist")
        
        # Verify final schema
        cursor.execute("PRAGMA table_info(users);")
        final_columns = cursor.fetchall()
        
        print("\n📋 Final users table schema:")
        for col in final_columns:
            print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error updating database: {e}")
        return False

def test_login_query():
    """Test that the login query works with the updated schema"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        # Test the query that was failing
        cursor.execute("SELECT * FROM users LIMIT 1")
        result = cursor.fetchone()
        
        if result:
            print("✅ Login query test successful")
        else:
            print("ℹ️ No users in database, but query structure is valid")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Login query test failed: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Quick Fix for Database Schema Issue")
    print("=" * 50)
    
    print("\n1. Adding missing columns to users table...")
    if not add_missing_columns():
        sys.exit(1)
    
    print("\n2. Testing login query...")
    if not test_login_query():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Database schema fixed successfully!")
    print("\nYou can now:")
    print("1. Start the FastAPI server: uvicorn app.main:app --reload")
    print("2. Login should work without the 'no such column' error")
    print("3. All profile features should be available")

if __name__ == "__main__":
    main()
