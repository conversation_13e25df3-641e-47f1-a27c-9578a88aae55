#!/usr/bin/env python3
"""
Apply enhanced notification system migration and initialize templates
"""

import os
import sys
import sqlite3
from alembic.config import Config
from alembic import command

def apply_notification_migration():
    """Apply the enhanced notification system migration"""
    try:
        alembic_cfg = Config("alembic.ini")
        
        print("🔄 Applying enhanced notification system migration...")
        
        # Apply migration 006
        command.upgrade(alembic_cfg, "006_add_enhanced_notifications")
        
        print("✅ Enhanced notification system migration applied successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error applying migration: {e}")
        return False

def verify_notification_tables():
    """Verify that notification tables were created correctly"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        # Check if notification tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%notification%';")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        
        expected_tables = [
            'notifications',
            'notification_templates', 
            'user_notification_preferences'
        ]
        
        print("\n📋 Notification tables verification:")
        all_exist = True
        for table in expected_tables:
            if table in table_names:
                print(f"  ✅ {table}")
                
                # Show table schema
                cursor.execute(f"PRAGMA table_info({table});")
                columns = cursor.fetchall()
                print(f"     Columns: {len(columns)} ({', '.join([col[1] for col in columns[:5]])}{'...' if len(columns) > 5 else ''})")
            else:
                print(f"  ❌ {table} - MISSING")
                all_exist = False
        
        conn.close()
        return all_exist
        
    except Exception as e:
        print(f"❌ Error verifying tables: {e}")
        return False

def initialize_notification_templates():
    """Initialize notification templates"""
    try:
        # Add the app directory to Python path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.database import SessionLocal
        from app.services.notification_templates import initialize_notification_templates
        
        print("\n📝 Initializing notification templates...")
        
        db = SessionLocal()
        try:
            initialize_notification_templates(db)
            print("✅ Notification templates initialized successfully!")
            return True
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ Error initializing templates: {e}")
        return False

def test_notification_system():
    """Test basic notification system functionality"""
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.database import SessionLocal
        from app.services.enhanced_notification_service import PersistentNotificationService
        from app.models import User
        
        print("\n🧪 Testing notification system...")
        
        db = SessionLocal()
        try:
            # Get a test user
            user = db.query(User).first()
            if not user:
                print("⚠️ No users found for testing")
                return True
            
            # Test notification service
            service = PersistentNotificationService(db)
            
            # Test creating a notification
            context = {
                'expense_description': 'Test Expense',
                'expense_total': 100.0,
                'creator_name': 'Test User',
                'group_name': 'Test Group',
                'group_id': 1
            }
            
            notification = service.create_notification(
                user.id, 'expense_created', context
            )
            
            if notification:
                print(f"✅ Test notification created: {notification.title}")
                
                # Test marking as read
                success = service.mark_as_read(notification.id, user.id)
                if success:
                    print("✅ Test notification marked as read")
                
                # Test getting summary
                summary = service.get_notification_summary(user.id)
                print(f"✅ Notification summary: {summary.total_count} total, {summary.unread_count} unread")
                
                return True
            else:
                print("⚠️ Test notification was not created (user preferences may be disabled)")
                return True
                
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ Error testing notification system: {e}")
        return False

def show_summary():
    """Show summary of notification system setup"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        print("\n📊 Enhanced Notification System Summary:")
        
        # Count notification tables
        cursor.execute("SELECT COUNT(*) FROM notification_templates")
        template_count = cursor.fetchone()[0]
        print(f"   📝 Notification templates: {template_count}")
        
        cursor.execute("SELECT COUNT(*) FROM notifications")
        notification_count = cursor.fetchone()[0]
        print(f"   📬 Total notifications: {notification_count}")
        
        cursor.execute("SELECT COUNT(*) FROM user_notification_preferences")
        preference_count = cursor.fetchone()[0]
        print(f"   ⚙️ User preferences: {preference_count}")
        
        # Show template categories
        cursor.execute("SELECT DISTINCT category FROM notification_templates")
        categories = cursor.fetchall()
        print(f"   📂 Template categories: {', '.join([cat[0] for cat in categories])}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error showing summary: {e}")

def main():
    """Main function"""
    print("🚀 Enhanced Notification System Setup")
    print("=" * 60)
    
    # Step 1: Apply migration
    if not apply_notification_migration():
        print("❌ Migration failed!")
        sys.exit(1)
    
    # Step 2: Verify tables
    if not verify_notification_tables():
        print("❌ Table verification failed!")
        sys.exit(1)
    
    # Step 3: Initialize templates
    if not initialize_notification_templates():
        print("❌ Template initialization failed!")
        sys.exit(1)
    
    # Step 4: Test system
    if not test_notification_system():
        print("❌ System test failed!")
        sys.exit(1)
    
    # Step 5: Show summary
    show_summary()
    
    print("\n" + "=" * 60)
    print("🎉 Enhanced Notification System Setup Complete!")
    print("✅ Multi-user notifications are now enabled")
    print("✅ Real-time WebSocket notifications active")
    print("✅ Persistent notification storage ready")
    print("✅ Deep-linking and action URLs supported")
    print("✅ User notification preferences available")
    print("\nFeatures enabled:")
    print("  • Expense management notifications")
    print("  • Group activity notifications") 
    print("  • Settlement notifications")
    print("  • Approval workflow notifications")
    print("  • System notifications")
    print("\nAPI endpoints available:")
    print("  • GET /notifications/ - Get user notifications")
    print("  • PUT /notifications/{id}/read - Mark as read")
    print("  • GET /notifications/summary - Get summary")
    print("  • GET /notifications/preferences - Get preferences")

if __name__ == "__main__":
    main()
