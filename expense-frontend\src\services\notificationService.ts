/**
 * Real-time notification service for expense tracking application
 */

import toast from 'react-hot-toast';

// WebSocketMessage interface removed - now handled by WebSocketContext

export interface AppNotification {
  id: string;
  type: 'expense_added' | 'approval_required' | 'settlement_pending' | 'request_approved' | 'request_rejected';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  data?: any;
}

class NotificationService {
  private notifications: AppNotification[] = [];
  private listeners: ((notifications: AppNotification[]) => void)[] = [];
  private refreshCallbacks: (() => void)[] = [];

  // WebSocket-related properties removed - now handled by WebSocketContext

  addNotification(notification: Omit<AppNotification, 'id' | 'timestamp' | 'read'>) {
    const newNotification: AppNotification = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      read: false
    };

    this.notifications.unshift(newNotification);
    
    // Keep only last 50 notifications
    if (this.notifications.length > 50) {
      this.notifications = this.notifications.slice(0, 50);
    }

    // Show toast notification
    this.showToast(newNotification);

    // Notify listeners
    this.notifyListeners();

    // Trigger data refresh
    this.triggerRefresh();
  }

  private showToast(notification: AppNotification) {
    const toastOptions = {
      duration: 4000,
      position: 'top-right' as const,
    };

    switch (notification.type) {
      case 'expense_added':
        toast.success(notification.message, toastOptions);
        break;
      case 'approval_required':
        toast(notification.message, {
          ...toastOptions,
          icon: '⏳',
        });
        break;
      case 'settlement_pending':
        toast(notification.message, {
          ...toastOptions,
          icon: '💰',
        });
        break;
      case 'request_approved':
        toast.success(notification.message, toastOptions);
        break;
      case 'request_rejected':
        toast.error(notification.message, toastOptions);
        break;
      default:
        toast(notification.message, toastOptions);
    }
  }

  getNotifications(): AppNotification[] {
    return [...this.notifications];
  }

  getUnreadCount(): number {
    return this.notifications.filter(n => !n.read).length;
  }

  markAsRead(notificationId: string) {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      this.notifyListeners();
    }
  }

  markAllAsRead() {
    this.notifications.forEach(n => n.read = true);
    this.notifyListeners();
  }

  subscribe(callback: (notifications: AppNotification[]) => void) {
    this.listeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  subscribeToRefresh(callback: () => void) {
    this.refreshCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      this.refreshCallbacks = this.refreshCallbacks.filter(cb => cb !== callback);
    };
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener([...this.notifications]));
  }

  private triggerRefresh() {
    this.refreshCallbacks.forEach(callback => callback());
  }

  // Real notification methods for actual user activities
  notifyExpenseCreated(expenseData: { description: string; amount: number; payer: string; groupName?: string }) {
    this.addNotification({
      type: 'expense_added',
      title: 'Expense Created',
      message: `You created "${expenseData.description}" for $${expenseData.amount.toFixed(2)}${expenseData.groupName ? ` in ${expenseData.groupName}` : ''}`,
      data: expenseData
    });
  }

  notifyExpenseNeedsApproval(expenseData: { description: string; amount: number; payer: string; groupName?: string }) {
    this.addNotification({
      type: 'approval_required',
      title: 'Approval Required',
      message: `${expenseData.payer} added "${expenseData.description}" for $${expenseData.amount.toFixed(2)} - requires your approval`,
      data: expenseData
    });
  }

  notifySettlementReceived(settlementData: { amount: number; payer: string }) {
    this.addNotification({
      type: 'settlement_pending',
      title: 'Settlement Received',
      message: `${settlementData.payer} sent you $${settlementData.amount.toFixed(2)} - please confirm receipt`,
      data: settlementData
    });
  }

  notifySettlementSent(settlementData: { amount: number; recipient: string }) {
    this.addNotification({
      type: 'expense_added',
      title: 'Settlement Sent',
      message: `Settlement of $${settlementData.amount.toFixed(2)} sent to ${settlementData.recipient} for confirmation`,
      data: settlementData
    });
  }

  notifyJoinRequestApproved(requestData: { groupName: string }) {
    this.addNotification({
      type: 'request_approved',
      title: 'Join Request Approved',
      message: `Your request to join "${requestData.groupName}" has been approved`,
      data: requestData
    });
  }

  notifyJoinRequestRejected(requestData: { groupName: string }) {
    this.addNotification({
      type: 'request_rejected',
      title: 'Join Request Rejected',
      message: `Your request to join "${requestData.groupName}" has been rejected`,
      data: requestData
    });
  }

  notifyExpenseApproved(expenseData: { description: string; amount: number; approver: string }) {
    this.addNotification({
      type: 'expense_added',
      title: 'Expense Approved',
      message: `${expenseData.approver} approved "${expenseData.description}" for $${expenseData.amount.toFixed(2)}`,
      data: expenseData
    });
  }

  notifySettlementConfirmed(settlementData: { amount: number; confirmer: string }) {
    this.addNotification({
      type: 'expense_added',
      title: 'Settlement Confirmed',
      message: `${settlementData.confirmer} confirmed receipt of $${settlementData.amount.toFixed(2)}`,
      data: settlementData
    });
  }

  // WebSocket functionality has been moved to WebSocketContext
  // This service now focuses only on local notification management

  // Legacy methods kept for backward compatibility but no longer create connections
  connectWebSocket(token: string) {
    console.log('NotificationService: WebSocket connection is now handled by WebSocketContext');
    // No-op: WebSocketContext handles all WebSocket connections
  }

  disconnectWebSocket() {
    console.log('NotificationService: WebSocket disconnection is now handled by WebSocketContext');
    // No-op: WebSocketContext handles all WebSocket connections
  }

  isWebSocketConnected(): boolean {
    console.log('NotificationService: WebSocket status is now available through WebSocketContext');
    return false; // Always return false since this service no longer manages connections
  }
}

// Create singleton instance
export const notificationService = new NotificationService();

export default notificationService;
