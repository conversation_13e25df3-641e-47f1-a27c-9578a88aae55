#!/usr/bin/env python3
"""
Fix script to add timestamp columns to users table
SQLite has limitations with default values, so we handle this separately
"""

import sqlite3
import os
import sys

def fix_timestamp_columns():
    """Add timestamp columns with proper SQLite handling"""
    db_path = "./dev.db"
    
    if not os.path.exists(db_path):
        print("❌ Database file 'dev.db' does not exist!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current schema
        cursor.execute("PRAGMA table_info(users);")
        columns = cursor.fetchall()
        existing_columns = [col[1] for col in columns]
        
        print("📋 Current users table columns:")
        for col in existing_columns:
            print(f"  - {col}")
        
        # Add timestamp columns if they don't exist
        if 'created_at' not in existing_columns:
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN created_at DATETIME")
                print("✅ Added column: created_at")
            except sqlite3.Error as e:
                print(f"❌ Error adding created_at: {e}")
        
        if 'updated_at' not in existing_columns:
            try:
                cursor.execute("ALTER TABLE users ADD COLUMN updated_at DATETIME")
                print("✅ Added column: updated_at")
            except sqlite3.Error as e:
                print(f"❌ Error adding updated_at: {e}")
        
        # Set default values for all NULL timestamp fields
        cursor.execute("""
            UPDATE users SET 
                created_at = datetime('now')
            WHERE created_at IS NULL
        """)
        
        cursor.execute("""
            UPDATE users SET 
                updated_at = datetime('now')
            WHERE updated_at IS NULL
        """)
        
        conn.commit()
        print("✅ Updated timestamp columns with default values")
        
        # Verify final schema
        cursor.execute("PRAGMA table_info(users);")
        final_columns = cursor.fetchall()
        
        print("\n📋 Final users table schema:")
        for col in final_columns:
            print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        
        # Check that we have all required columns
        required_columns = [
            'id', 'email', 'hashed_password', 'groq_api_key',
            'full_name', 'avatar_url', 'phone', 'timezone', 'currency', 'language',
            'email_notifications', 'expense_notifications', 'approval_notifications', 
            'settlement_notifications', 'created_at', 'updated_at'
        ]
        
        final_column_names = [col[1] for col in final_columns]
        missing_columns = [col for col in required_columns if col not in final_column_names]
        
        if missing_columns:
            print(f"\n❌ Still missing columns: {', '.join(missing_columns)}")
            return False
        else:
            print(f"\n✅ All {len(required_columns)} required columns are present!")
            return True
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error updating database: {e}")
        return False

def test_user_query():
    """Test that we can query the users table with all columns"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        # Test a SELECT * query (this is what SQLAlchemy does)
        cursor.execute("SELECT * FROM users LIMIT 1")
        result = cursor.fetchone()
        
        # Get column names
        cursor.execute("PRAGMA table_info(users);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"\n🔍 Query test successful! Table has {len(column_names)} columns:")
        for i, col_name in enumerate(column_names):
            value = result[i] if result and i < len(result) else "NULL"
            print(f"  {i+1:2d}. {col_name}: {value}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Query test failed: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Fix Timestamp Columns")
    print("=" * 50)
    
    if not fix_timestamp_columns():
        sys.exit(1)
    
    if not test_user_query():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Database schema is now complete!")
    print("\nThe users table now has all required columns.")
    print("You can start the FastAPI server and login should work.")

if __name__ == "__main__":
    main()
