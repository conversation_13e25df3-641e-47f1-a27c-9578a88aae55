"""
Notification templates initialization and management
"""

from sqlalchemy.orm import Session
from ..models import NotificationTemplate, NotificationCategory, NotificationPriority


def initialize_notification_templates(db: Session):
    """Initialize default notification templates"""
    
    templates = [
        # Expense Management Templates
        {
            'template_key': 'expense_created',
            'category': NotificationCategory.EXPENSE,
            'title_template': 'New Expense Added',
            'message_template': '{creator_name} added "{expense_description}" for PKR {expense_total:.2f} in {group_name}',
            'action_text_template': 'Review Expense',
            'action_url_template': '/approvals',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'template_key': 'expense_approved',
            'category': NotificationCategory.EXPENSE,
            'title_template': 'Expense Approved',
            'message_template': '{approver_name} approved "{expense_description}" in {group_name}',
            'action_text_template': 'View Expenses',
            'action_url_template': '/expenses/history',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'template_key': 'expense_rejected',
            'category': NotificationCategory.EXPENSE,
            'title_template': 'Expense Rejected',
            'message_template': '{approver_name} rejected "{expense_description}" in {group_name}',
            'action_text_template': 'View Details',
            'action_url_template': '/approvals',
            'priority': NotificationPriority.HIGH
        },
        {
            'template_key': 'expense_updated',
            'category': NotificationCategory.EXPENSE,
            'title_template': 'Expense Updated',
            'message_template': '{updater_name} updated "{expense_description}" in {group_name}',
            'action_text_template': 'View Changes',
            'action_url_template': '/expenses/history',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'template_key': 'expense_deleted',
            'category': NotificationCategory.EXPENSE,
            'title_template': 'Expense Deleted',
            'message_template': '{deleter_name} deleted "{expense_description}" from {group_name}',
            'action_text_template': 'View Expenses',
            'action_url_template': '/expenses/history',
            'priority': NotificationPriority.HIGH
        },
        {
            'template_key': 'expense_needs_approval',
            'category': NotificationCategory.APPROVAL,
            'title_template': 'Approval Required',
            'message_template': '{creator_name} submitted "{expense_description}" for approval in {group_name}',
            'action_text_template': 'Review & Approve',
            'action_url_template': '/approvals',
            'priority': NotificationPriority.HIGH
        },
        {
            'template_key': 'expense_approval_reminder',
            'category': NotificationCategory.APPROVAL,
            'title_template': 'Approval Reminder',
            'message_template': 'Reminder: "{expense_description}" is still pending approval in {group_name}',
            'action_text_template': 'Review Now',
            'action_url_template': '/approvals',
            'priority': NotificationPriority.HIGH
        },
        
        # Group Management Templates
        {
            'template_key': 'member_joined',
            'category': NotificationCategory.GROUP,
            'title_template': 'New Member Joined',
            'message_template': '{member_name} joined {group_name}',
            'action_text_template': 'View Group',
            'action_url_template': '/group-management/groups/{group_id}/details',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'template_key': 'member_left',
            'category': NotificationCategory.GROUP,
            'title_template': 'Member Left Group',
            'message_template': '{member_name} left {group_name}',
            'action_text_template': 'View Group',
            'action_url_template': '/group-management/groups/{group_id}/details',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'template_key': 'group_settings_changed',
            'category': NotificationCategory.GROUP,
            'title_template': 'Group Settings Updated',
            'message_template': '{changer_name} updated settings for {group_name}',
            'action_text_template': 'View Changes',
            'action_url_template': '/group-management/groups/{group_id}/details',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'template_key': 'group_ownership_transferred',
            'category': NotificationCategory.GROUP,
            'title_template': 'Group Ownership Transferred',
            'message_template': 'Ownership of {group_name} has been transferred to {new_owner_name}',
            'action_text_template': 'View Group',
            'action_url_template': '/group-management/groups/{group_id}/details',
            'priority': NotificationPriority.HIGH
        },
        
        # Join Request Templates
        {
            'template_key': 'join_request_received',
            'category': NotificationCategory.GROUP,
            'title_template': 'Join Request Received',
            'message_template': '{requester_name} wants to join {group_name}',
            'action_text_template': 'Review Request',
            'action_url_template': '/group-management/groups/{group_id}/details',
            'priority': NotificationPriority.HIGH
        },
        {
            'template_key': 'join_request_approved',
            'category': NotificationCategory.GROUP,
            'title_template': 'Join Request Approved',
            'message_template': '{processor_name} approved your request to join "{group_name}"',
            'action_text_template': 'View Groups',
            'action_url_template': '/group-management',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'template_key': 'join_request_rejected',
            'category': NotificationCategory.GROUP,
            'title_template': 'Join Request Rejected',
            'message_template': '{processor_name} rejected your request to join "{group_name}"',
            'action_text_template': None,
            'action_url_template': None,
            'priority': NotificationPriority.HIGH
        },
        
        # Settlement Templates
        {
            'template_key': 'settlement_initiated',
            'category': NotificationCategory.SETTLEMENT,
            'title_template': 'Settlement Initiated',
            'message_template': '{sender_name} initiated a settlement of PKR {amount:.2f}',
            'action_text_template': 'View Settlements',
            'action_url_template': '/settlements',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'template_key': 'settlement_received',
            'category': NotificationCategory.SETTLEMENT,
            'title_template': 'Settlement Received',
            'message_template': '{sender_name} sent you PKR {amount:.2f}',
            'action_text_template': 'Review Settlement',
            'action_url_template': '/settlements',
            'priority': NotificationPriority.HIGH
        },
        {
            'template_key': 'settlement_accepted',
            'category': NotificationCategory.SETTLEMENT,
            'title_template': 'Settlement Accepted',
            'message_template': '{accepter_name} accepted your settlement of PKR {amount:.2f}',
            'action_text_template': 'View Details',
            'action_url_template': '/settlements',
            'priority': NotificationPriority.MEDIUM
        },
        {
            'template_key': 'settlement_disputed',
            'category': NotificationCategory.SETTLEMENT,
            'title_template': 'Settlement Disputed',
            'message_template': '{disputer_name} disputed a settlement of PKR {amount:.2f}',
            'action_text_template': 'Resolve Dispute',
            'action_url_template': '/settlements',
            'priority': NotificationPriority.URGENT
        },
        {
            'template_key': 'settlement_completed',
            'category': NotificationCategory.SETTLEMENT,
            'title_template': 'Settlement Completed',
            'message_template': 'Settlement of PKR {amount:.2f} between {sender_name} and {accepter_name} completed',
            'action_text_template': 'View Settlements',
            'action_url_template': '/settlements',
            'priority': NotificationPriority.MEDIUM
        },
        
        # System Templates
        {
            'template_key': 'system_maintenance',
            'category': NotificationCategory.SYSTEM,
            'title_template': 'System Maintenance',
            'message_template': 'System maintenance scheduled for {maintenance_time}. Expected downtime: {duration}',
            'action_text_template': 'Learn More',
            'action_url_template': '/system/maintenance',
            'priority': NotificationPriority.HIGH
        },
        {
            'template_key': 'system_update',
            'category': NotificationCategory.SYSTEM,
            'title_template': 'System Update',
            'message_template': 'New features and improvements are now available!',
            'action_text_template': 'See What\'s New',
            'action_url_template': '/system/updates',
            'priority': NotificationPriority.LOW
        }
    ]
    
    for template_data in templates:
        # Check if template already exists
        existing = db.query(NotificationTemplate).filter(
            NotificationTemplate.template_key == template_data['template_key']
        ).first()
        
        if not existing:
            template = NotificationTemplate(**template_data)
            db.add(template)
    
    db.commit()
    print(f"Initialized {len(templates)} notification templates")


def get_template_by_key(db: Session, template_key: str) -> NotificationTemplate:
    """Get notification template by key"""
    return db.query(NotificationTemplate).filter(
        NotificationTemplate.template_key == template_key
    ).first()


def update_template(db: Session, template_key: str, **updates) -> NotificationTemplate:
    """Update notification template"""
    template = get_template_by_key(db, template_key)
    if not template:
        return None
    
    for key, value in updates.items():
        if hasattr(template, key):
            setattr(template, key, value)
    
    db.commit()
    db.refresh(template)
    return template


def deactivate_template(db: Session, template_key: str) -> bool:
    """Deactivate notification template"""
    template = get_template_by_key(db, template_key)
    if not template:
        return False
    
    template.is_active = False
    db.commit()
    return True
