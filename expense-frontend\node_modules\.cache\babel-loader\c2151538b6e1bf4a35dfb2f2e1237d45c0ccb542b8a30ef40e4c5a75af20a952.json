{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\components\\\\Debug\\\\WebSocketDebug.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useWebSocketContext } from '../../contexts/WebSocketContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Wifi, WifiOff, AlertCircle, CheckCircle, RefreshCw, Eye, EyeOff } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WebSocketDebug = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    isConnected,\n    isConnecting,\n    connectionError,\n    reconnectAttempts,\n    connect,\n    disconnect\n  } = useWebSocketContext();\n  const [showDetails, setShowDetails] = useState(false);\n  const [connectionLogs, setConnectionLogs] = useState([]);\n\n  // Monitor connection status changes\n  useEffect(() => {\n    const timestamp = new Date().toLocaleTimeString();\n    if (isConnected) {\n      setConnectionLogs(prev => [`${timestamp}: Connected successfully`, ...prev.slice(0, 9)]);\n    } else if (isConnecting) {\n      setConnectionLogs(prev => [`${timestamp}: Connecting...`, ...prev.slice(0, 9)]);\n    } else if (connectionError) {\n      setConnectionLogs(prev => [`${timestamp}: Error - ${connectionError}`, ...prev.slice(0, 9)]);\n    }\n  }, [isConnected, isConnecting, connectionError]);\n  const getConnectionStatus = () => {\n    if (isConnected) return {\n      text: 'Connected',\n      color: 'text-green-600',\n      icon: CheckCircle\n    };\n    if (isConnecting) return {\n      text: 'Connecting...',\n      color: 'text-yellow-600',\n      icon: RefreshCw\n    };\n    if (connectionError) return {\n      text: 'Error',\n      color: 'text-red-600',\n      icon: AlertCircle\n    };\n    return {\n      text: 'Disconnected',\n      color: 'text-gray-600',\n      icon: WifiOff\n    };\n  };\n  const status = getConnectionStatus();\n  const StatusIcon = status.icon;\n  const checkBackendHealth = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/ws/health');\n      const data = await response.json();\n      setConnectionLogs(prev => [`${new Date().toLocaleTimeString()}: Backend health - ${data.status} (${data.connected_users} users)`, ...prev.slice(0, 9)]);\n    } catch (error) {\n      setConnectionLogs(prev => [`${new Date().toLocaleTimeString()}: Backend health check failed - ${error}`, ...prev.slice(0, 9)]);\n    }\n  };\n  const testWebSocketUrl = () => {\n    const token = localStorage.getItem('token');\n    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n    const wsHost = process.env.REACT_APP_WS_URL || 'localhost:8000';\n    const wsUrl = `${wsProtocol}//${wsHost}/ws/notifications?token=${token ? 'PRESENT' : 'MISSING'}`;\n    setConnectionLogs(prev => [`${new Date().toLocaleTimeString()}: WebSocket URL - ${wsUrl}`, ...prev.slice(0, 9)]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 max-w-sm z-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [isConnected ? /*#__PURE__*/_jsxDEV(Wifi, {\n          className: \"w-5 h-5 text-green-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(WifiOff, {\n          className: \"w-5 h-5 text-red-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-gray-900 dark:text-white\",\n          children: \"WebSocket\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowDetails(!showDetails),\n        className: \"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\",\n        children: showDetails ? /*#__PURE__*/_jsxDEV(EyeOff, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 26\n        }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 59\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(StatusIcon, {\n        className: `w-4 h-4 ${status.color}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: `text-sm font-medium ${status.color}`,\n        children: status.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), reconnectAttempts > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-xs text-yellow-600\",\n        children: [\"(Attempt \", reconnectAttempts, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-xs text-gray-600 dark:text-gray-400 space-y-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"User: \", user ? '✅ Authenticated' : '❌ Not authenticated']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [\"Token: \", localStorage.getItem('token') ? '✅ Present' : '❌ Missing']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), connectionError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-red-600 dark:text-red-400\",\n        children: [\"Error: \", connectionError]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), showDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-2 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: isConnected ? disconnect : connect,\n          className: `px-3 py-1 rounded text-xs font-medium ${isConnected ? 'bg-red-100 text-red-700 hover:bg-red-200' : 'bg-green-100 text-green-700 hover:bg-green-200'}`,\n          children: isConnected ? 'Disconnect' : 'Connect'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: checkBackendHealth,\n          className: \"px-3 py-1 rounded text-xs font-medium bg-blue-100 text-blue-700 hover:bg-blue-200\",\n          children: \"Health Check\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: testWebSocketUrl,\n          className: \"px-3 py-1 rounded text-xs font-medium bg-purple-100 text-purple-700 hover:bg-purple-200\",\n          children: \"Test URL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 dark:bg-gray-700 rounded p-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs font-medium text-gray-700 dark:text-gray-300 mb-2\",\n          children: \"Connection Logs:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-1 max-h-32 overflow-y-auto\",\n          children: connectionLogs.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 dark:text-gray-400\",\n            children: \"No logs yet...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 17\n          }, this) : connectionLogs.map((log, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-600 dark:text-gray-400 font-mono\",\n            children: log\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3 text-xs text-gray-500 dark:text-gray-400\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Protocol: \", window.location.protocol === 'https:' ? 'WSS' : 'WS']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Host: \", process.env.REACT_APP_WS_URL || 'localhost:8000']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"Environment: \", process.env.NODE_ENV]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(WebSocketDebug, \"XE9F6klIdkFoDdLc3bsY+L5BPP4=\", false, function () {\n  return [useAuth, useWebSocketContext];\n});\n_c = WebSocketDebug;\nexport default WebSocketDebug;\nvar _c;\n$RefreshReg$(_c, \"WebSocketDebug\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useWebSocketContext", "useAuth", "Wifi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertCircle", "CheckCircle", "RefreshCw", "Eye", "Eye<PERSON>ff", "jsxDEV", "_jsxDEV", "WebSocketDebug", "_s", "user", "isConnected", "isConnecting", "connectionError", "reconnectAttempts", "connect", "disconnect", "showDetails", "setShowDetails", "connectionLogs", "setConnectionLogs", "timestamp", "Date", "toLocaleTimeString", "prev", "slice", "getConnectionStatus", "text", "color", "icon", "status", "StatusIcon", "checkBackendHealth", "response", "fetch", "data", "json", "connected_users", "error", "testWebSocketUrl", "token", "localStorage", "getItem", "wsProtocol", "window", "location", "protocol", "wsHost", "process", "env", "REACT_APP_WS_URL", "wsUrl", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "length", "map", "log", "index", "NODE_ENV", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/components/Debug/WebSocketDebug.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useWebSocketContext } from '../../contexts/WebSocketContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Wifi, WifiOff, AlertCircle, CheckCircle, RefreshCw, Eye, EyeOff } from 'lucide-react';\n\nconst WebSocketDebug: React.FC = () => {\n  const { user } = useAuth();\n  const {\n    isConnected,\n    isConnecting,\n    connectionError,\n    reconnectAttempts,\n    connect,\n    disconnect,\n  } = useWebSocketContext();\n\n  const [showDetails, setShowDetails] = useState(false);\n  const [connectionLogs, setConnectionLogs] = useState<string[]>([]);\n\n  // Monitor connection status changes\n  useEffect(() => {\n    const timestamp = new Date().toLocaleTimeString();\n    \n    if (isConnected) {\n      setConnectionLogs(prev => [`${timestamp}: Connected successfully`, ...prev.slice(0, 9)]);\n    } else if (isConnecting) {\n      setConnectionLogs(prev => [`${timestamp}: Connecting...`, ...prev.slice(0, 9)]);\n    } else if (connectionError) {\n      setConnectionLogs(prev => [`${timestamp}: Error - ${connectionError}`, ...prev.slice(0, 9)]);\n    }\n  }, [isConnected, isConnecting, connectionError]);\n\n  const getConnectionStatus = () => {\n    if (isConnected) return { text: 'Connected', color: 'text-green-600', icon: CheckCircle };\n    if (isConnecting) return { text: 'Connecting...', color: 'text-yellow-600', icon: RefreshCw };\n    if (connectionError) return { text: 'Error', color: 'text-red-600', icon: AlertCircle };\n    return { text: 'Disconnected', color: 'text-gray-600', icon: WifiOff };\n  };\n\n  const status = getConnectionStatus();\n  const StatusIcon = status.icon;\n\n  const checkBackendHealth = async () => {\n    try {\n      const response = await fetch('http://localhost:8000/ws/health');\n      const data = await response.json();\n      setConnectionLogs(prev => [\n        `${new Date().toLocaleTimeString()}: Backend health - ${data.status} (${data.connected_users} users)`,\n        ...prev.slice(0, 9)\n      ]);\n    } catch (error) {\n      setConnectionLogs(prev => [\n        `${new Date().toLocaleTimeString()}: Backend health check failed - ${error}`,\n        ...prev.slice(0, 9)\n      ]);\n    }\n  };\n\n  const testWebSocketUrl = () => {\n    const token = localStorage.getItem('token');\n    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n    const wsHost = process.env.REACT_APP_WS_URL || 'localhost:8000';\n    const wsUrl = `${wsProtocol}//${wsHost}/ws/notifications?token=${token ? 'PRESENT' : 'MISSING'}`;\n    \n    setConnectionLogs(prev => [\n      `${new Date().toLocaleTimeString()}: WebSocket URL - ${wsUrl}`,\n      ...prev.slice(0, 9)\n    ]);\n  };\n\n  return (\n    <div className=\"fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 max-w-sm z-50\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-3\">\n        <div className=\"flex items-center space-x-2\">\n          {isConnected ? (\n            <Wifi className=\"w-5 h-5 text-green-600\" />\n          ) : (\n            <WifiOff className=\"w-5 h-5 text-red-600\" />\n          )}\n          <span className=\"font-medium text-gray-900 dark:text-white\">WebSocket</span>\n        </div>\n        <button\n          onClick={() => setShowDetails(!showDetails)}\n          className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\n        >\n          {showDetails ? <EyeOff className=\"w-4 h-4\" /> : <Eye className=\"w-4 h-4\" />}\n        </button>\n      </div>\n\n      {/* Status */}\n      <div className=\"flex items-center space-x-2 mb-3\">\n        <StatusIcon className={`w-4 h-4 ${status.color}`} />\n        <span className={`text-sm font-medium ${status.color}`}>\n          {status.text}\n        </span>\n        {reconnectAttempts > 0 && (\n          <span className=\"text-xs text-yellow-600\">\n            (Attempt {reconnectAttempts})\n          </span>\n        )}\n      </div>\n\n      {/* Quick Info */}\n      <div className=\"text-xs text-gray-600 dark:text-gray-400 space-y-1\">\n        <div>User: {user ? '✅ Authenticated' : '❌ Not authenticated'}</div>\n        <div>Token: {localStorage.getItem('token') ? '✅ Present' : '❌ Missing'}</div>\n        {connectionError && (\n          <div className=\"text-red-600 dark:text-red-400\">\n            Error: {connectionError}\n          </div>\n        )}\n      </div>\n\n      {/* Detailed View */}\n      {showDetails && (\n        <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n          {/* Controls */}\n          <div className=\"flex space-x-2 mb-3\">\n            <button\n              onClick={isConnected ? disconnect : connect}\n              className={`px-3 py-1 rounded text-xs font-medium ${\n                isConnected\n                  ? 'bg-red-100 text-red-700 hover:bg-red-200'\n                  : 'bg-green-100 text-green-700 hover:bg-green-200'\n              }`}\n            >\n              {isConnected ? 'Disconnect' : 'Connect'}\n            </button>\n            <button\n              onClick={checkBackendHealth}\n              className=\"px-3 py-1 rounded text-xs font-medium bg-blue-100 text-blue-700 hover:bg-blue-200\"\n            >\n              Health Check\n            </button>\n            <button\n              onClick={testWebSocketUrl}\n              className=\"px-3 py-1 rounded text-xs font-medium bg-purple-100 text-purple-700 hover:bg-purple-200\"\n            >\n              Test URL\n            </button>\n          </div>\n\n          {/* Connection Logs */}\n          <div className=\"bg-gray-50 dark:bg-gray-700 rounded p-2\">\n            <div className=\"text-xs font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Connection Logs:\n            </div>\n            <div className=\"space-y-1 max-h-32 overflow-y-auto\">\n              {connectionLogs.length === 0 ? (\n                <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                  No logs yet...\n                </div>\n              ) : (\n                connectionLogs.map((log, index) => (\n                  <div\n                    key={index}\n                    className=\"text-xs text-gray-600 dark:text-gray-400 font-mono\"\n                  >\n                    {log}\n                  </div>\n                ))\n              )}\n            </div>\n          </div>\n\n          {/* Environment Info */}\n          <div className=\"mt-3 text-xs text-gray-500 dark:text-gray-400\">\n            <div>Protocol: {window.location.protocol === 'https:' ? 'WSS' : 'WS'}</div>\n            <div>Host: {process.env.REACT_APP_WS_URL || 'localhost:8000'}</div>\n            <div>Environment: {process.env.NODE_ENV}</div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default WebSocketDebug;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAEC,GAAG,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/F,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM;IAAEC;EAAK,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC1B,MAAM;IACJa,WAAW;IACXC,YAAY;IACZC,eAAe;IACfC,iBAAiB;IACjBC,OAAO;IACPC;EACF,CAAC,GAAGnB,mBAAmB,CAAC,CAAC;EAEzB,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAW,EAAE,CAAC;;EAElE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMyB,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;IAEjD,IAAIZ,WAAW,EAAE;MACfS,iBAAiB,CAACI,IAAI,IAAI,CAAC,GAAGH,SAAS,0BAA0B,EAAE,GAAGG,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1F,CAAC,MAAM,IAAIb,YAAY,EAAE;MACvBQ,iBAAiB,CAACI,IAAI,IAAI,CAAC,GAAGH,SAAS,iBAAiB,EAAE,GAAGG,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC,MAAM,IAAIZ,eAAe,EAAE;MAC1BO,iBAAiB,CAACI,IAAI,IAAI,CAAC,GAAGH,SAAS,aAAaR,eAAe,EAAE,EAAE,GAAGW,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9F;EACF,CAAC,EAAE,CAACd,WAAW,EAAEC,YAAY,EAAEC,eAAe,CAAC,CAAC;EAEhD,MAAMa,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIf,WAAW,EAAE,OAAO;MAAEgB,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,IAAI,EAAE3B;IAAY,CAAC;IACzF,IAAIU,YAAY,EAAE,OAAO;MAAEe,IAAI,EAAE,eAAe;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,IAAI,EAAE1B;IAAU,CAAC;IAC7F,IAAIU,eAAe,EAAE,OAAO;MAAEc,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAE,cAAc;MAAEC,IAAI,EAAE5B;IAAY,CAAC;IACvF,OAAO;MAAE0B,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE7B;IAAQ,CAAC;EACxE,CAAC;EAED,MAAM8B,MAAM,GAAGJ,mBAAmB,CAAC,CAAC;EACpC,MAAMK,UAAU,GAAGD,MAAM,CAACD,IAAI;EAE9B,MAAMG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,iCAAiC,CAAC;MAC/D,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClChB,iBAAiB,CAACI,IAAI,IAAI,CACxB,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,sBAAsBY,IAAI,CAACL,MAAM,KAAKK,IAAI,CAACE,eAAe,SAAS,EACrG,GAAGb,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACpB,CAAC;IACJ,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdlB,iBAAiB,CAACI,IAAI,IAAI,CACxB,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,mCAAmCe,KAAK,EAAE,EAC5E,GAAGd,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACpB,CAAC;IACJ;EACF,CAAC;EAED,MAAMc,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,UAAU,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;IACzE,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB,IAAI,gBAAgB;IAC/D,MAAMC,KAAK,GAAG,GAAGR,UAAU,KAAKI,MAAM,2BAA2BP,KAAK,GAAG,SAAS,GAAG,SAAS,EAAE;IAEhGpB,iBAAiB,CAACI,IAAI,IAAI,CACxB,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,qBAAqB4B,KAAK,EAAE,EAC9D,GAAG3B,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACpB,CAAC;EACJ,CAAC;EAED,oBACElB,OAAA;IAAK6C,SAAS,EAAC,qIAAqI;IAAAC,QAAA,gBAElJ9C,OAAA;MAAK6C,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrD9C,OAAA;QAAK6C,SAAS,EAAC,6BAA6B;QAAAC,QAAA,GACzC1C,WAAW,gBACVJ,OAAA,CAACR,IAAI;UAACqD,SAAS,EAAC;QAAwB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE3ClD,OAAA,CAACP,OAAO;UAACoD,SAAS,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC5C,eACDlD,OAAA;UAAM6C,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eACNlD,OAAA;QACEmD,OAAO,EAAEA,CAAA,KAAMxC,cAAc,CAAC,CAACD,WAAW,CAAE;QAC5CmC,SAAS,EAAC,+EAA+E;QAAAC,QAAA,EAExFpC,WAAW,gBAAGV,OAAA,CAACF,MAAM;UAAC+C,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACH,GAAG;UAACgD,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNlD,OAAA;MAAK6C,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/C9C,OAAA,CAACwB,UAAU;QAACqB,SAAS,EAAE,WAAWtB,MAAM,CAACF,KAAK;MAAG;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpDlD,OAAA;QAAM6C,SAAS,EAAE,uBAAuBtB,MAAM,CAACF,KAAK,EAAG;QAAAyB,QAAA,EACpDvB,MAAM,CAACH;MAAI;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,EACN3C,iBAAiB,GAAG,CAAC,iBACpBP,OAAA;QAAM6C,SAAS,EAAC,yBAAyB;QAAAC,QAAA,GAAC,WAC/B,EAACvC,iBAAiB,EAAC,GAC9B;MAAA;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNlD,OAAA;MAAK6C,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBACjE9C,OAAA;QAAA8C,QAAA,GAAK,QAAM,EAAC3C,IAAI,GAAG,iBAAiB,GAAG,qBAAqB;MAAA;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnElD,OAAA;QAAA8C,QAAA,GAAK,SAAO,EAACZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,WAAW,GAAG,WAAW;MAAA;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAC5E5C,eAAe,iBACdN,OAAA;QAAK6C,SAAS,EAAC,gCAAgC;QAAAC,QAAA,GAAC,SACvC,EAACxC,eAAe;MAAA;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLxC,WAAW,iBACVV,OAAA;MAAK6C,SAAS,EAAC,yDAAyD;MAAAC,QAAA,gBAEtE9C,OAAA;QAAK6C,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC9C,OAAA;UACEmD,OAAO,EAAE/C,WAAW,GAAGK,UAAU,GAAGD,OAAQ;UAC5CqC,SAAS,EAAE,yCACTzC,WAAW,GACP,0CAA0C,GAC1C,gDAAgD,EACnD;UAAA0C,QAAA,EAEF1C,WAAW,GAAG,YAAY,GAAG;QAAS;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACTlD,OAAA;UACEmD,OAAO,EAAE1B,kBAAmB;UAC5BoB,SAAS,EAAC,mFAAmF;UAAAC,QAAA,EAC9F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlD,OAAA;UACEmD,OAAO,EAAEnB,gBAAiB;UAC1Ba,SAAS,EAAC,yFAAyF;UAAAC,QAAA,EACpG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtD9C,OAAA;UAAK6C,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAE3E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNlD,OAAA;UAAK6C,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAChDlC,cAAc,CAACwC,MAAM,KAAK,CAAC,gBAC1BpD,OAAA;YAAK6C,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAENtC,cAAc,CAACyC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAC5BvD,OAAA;YAEE6C,SAAS,EAAC,oDAAoD;YAAAC,QAAA,EAE7DQ;UAAG,GAHCC,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIP,CACN;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC5D9C,OAAA;UAAA8C,QAAA,GAAK,YAAU,EAACT,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,GAAG,KAAK,GAAG,IAAI;QAAA;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3ElD,OAAA;UAAA8C,QAAA,GAAK,QAAM,EAACL,OAAO,CAACC,GAAG,CAACC,gBAAgB,IAAI,gBAAgB;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnElD,OAAA;UAAA8C,QAAA,GAAK,eAAa,EAACL,OAAO,CAACC,GAAG,CAACc,QAAQ;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChD,EAAA,CA3KID,cAAwB;EAAA,QACXV,OAAO,EAQpBD,mBAAmB;AAAA;AAAAmE,EAAA,GATnBxD,cAAwB;AA6K9B,eAAeA,cAAc;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}