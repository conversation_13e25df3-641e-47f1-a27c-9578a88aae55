import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout/Layout';
import {
  DollarSign,
  Users,
  Receipt,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
  Plus
} from 'lucide-react';
import { Balance, Group } from '../types';
import { expensesAPI, groupsAPI } from '../services/api';
import { formatCurrency, toNumber } from '../utils/formatters';
import { useAutoRefresh } from '../hooks/useAutoRefresh';
import { dashboardCircuitBreaker, withCircuitBreaker } from '../utils/circuitBreaker';
import toast from 'react-hot-toast';

interface DashboardStats {
  totalOwed: number;
  totalOwing: number;
  totalGroups: number;
  recentExpenses: number;
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<DashboardStats>({
    totalOwed: 0,
    totalOwing: 0,
    totalGroups: 0,
    recentExpenses: 0,
  });
  const [balances, setBalances] = useState<Balance[]>([]);
  const [groups, setGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastLoadAttempt, setLastLoadAttempt] = useState<number>(0);

  const loadDashboardData = async () => {
    const now = Date.now();

    // Prevent rapid successive calls
    if (now - lastLoadAttempt < 5000) { // 5 second cooldown
      console.log('Dashboard data load skipped - too soon since last attempt');
      return;
    }

    setLastLoadAttempt(now);

    try {
      const result = await withCircuitBreaker(
        async () => {
          const [balancesRes, groupsRes] = await Promise.all([
            expensesAPI.getBalances(),
            groupsAPI.getMyGroups(),
          ]);
          return { balancesRes, groupsRes };
        },
        dashboardCircuitBreaker,
        'Dashboard Data Load'
      );

      const { balancesRes, groupsRes } = result;
      const balanceData = balancesRes.data;
      const groupData = groupsRes.data;

      setBalances(balanceData);
      setGroups(groupData);

      // Calculate stats
      const totalOwed = balanceData
        .filter(b => toNumber(b.amount) > 0)
        .reduce((sum, b) => sum + toNumber(b.amount), 0);

      const totalOwing = Math.abs(balanceData
        .filter(b => toNumber(b.amount) < 0)
        .reduce((sum, b) => sum + toNumber(b.amount), 0));

      setStats({
        totalOwed,
        totalOwing,
        totalGroups: groupData.length,
        recentExpenses: balanceData.length,
      });

      console.log('Dashboard data loaded successfully');
    } catch (error: any) {
      console.error('Dashboard data load failed:', error);

      // Only show toast if it's not a circuit breaker error
      if (!error.message?.includes('Circuit breaker is OPEN')) {
        toast.error('Failed to load dashboard data');
      } else {
        console.log('Dashboard load blocked by circuit breaker');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  // Auto-refresh data when notifications indicate changes
  useAutoRefresh(loadDashboardData, []);

  const StatCard: React.FC<{
    title: string;
    value: string;
    icon: React.ReactNode;
    trend?: string;
    trendUp?: boolean;
    color: string;
  }> = ({ title, value, icon, trend, trendUp, color }) => (
    <div className="card">
      <div className="card-content">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {trend && (
              <div className={`flex items-center mt-1 text-sm ${
                trendUp ? 'text-green-600' : 'text-red-600'
              }`}>
                {trendUp ? (
                  <ArrowUpRight className="w-4 h-4 mr-1" />
                ) : (
                  <ArrowDownRight className="w-4 h-4 mr-1" />
                )}
                {trend}
              </div>
            )}
          </div>
          <div className={`p-3 rounded-lg ${color}`}>
            {icon}
          </div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <Layout title="Dashboard" subtitle="Overview of your expenses and balances">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Dashboard" subtitle="Overview of your expenses and balances">
      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Owed to You"
            value={formatCurrency(stats.totalOwed)}
            icon={<TrendingUp className="w-6 h-6 text-green-600" />}
            color="bg-green-100"
          />
          <StatCard
            title="Total You Owe"
            value={formatCurrency(stats.totalOwing)}
            icon={<DollarSign className="w-6 h-6 text-red-600" />}
            color="bg-red-100"
          />
          <StatCard
            title="Active Groups"
            value={stats.totalGroups.toString()}
            icon={<Users className="w-6 h-6 text-blue-600" />}
            color="bg-blue-100"
          />
          <StatCard
            title="Recent Balances"
            value={stats.recentExpenses.toString()}
            icon={<Receipt className="w-6 h-6 text-purple-600" />}
            color="bg-purple-100"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Balances */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">Recent Balances</h3>
            </div>
            <div className="card-content">
              {balances.length === 0 ? (
                <div className="text-center py-8">
                  <Receipt className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No balances yet</p>
                  <p className="text-sm text-gray-400">Start by creating an expense</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {balances.slice(0, 5).map((balance, index) => (
                    <div key={index} className="flex items-center justify-between py-2">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-gray-600">
                            {balance.email.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900">
                            {balance.email}
                          </p>
                        </div>
                      </div>
                      <div className={`text-sm font-medium ${
                        toNumber(balance.amount) > 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {toNumber(balance.amount) > 0 ? '+' : ''}{formatCurrency(balance.amount)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Groups */}
          <div className="card">
            <div className="card-header flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Your Groups</h3>
              <button
                className="btn-ghost"
                onClick={() => navigate('/groups')}
              >
                <Plus className="w-4 h-4 mr-1" />
                New Group
              </button>
            </div>
            <div className="card-content">
              {groups.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No groups yet</p>
                  <p className="text-sm text-gray-400">Create or join a group to start</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {groups.slice(0, 5).map((group) => (
                    <div key={group.id} className="flex items-center justify-between py-2">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                          <Users className="w-4 h-4 text-primary-600" />
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-900">
                            {group.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {group.members.length} members
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;
