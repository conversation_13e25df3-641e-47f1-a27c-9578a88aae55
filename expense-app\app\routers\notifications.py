"""
Enhanced notification router for managing user notifications
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from .. import schemas
from ..deps import get_db, get_current_user
from ..models import User
from ..services.enhanced_notification_service import PersistentNotificationService
from ..services.notification_templates import initialize_notification_templates

router = APIRouter(prefix="/notifications", tags=["notifications"])

@router.get("/", response_model=List[schemas.NotificationOut],
           summary="Get user notifications",
           description="Get notifications for the current user with pagination and filtering")
def get_notifications(
    limit: int = Query(50, ge=1, le=100, description="Number of notifications to return"),
    offset: int = Query(0, ge=0, description="Number of notifications to skip"),
    status: Optional[str] = Query(None, description="Filter by status: unread, read, archived"),
    category: Optional[str] = Query(None, description="Filter by category: expense, group, settlement, approval, system"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get notifications for the current user."""
    service = PersistentNotificationService(db)
    notifications = service.get_user_notifications(
        current_user.id, limit=limit, offset=offset, status=status
    )
    return notifications

@router.get("/summary", response_model=schemas.NotificationSummary,
           summary="Get notification summary",
           description="Get notification counts and breakdown by category/priority")
def get_notification_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get notification summary for the current user."""
    service = PersistentNotificationService(db)
    return service.get_notification_summary(current_user.id)

@router.put("/{notification_id}/read",
           summary="Mark notification as read",
           description="Mark a specific notification as read")
def mark_notification_read(
    notification_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Mark a notification as read."""
    service = PersistentNotificationService(db)
    success = service.mark_as_read(notification_id, current_user.id)
    
    if not success:
        raise HTTPException(status_code=404, detail="Notification not found")
    
    return {"message": "Notification marked as read"}

@router.put("/bulk/read",
           summary="Mark multiple notifications as read",
           description="Mark multiple notifications as read in bulk")
def mark_notifications_read_bulk(
    bulk_update: schemas.BulkNotificationUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Mark multiple notifications as read."""
    service = PersistentNotificationService(db)
    count = service.mark_bulk_as_read(bulk_update.notification_ids, current_user.id)
    
    return {"message": f"Marked {count} notifications as read"}

@router.get("/preferences", response_model=List[schemas.NotificationPreferenceOut],
           summary="Get notification preferences",
           description="Get user's notification preferences for different types")
def get_notification_preferences(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user's notification preferences."""
    from ..models import UserNotificationPreference
    
    preferences = db.query(UserNotificationPreference).filter(
        UserNotificationPreference.user_id == current_user.id
    ).all()
    
    return preferences

@router.put("/preferences",
           summary="Update notification preferences",
           description="Update user's notification preferences")
def update_notification_preferences(
    preferences: List[schemas.NotificationPreferenceUpdate],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update user's notification preferences."""
    from ..models import UserNotificationPreference
    from sqlalchemy import and_
    
    updated_count = 0
    
    for pref_update in preferences:
        # Check if preference exists
        existing = db.query(UserNotificationPreference).filter(
            and_(
                UserNotificationPreference.user_id == current_user.id,
                UserNotificationPreference.template_key == pref_update.template_key
            )
        ).first()
        
        if existing:
            # Update existing preference
            existing.enabled = pref_update.enabled
            existing.email_enabled = pref_update.email_enabled
            existing.push_enabled = pref_update.push_enabled
            updated_count += 1
        else:
            # Create new preference
            new_pref = UserNotificationPreference(
                user_id=current_user.id,
                template_key=pref_update.template_key,
                enabled=pref_update.enabled,
                email_enabled=pref_update.email_enabled,
                push_enabled=pref_update.push_enabled
            )
            db.add(new_pref)
            updated_count += 1
    
    db.commit()
    
    return {"message": f"Updated {updated_count} notification preferences"}

@router.get("/templates", response_model=List[schemas.NotificationTemplateOut],
           summary="Get notification templates",
           description="Get available notification templates (admin only)")
def get_notification_templates(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get notification templates."""
    from ..models import NotificationTemplate
    
    # For now, allow all users to see templates
    # In production, you might want to restrict this to admins
    templates = db.query(NotificationTemplate).filter(
        NotificationTemplate.is_active == True
    ).all()
    
    return templates

@router.post("/templates/initialize",
            summary="Initialize notification templates",
            description="Initialize default notification templates (admin only)")
def initialize_templates(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Initialize default notification templates."""
    # In production, you might want to restrict this to admin users
    try:
        initialize_notification_templates(db)
        return {"message": "Notification templates initialized successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/cleanup",
              summary="Cleanup old notifications",
              description="Clean up old read notifications")
def cleanup_old_notifications(
    days: int = Query(30, ge=1, le=365, description="Delete notifications older than this many days"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Clean up old read notifications."""
    service = PersistentNotificationService(db)
    count = service.cleanup_old_notifications(days)
    
    return {"message": f"Cleaned up {count} old notifications"}

@router.get("/unread/count",
           summary="Get unread notification count",
           description="Get count of unread notifications for badge display")
def get_unread_count(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get unread notification count."""
    from ..models import Notification, NotificationStatus
    from sqlalchemy import and_
    
    count = db.query(Notification).filter(
        and_(
            Notification.user_id == current_user.id,
            Notification.status == NotificationStatus.UNREAD
        )
    ).count()
    
    return {"unread_count": count}

@router.get("/recent", response_model=List[schemas.NotificationOut],
           summary="Get recent notifications",
           description="Get most recent notifications for real-time display")
def get_recent_notifications(
    limit: int = Query(10, ge=1, le=50, description="Number of recent notifications"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get recent notifications for real-time display."""
    service = PersistentNotificationService(db)
    notifications = service.get_user_notifications(
        current_user.id, limit=limit, offset=0
    )
    return notifications
