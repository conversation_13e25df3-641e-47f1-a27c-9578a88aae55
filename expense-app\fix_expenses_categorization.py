#!/usr/bin/env python3
"""
Fix script to add categorization columns to expenses table and create categorization tables
"""

import sqlite3
import os
import sys

def check_expenses_table():
    """Check current expenses table schema"""
    db_path = "./dev.db"
    
    if not os.path.exists(db_path):
        print("❌ Database file 'dev.db' does not exist!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if expenses table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='expenses';")
        if not cursor.fetchone():
            print("❌ Expenses table does not exist!")
            return False
        
        # Get expenses table schema
        cursor.execute("PRAGMA table_info(expenses);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print("📋 Current expenses table columns:")
        for col in column_names:
            print(f"  - {col}")
        
        # Check for category_id column
        if 'category_id' in column_names:
            print("✅ category_id column already exists")
            return True
        else:
            print("❌ category_id column is missing")
            return False
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking expenses table: {e}")
        return False

def create_categorization_tables():
    """Create categorization tables if they don't exist"""
    db_path = "./dev.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create expense_categories table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS expense_categories (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                color TEXT DEFAULT '#6B7280',
                icon TEXT,
                parent_id INTEGER,
                is_system BOOLEAN DEFAULT 0,
                created_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id),
                FOREIGN KEY (parent_id) REFERENCES expense_categories (id)
            )
        """)
        print("✅ Created/verified expense_categories table")
        
        # Create expense_tags table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS expense_tags (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                color TEXT DEFAULT '#3B82F6',
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        """)
        print("✅ Created/verified expense_tags table")
        
        # Create expense_tag_associations table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS expense_tag_associations (
                expense_id INTEGER,
                tag_id INTEGER,
                PRIMARY KEY (expense_id, tag_id),
                FOREIGN KEY (expense_id) REFERENCES expenses (id),
                FOREIGN KEY (tag_id) REFERENCES expense_tags (id)
            )
        """)
        print("✅ Created/verified expense_tag_associations table")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating categorization tables: {e}")
        return False

def add_category_id_to_expenses():
    """Add category_id column to expenses table"""
    db_path = "./dev.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Add category_id column
        cursor.execute("ALTER TABLE expenses ADD COLUMN category_id INTEGER")
        print("✅ Added category_id column to expenses table")
        
        # Note: SQLite doesn't support adding foreign key constraints to existing tables
        # The foreign key relationship will be enforced at the application level
        
        conn.commit()
        conn.close()
        return True
        
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e):
            print("ℹ️ category_id column already exists")
            return True
        else:
            print(f"❌ Error adding category_id column: {e}")
            return False
    except Exception as e:
        print(f"❌ Error adding category_id column: {e}")
        return False

def verify_categorization_schema():
    """Verify all categorization tables and columns exist"""
    db_path = "./dev.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check expenses table has category_id
        cursor.execute("PRAGMA table_info(expenses);")
        expenses_columns = [col[1] for col in cursor.fetchall()]
        
        if 'category_id' not in expenses_columns:
            print("❌ category_id column missing from expenses table")
            return False
        
        print("✅ expenses.category_id column exists")
        
        # Check categorization tables exist
        required_tables = ['expense_categories', 'expense_tags', 'expense_tag_associations']
        
        for table in required_tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            if not cursor.fetchone():
                print(f"❌ {table} table missing")
                return False
            print(f"✅ {table} table exists")
        
        # Check expense_categories table structure
        cursor.execute("PRAGMA table_info(expense_categories);")
        category_columns = [col[1] for col in cursor.fetchall()]
        required_category_columns = ['id', 'name', 'description', 'color', 'icon', 'parent_id', 'is_system', 'created_by', 'created_at', 'updated_at']
        
        missing_category_columns = [col for col in required_category_columns if col not in category_columns]
        if missing_category_columns:
            print(f"❌ Missing columns in expense_categories: {missing_category_columns}")
            return False
        
        print("✅ expense_categories table structure is complete")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error verifying schema: {e}")
        return False

def test_categorization_query():
    """Test the query that was failing"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        # Test the query that includes category_id
        cursor.execute("""
            SELECT expenses.id, expenses.payer_id, expenses.group_id, expenses.total, 
                   expenses.description, expenses.status, expenses.created_at, 
                   expenses.approved_at, expenses.category_id
            FROM expenses 
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        
        if result:
            print("✅ Categorization query test successful!")
            print(f"Sample expense: ID={result[0]}, category_id={result[8]}")
        else:
            print("✅ Categorization query structure is valid (no expenses found)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Categorization query test failed: {e}")
        return False

def initialize_system_categories():
    """Initialize default system categories"""
    db_path = "./dev.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if system categories already exist
        cursor.execute("SELECT COUNT(*) FROM expense_categories WHERE is_system = 1")
        existing_count = cursor.fetchone()[0]
        
        if existing_count > 0:
            print(f"ℹ️ {existing_count} system categories already exist")
            conn.close()
            return True
        
        # Insert default system categories
        system_categories = [
            ("Food & Dining", "Restaurants, groceries, food delivery", "#F59E0B", "🍽️"),
            ("Transportation", "Fuel, public transport, taxi, parking", "#3B82F6", "🚗"),
            ("Utilities", "Electricity, water, gas, internet", "#10B981", "⚡"),
            ("Entertainment", "Movies, games, events, subscriptions", "#8B5CF6", "🎬"),
            ("Shopping", "Clothing, electronics, household items", "#EC4899", "🛍️"),
            ("Healthcare", "Medical expenses, pharmacy, insurance", "#EF4444", "🏥"),
            ("Education", "Books, courses, training, tuition", "#06B6D4", "📚"),
            ("Travel", "Hotels, flights, vacation expenses", "#84CC16", "✈️"),
            ("Business", "Office supplies, meetings, professional services", "#6B7280", "💼"),
            ("Other", "Miscellaneous expenses", "#9CA3AF", "📦"),
        ]
        
        for name, description, color, icon in system_categories:
            cursor.execute("""
                INSERT INTO expense_categories (name, description, color, icon, is_system, created_by)
                VALUES (?, ?, ?, ?, 1, NULL)
            """, (name, description, color, icon))
        
        conn.commit()
        print(f"✅ Initialized {len(system_categories)} system categories")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error initializing system categories: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Fix Expenses Categorization Schema")
    print("=" * 50)
    
    print("\n1. Checking current expenses table...")
    has_category_id = check_expenses_table()
    
    print("\n2. Creating categorization tables...")
    if not create_categorization_tables():
        sys.exit(1)
    
    if not has_category_id:
        print("\n3. Adding category_id column to expenses...")
        if not add_category_id_to_expenses():
            sys.exit(1)
    else:
        print("\n3. category_id column already exists")
    
    print("\n4. Verifying categorization schema...")
    if not verify_categorization_schema():
        sys.exit(1)
    
    print("\n5. Testing categorization query...")
    if not test_categorization_query():
        sys.exit(1)
    
    print("\n6. Initializing system categories...")
    if not initialize_system_categories():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Expenses categorization schema fixed successfully!")
    print("\n✅ The 'no such column: expenses.category_id' error should be resolved.")
    print("✅ /expenses/history endpoint should now work.")
    print("✅ Categorization features are ready to use.")

if __name__ == "__main__":
    main()
