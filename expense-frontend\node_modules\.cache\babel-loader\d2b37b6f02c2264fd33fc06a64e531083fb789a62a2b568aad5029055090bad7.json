{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\contexts\\\\WebSocketContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { useWebSocket } from '../hooks/useWebSocket';\nimport { useAuth } from './AuthContext';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WebSocketContext = /*#__PURE__*/createContext(null);\nexport const WebSocketProvider = ({\n  children\n}) => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [lastMessage, setLastMessage] = useState(null);\n  const [messageHistory, setMessageHistory] = useState([]);\n  const [hasShownConnectionError, setHasShownConnectionError] = useState(false);\n  const [hasShownDisconnectionError, setHasShownDisconnectionError] = useState(false);\n  const webSocketReturn = useWebSocket({\n    onMessage: message => {\n      console.log('WebSocket message received:', message);\n      setLastMessage(message);\n      setMessageHistory(prev => [message, ...prev.slice(0, 99)]); // Keep last 100 messages\n\n      // Handle different message types\n      switch (message.type) {\n        case 'expense_created':\n        case 'expense_approved':\n        case 'expense_rejected':\n        case 'expense_updated':\n        case 'expense_deleted':\n          // Show toast for expense-related notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '💰',\n              duration: 4000\n            });\n          }\n          break;\n        case 'member_joined':\n        case 'member_left':\n        case 'group_settings_changed':\n        case 'join_request_received':\n          // Show toast for group-related notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '👥',\n              duration: 4000\n            });\n          }\n          break;\n        case 'settlement_initiated':\n        case 'settlement_received':\n        case 'settlement_accepted':\n        case 'settlement_disputed':\n        case 'settlement_completed':\n          // Show toast for settlement-related notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '💸',\n              duration: 5000\n            });\n          }\n          break;\n        case 'expense_needs_approval':\n        case 'expense_approval_reminder':\n          // Show toast for approval-related notifications with higher priority\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '⏰',\n              duration: 6000\n            });\n          }\n          break;\n        case 'system_maintenance':\n        case 'system_update':\n          // Show toast for system notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '🔧',\n              duration: 8000\n            });\n          }\n          break;\n        default:\n          // Generic notification\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '📢',\n              duration: 4000\n            });\n          }\n          break;\n      }\n    },\n    onConnect: () => {\n      console.log('WebSocket connected successfully');\n      setHasShownConnectionError(false);\n      setHasShownDisconnectionError(false);\n\n      // Only show success toast if user is authenticated\n      if (user) {\n        toast.success('Real-time notifications connected', {\n          duration: 2000\n        });\n      }\n    },\n    onDisconnect: () => {\n      console.log('WebSocket disconnected');\n\n      // Only show disconnection toast once and if user is authenticated\n      if (user && !hasShownDisconnectionError) {\n        setHasShownDisconnectionError(true);\n        toast.error('Real-time notifications disconnected', {\n          duration: 3000\n        });\n      }\n    },\n    onError: error => {\n      console.error('WebSocket connection error:', error);\n\n      // Only show error toast once and if user is authenticated\n      if (user && !hasShownConnectionError) {\n        setHasShownConnectionError(true);\n        toast.error('Notification connection error', {\n          duration: 4000\n        });\n      }\n    },\n    autoReconnect: true,\n    maxReconnectAttempts: 5,\n    reconnectInterval: 2000\n  });\n  const clearMessageHistory = () => {\n    setMessageHistory([]);\n    setLastMessage(null);\n  };\n\n  // Only connect when user is authenticated and token is available\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (user && token) {\n      console.log('User authenticated, connecting WebSocket...');\n      setHasShownConnectionError(false);\n      setHasShownDisconnectionError(false);\n\n      // Small delay to ensure auth state is stable\n      const connectTimeout = setTimeout(() => {\n        webSocketReturn.connect();\n      }, 100);\n      return () => clearTimeout(connectTimeout);\n    } else {\n      console.log('User not authenticated or no token, disconnecting WebSocket...');\n      webSocketReturn.disconnect();\n      clearMessageHistory();\n      setHasShownConnectionError(false);\n      setHasShownDisconnectionError(false);\n    }\n  }, [user, webSocketReturn.connect, webSocketReturn.disconnect]);\n  const contextValue = {\n    ...webSocketReturn,\n    lastMessage,\n    messageHistory,\n    clearMessageHistory\n  };\n  return /*#__PURE__*/_jsxDEV(WebSocketContext.Provider, {\n    value: contextValue,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 184,\n    columnNumber: 5\n  }, this);\n};\n_s(WebSocketProvider, \"dp0lpcvzC6U57g7uMPEZPDNBz8Q=\", false, function () {\n  return [useAuth, useWebSocket];\n});\n_c = WebSocketProvider;\nexport const useWebSocketContext = () => {\n  _s2();\n  const context = useContext(WebSocketContext);\n  if (!context) {\n    throw new Error('useWebSocketContext must be used within a WebSocketProvider');\n  }\n  return context;\n};\n_s2(useWebSocketContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default WebSocketContext;\nvar _c;\n$RefreshReg$(_c, \"WebSocketProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "useWebSocket", "useAuth", "toast", "jsxDEV", "_jsxDEV", "WebSocketContext", "WebSocketProvider", "children", "_s", "user", "lastMessage", "setLastMessage", "messageHistory", "setMessageHistory", "hasShownConnectionError", "setHasShownConnectionError", "hasShownDisconnectionError", "setHasShownDisconnectionError", "webSocketReturn", "onMessage", "message", "console", "log", "prev", "slice", "type", "title", "icon", "duration", "onConnect", "success", "onDisconnect", "error", "onError", "autoReconnect", "maxReconnectAttempts", "reconnectInterval", "clearMessageHistory", "token", "localStorage", "getItem", "connectTimeout", "setTimeout", "connect", "clearTimeout", "disconnect", "contextValue", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useWebSocketContext", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/contexts/WebSocketContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport { useWebSocket, EnhancedWebSocketMessage, UseWebSocketReturn } from '../hooks/useWebSocket';\nimport { useAuth } from './AuthContext';\nimport toast from 'react-hot-toast';\n\ninterface WebSocketContextType extends UseWebSocketReturn {\n  lastMessage: EnhancedWebSocketMessage | null;\n  messageHistory: EnhancedWebSocketMessage[];\n  clearMessageHistory: () => void;\n}\n\nconst WebSocketContext = createContext<WebSocketContextType | null>(null);\n\ninterface WebSocketProviderProps {\n  children: ReactNode;\n}\n\nexport const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {\n  const { user } = useAuth();\n  const [lastMessage, setLastMessage] = useState<EnhancedWebSocketMessage | null>(null);\n  const [messageHistory, setMessageHistory] = useState<EnhancedWebSocketMessage[]>([]);\n  const [hasShownConnectionError, setHasShownConnectionError] = useState(false);\n  const [hasShownDisconnectionError, setHasShownDisconnectionError] = useState(false);\n\n  const webSocketReturn = useWebSocket({\n    onMessage: (message: EnhancedWebSocketMessage) => {\n      console.log('WebSocket message received:', message);\n      \n      setLastMessage(message);\n      setMessageHistory(prev => [message, ...prev.slice(0, 99)]); // Keep last 100 messages\n      \n      // Handle different message types\n      switch (message.type) {\n        case 'expense_created':\n        case 'expense_approved':\n        case 'expense_rejected':\n        case 'expense_updated':\n        case 'expense_deleted':\n          // Show toast for expense-related notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '💰',\n              duration: 4000,\n            });\n          }\n          break;\n          \n        case 'member_joined':\n        case 'member_left':\n        case 'group_settings_changed':\n        case 'join_request_received':\n          // Show toast for group-related notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '👥',\n              duration: 4000,\n            });\n          }\n          break;\n          \n        case 'settlement_initiated':\n        case 'settlement_received':\n        case 'settlement_accepted':\n        case 'settlement_disputed':\n        case 'settlement_completed':\n          // Show toast for settlement-related notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '💸',\n              duration: 5000,\n            });\n          }\n          break;\n          \n        case 'expense_needs_approval':\n        case 'expense_approval_reminder':\n          // Show toast for approval-related notifications with higher priority\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '⏰',\n              duration: 6000,\n            });\n          }\n          break;\n          \n        case 'system_maintenance':\n        case 'system_update':\n          // Show toast for system notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '🔧',\n              duration: 8000,\n            });\n          }\n          break;\n          \n        default:\n          // Generic notification\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '📢',\n              duration: 4000,\n            });\n          }\n          break;\n      }\n    },\n    onConnect: () => {\n      console.log('WebSocket connected successfully');\n      setHasShownConnectionError(false);\n      setHasShownDisconnectionError(false);\n\n      // Only show success toast if user is authenticated\n      if (user) {\n        toast.success('Real-time notifications connected', {\n          duration: 2000,\n        });\n      }\n    },\n    onDisconnect: () => {\n      console.log('WebSocket disconnected');\n\n      // Only show disconnection toast once and if user is authenticated\n      if (user && !hasShownDisconnectionError) {\n        setHasShownDisconnectionError(true);\n        toast.error('Real-time notifications disconnected', {\n          duration: 3000,\n        });\n      }\n    },\n    onError: (error) => {\n      console.error('WebSocket connection error:', error);\n\n      // Only show error toast once and if user is authenticated\n      if (user && !hasShownConnectionError) {\n        setHasShownConnectionError(true);\n        toast.error('Notification connection error', {\n          duration: 4000,\n        });\n      }\n    },\n    autoReconnect: true,\n    maxReconnectAttempts: 5,\n    reconnectInterval: 2000,\n  });\n\n  const clearMessageHistory = () => {\n    setMessageHistory([]);\n    setLastMessage(null);\n  };\n\n  // Only connect when user is authenticated and token is available\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n\n    if (user && token) {\n      console.log('User authenticated, connecting WebSocket...');\n      setHasShownConnectionError(false);\n      setHasShownDisconnectionError(false);\n\n      // Small delay to ensure auth state is stable\n      const connectTimeout = setTimeout(() => {\n        webSocketReturn.connect();\n      }, 100);\n\n      return () => clearTimeout(connectTimeout);\n    } else {\n      console.log('User not authenticated or no token, disconnecting WebSocket...');\n      webSocketReturn.disconnect();\n      clearMessageHistory();\n      setHasShownConnectionError(false);\n      setHasShownDisconnectionError(false);\n    }\n  }, [user, webSocketReturn.connect, webSocketReturn.disconnect]);\n\n  const contextValue: WebSocketContextType = {\n    ...webSocketReturn,\n    lastMessage,\n    messageHistory,\n    clearMessageHistory,\n  };\n\n  return (\n    <WebSocketContext.Provider value={contextValue}>\n      {children}\n    </WebSocketContext.Provider>\n  );\n};\n\nexport const useWebSocketContext = (): WebSocketContextType => {\n  const context = useContext(WebSocketContext);\n  if (!context) {\n    throw new Error('useWebSocketContext must be used within a WebSocketProvider');\n  }\n  return context;\n};\n\nexport default WebSocketContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAmB,OAAO;AACxF,SAASC,YAAY,QAAsD,uBAAuB;AAClG,SAASC,OAAO,QAAQ,eAAe;AACvC,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQpC,MAAMC,gBAAgB,gBAAGT,aAAa,CAA8B,IAAI,CAAC;AAMzE,OAAO,MAAMU,iBAAmD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACnF,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAkC,IAAI,CAAC;EACrF,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAA6B,EAAE,CAAC;EACpF,MAAM,CAACe,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACiB,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEnF,MAAMmB,eAAe,GAAGlB,YAAY,CAAC;IACnCmB,SAAS,EAAGC,OAAiC,IAAK;MAChDC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,OAAO,CAAC;MAEnDT,cAAc,CAACS,OAAO,CAAC;MACvBP,iBAAiB,CAACU,IAAI,IAAI,CAACH,OAAO,EAAE,GAAGG,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5D;MACA,QAAQJ,OAAO,CAACK,IAAI;QAClB,KAAK,iBAAiB;QACtB,KAAK,kBAAkB;QACvB,KAAK,kBAAkB;QACvB,KAAK,iBAAiB;QACtB,KAAK,iBAAiB;UACpB;UACA,IAAIL,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACA,OAAO,EAAE;YACpClB,KAAK,CAACkB,OAAO,CAACA,OAAO,EAAE;cACrBO,IAAI,EAAE,IAAI;cACVC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UACA;QAEF,KAAK,eAAe;QACpB,KAAK,aAAa;QAClB,KAAK,wBAAwB;QAC7B,KAAK,uBAAuB;UAC1B;UACA,IAAIR,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACA,OAAO,EAAE;YACpClB,KAAK,CAACkB,OAAO,CAACA,OAAO,EAAE;cACrBO,IAAI,EAAE,IAAI;cACVC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UACA;QAEF,KAAK,sBAAsB;QAC3B,KAAK,qBAAqB;QAC1B,KAAK,qBAAqB;QAC1B,KAAK,qBAAqB;QAC1B,KAAK,sBAAsB;UACzB;UACA,IAAIR,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACA,OAAO,EAAE;YACpClB,KAAK,CAACkB,OAAO,CAACA,OAAO,EAAE;cACrBO,IAAI,EAAE,IAAI;cACVC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UACA;QAEF,KAAK,wBAAwB;QAC7B,KAAK,2BAA2B;UAC9B;UACA,IAAIR,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACA,OAAO,EAAE;YACpClB,KAAK,CAACkB,OAAO,CAACA,OAAO,EAAE;cACrBO,IAAI,EAAE,GAAG;cACTC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UACA;QAEF,KAAK,oBAAoB;QACzB,KAAK,eAAe;UAClB;UACA,IAAIR,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACA,OAAO,EAAE;YACpClB,KAAK,CAACkB,OAAO,CAACA,OAAO,EAAE;cACrBO,IAAI,EAAE,IAAI;cACVC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UACA;QAEF;UACE;UACA,IAAIR,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACA,OAAO,EAAE;YACpClB,KAAK,CAACkB,OAAO,CAACA,OAAO,EAAE;cACrBO,IAAI,EAAE,IAAI;cACVC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UACA;MACJ;IACF,CAAC;IACDC,SAAS,EAAEA,CAAA,KAAM;MACfR,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/CP,0BAA0B,CAAC,KAAK,CAAC;MACjCE,6BAA6B,CAAC,KAAK,CAAC;;MAEpC;MACA,IAAIR,IAAI,EAAE;QACRP,KAAK,CAAC4B,OAAO,CAAC,mCAAmC,EAAE;UACjDF,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC;IACDG,YAAY,EAAEA,CAAA,KAAM;MAClBV,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;;MAErC;MACA,IAAIb,IAAI,IAAI,CAACO,0BAA0B,EAAE;QACvCC,6BAA6B,CAAC,IAAI,CAAC;QACnCf,KAAK,CAAC8B,KAAK,CAAC,sCAAsC,EAAE;UAClDJ,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC;IACDK,OAAO,EAAGD,KAAK,IAAK;MAClBX,OAAO,CAACW,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;MAEnD;MACA,IAAIvB,IAAI,IAAI,CAACK,uBAAuB,EAAE;QACpCC,0BAA0B,CAAC,IAAI,CAAC;QAChCb,KAAK,CAAC8B,KAAK,CAAC,+BAA+B,EAAE;UAC3CJ,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC;IACDM,aAAa,EAAE,IAAI;IACnBC,oBAAoB,EAAE,CAAC;IACvBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEF,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCxB,iBAAiB,CAAC,EAAE,CAAC;IACrBF,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACAb,SAAS,CAAC,MAAM;IACd,MAAMwC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAE3C,IAAI/B,IAAI,IAAI6B,KAAK,EAAE;MACjBjB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC1DP,0BAA0B,CAAC,KAAK,CAAC;MACjCE,6BAA6B,CAAC,KAAK,CAAC;;MAEpC;MACA,MAAMwB,cAAc,GAAGC,UAAU,CAAC,MAAM;QACtCxB,eAAe,CAACyB,OAAO,CAAC,CAAC;MAC3B,CAAC,EAAE,GAAG,CAAC;MAEP,OAAO,MAAMC,YAAY,CAACH,cAAc,CAAC;IAC3C,CAAC,MAAM;MACLpB,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;MAC7EJ,eAAe,CAAC2B,UAAU,CAAC,CAAC;MAC5BR,mBAAmB,CAAC,CAAC;MACrBtB,0BAA0B,CAAC,KAAK,CAAC;MACjCE,6BAA6B,CAAC,KAAK,CAAC;IACtC;EACF,CAAC,EAAE,CAACR,IAAI,EAAES,eAAe,CAACyB,OAAO,EAAEzB,eAAe,CAAC2B,UAAU,CAAC,CAAC;EAE/D,MAAMC,YAAkC,GAAG;IACzC,GAAG5B,eAAe;IAClBR,WAAW;IACXE,cAAc;IACdyB;EACF,CAAC;EAED,oBACEjC,OAAA,CAACC,gBAAgB,CAAC0C,QAAQ;IAACC,KAAK,EAAEF,YAAa;IAAAvC,QAAA,EAC5CA;EAAQ;IAAA0C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC,CAAC;AAAC5C,EAAA,CA1KWF,iBAAmD;EAAA,QAC7CL,OAAO,EAMAD,YAAY;AAAA;AAAAqD,EAAA,GAPzB/C,iBAAmD;AA4KhE,OAAO,MAAMgD,mBAAmB,GAAGA,CAAA,KAA4B;EAAAC,GAAA;EAC7D,MAAMC,OAAO,GAAG3D,UAAU,CAACQ,gBAAgB,CAAC;EAC5C,IAAI,CAACmD,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6DAA6D,CAAC;EAChF;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,mBAAmB;AAQhC,eAAejD,gBAAgB;AAAC,IAAAgD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}