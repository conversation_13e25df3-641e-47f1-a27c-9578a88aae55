"""
Enhanced notification service with database persistence and multi-user targeting
"""

import json
from typing import List, Dict, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from datetime import datetime, timedelta

from ..models import (
    User, Group, Expense, Notification, NotificationTemplate, 
    UserNotificationPreference, NotificationCategory, NotificationPriority, 
    NotificationStatus
)
from .. import schemas
from ..websocket_manager import manager, EnhancedNotificationService


class NotificationTargetingService:
    """Service for determining notification recipients"""
    
    @staticmethod
    def get_group_members(db: Session, group_id: int, exclude_user_ids: List[int] = None) -> List[int]:
        """Get all member IDs for a group"""
        from ..models import Group, user_group_association

        query = db.query(user_group_association.c.user_id).filter(
            user_group_association.c.group_id == group_id
        )

        if exclude_user_ids:
            query = query.filter(~user_group_association.c.user_id.in_(exclude_user_ids))

        return [row[0] for row in query.all()]
    
    @staticmethod
    def get_group_owners_and_admins(db: Session, group_id: int) -> List[int]:
        """Get owner and admin IDs for a group"""
        group = db.query(Group).filter(Group.id == group_id).first()
        if not group:
            return []
        
        # For now, just return owner. In future, add admin role support
        return [group.owner_id]
    
    @staticmethod
    def get_expense_approvers(db: Session, expense_id: int) -> List[int]:
        """Get users who can approve an expense"""
        expense = db.query(Expense).filter(Expense.id == expense_id).first()
        if not expense:
            return []
        
        # For now, return group owner. In future, add approval role support
        return NotificationTargetingService.get_group_owners_and_admins(db, expense.group_id)


class PersistentNotificationService:
    """Service for creating and managing persistent notifications"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_notification(self, user_id: int, template_key: str, 
                          context: Dict[str, Any], **kwargs) -> Notification:
        """Create a persistent notification from template"""
        
        # Get template
        template = self.db.query(NotificationTemplate).filter(
            NotificationTemplate.template_key == template_key
        ).first()
        
        if not template:
            raise ValueError(f"Notification template '{template_key}' not found")
        
        # Check user preferences
        preference = self.db.query(UserNotificationPreference).filter(
            and_(
                UserNotificationPreference.user_id == user_id,
                UserNotificationPreference.template_key == template_key
            )
        ).first()
        
        if preference and not preference.enabled:
            return None  # User has disabled this notification type
        
        # Render template with context
        title = self._render_template(template.title_template, context)
        message = self._render_template(template.message_template, context)
        action_text = self._render_template(template.action_text_template, context) if template.action_text_template else None
        action_url = self._render_template(template.action_url_template, context) if template.action_url_template else None
        
        # Create notification
        notification = Notification(
            user_id=user_id,
            title=title,
            message=message,
            category=template.category,
            priority=template.priority,
            action_url=action_url,
            action_text=action_text,
            expense_id=context.get('expense_id'),
            group_id=context.get('group_id'),
            settlement_id=context.get('settlement_id'),
            data=json.dumps(context) if context else None,
            expires_at=kwargs.get('expires_at')
        )
        
        self.db.add(notification)
        self.db.commit()
        self.db.refresh(notification)
        
        return notification
    
    def create_bulk_notifications(self, user_ids: List[int], template_key: str, 
                                context: Dict[str, Any], **kwargs) -> List[Notification]:
        """Create notifications for multiple users"""
        notifications = []
        
        for user_id in user_ids:
            try:
                notification = self.create_notification(user_id, template_key, context, **kwargs)
                if notification:
                    notifications.append(notification)
            except Exception as e:
                print(f"Failed to create notification for user {user_id}: {e}")
        
        return notifications
    
    def mark_as_read(self, notification_id: int, user_id: int) -> bool:
        """Mark notification as read"""
        notification = self.db.query(Notification).filter(
            and_(
                Notification.id == notification_id,
                Notification.user_id == user_id
            )
        ).first()
        
        if not notification:
            return False
        
        notification.status = NotificationStatus.READ
        notification.read_at = datetime.utcnow()
        self.db.commit()
        
        return True
    
    def mark_bulk_as_read(self, notification_ids: List[int], user_id: int) -> int:
        """Mark multiple notifications as read"""
        count = self.db.query(Notification).filter(
            and_(
                Notification.id.in_(notification_ids),
                Notification.user_id == user_id
            )
        ).update({
            'status': NotificationStatus.READ,
            'read_at': datetime.utcnow()
        }, synchronize_session=False)
        
        self.db.commit()
        return count
    
    def get_user_notifications(self, user_id: int, limit: int = 50, 
                             offset: int = 0, status: str = None) -> List[Notification]:
        """Get notifications for a user"""
        query = self.db.query(Notification).filter(
            Notification.user_id == user_id
        )
        
        if status:
            query = query.filter(Notification.status == status)
        
        return query.order_by(desc(Notification.created_at)).offset(offset).limit(limit).all()
    
    def get_notification_summary(self, user_id: int) -> schemas.NotificationSummary:
        """Get notification summary for a user"""
        total_count = self.db.query(Notification).filter(
            Notification.user_id == user_id
        ).count()
        
        unread_count = self.db.query(Notification).filter(
            and_(
                Notification.user_id == user_id,
                Notification.status == NotificationStatus.UNREAD
            )
        ).count()
        
        # Get category breakdown
        category_query = self.db.query(
            Notification.category,
            self.db.func.count(Notification.id)
        ).filter(
            and_(
                Notification.user_id == user_id,
                Notification.status == NotificationStatus.UNREAD
            )
        ).group_by(Notification.category).all()
        
        categories = {str(cat.value): count for cat, count in category_query}
        
        # Get priority breakdown
        priority_query = self.db.query(
            Notification.priority,
            self.db.func.count(Notification.id)
        ).filter(
            and_(
                Notification.user_id == user_id,
                Notification.status == NotificationStatus.UNREAD
            )
        ).group_by(Notification.priority).all()
        
        priorities = {str(pri.value): count for pri, count in priority_query}
        
        return schemas.NotificationSummary(
            total_count=total_count,
            unread_count=unread_count,
            categories=categories,
            priorities=priorities
        )
    
    def cleanup_old_notifications(self, days: int = 30) -> int:
        """Clean up old read notifications"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        count = self.db.query(Notification).filter(
            and_(
                Notification.status == NotificationStatus.READ,
                Notification.read_at < cutoff_date
            )
        ).delete()
        
        self.db.commit()
        return count
    
    def _render_template(self, template: str, context: Dict[str, Any]) -> str:
        """Render template string with context variables"""
        if not template:
            return ""
        
        try:
            return template.format(**context)
        except KeyError as e:
            print(f"Missing template variable: {e}")
            return template
        except Exception as e:
            print(f"Template rendering error: {e}")
            return template


class ComprehensiveNotificationService:
    """Main service that combines real-time and persistent notifications"""
    
    def __init__(self, db: Session):
        self.db = db
        self.persistent_service = PersistentNotificationService(db)
        self.targeting_service = NotificationTargetingService()
    
    async def send_expense_created_notifications(self, expense: Expense, creator: User):
        """Send notifications when expense is created"""
        group_member_ids = self.targeting_service.get_group_members(self.db, expense.group_id)

        # Get group name safely
        group_name = "Unknown Group"
        if hasattr(expense, 'group') and expense.group:
            group_name = expense.group.name
        else:
            # Fetch group name from database
            from ..models import Group
            group = self.db.query(Group).filter(Group.id == expense.group_id).first()
            if group:
                group_name = group.name

        context = {
            'expense_id': expense.id,
            'expense_description': expense.description,
            'expense_total': expense.total,
            'creator_name': creator.full_name or creator.email,
            'group_name': group_name,
            'group_id': expense.group_id
        }
        
        # Create persistent notifications
        self.persistent_service.create_bulk_notifications(
            group_member_ids, 'expense_created', context
        )
        
        # Send real-time notifications
        await EnhancedNotificationService.notify_expense_created(
            {'id': expense.id, 'description': expense.description, 'total': float(expense.total), 'created_at': expense.created_at.isoformat()},
            group_member_ids, creator.id, context['creator_name'], context['group_name']
        )
    
    async def send_expense_approved_notifications(self, expense: Expense, approver: User):
        """Send notifications when expense is approved"""
        group_member_ids = self.targeting_service.get_group_members(self.db, expense.group_id)

        # Get group name safely
        group_name = "Unknown Group"
        if hasattr(expense, 'group') and expense.group:
            group_name = expense.group.name
        else:
            # Fetch group name from database
            from ..models import Group
            group = self.db.query(Group).filter(Group.id == expense.group_id).first()
            if group:
                group_name = group.name

        context = {
            'expense_id': expense.id,
            'expense_description': expense.description,
            'expense_total': expense.total,
            'approver_name': approver.full_name or approver.email,
            'group_name': group_name,
            'group_id': expense.group_id
        }
        
        # Create persistent notifications
        self.persistent_service.create_bulk_notifications(
            group_member_ids, 'expense_approved', context
        )
        
        # Send real-time notifications
        await EnhancedNotificationService.notify_expense_approved(
            {'id': expense.id, 'description': expense.description, 'total': float(expense.total), 'approved_at': expense.approved_at.isoformat() if expense.approved_at else None},
            context['approver_name'], group_member_ids, context['group_name']
        )
    
    async def send_member_joined_notifications(self, group: Group, new_member: User, existing_member_ids: List[int]):
        """Send notifications when member joins group"""
        context = {
            'group_id': group.id,
            'group_name': group.name,
            'member_name': new_member.full_name or new_member.email,
            'member_id': new_member.id
        }

        # Create persistent notifications
        self.persistent_service.create_bulk_notifications(
            existing_member_ids, 'member_joined', context
        )

        # Send real-time notifications
        await EnhancedNotificationService.notify_member_joined(
            context['member_name'], context['group_name'], existing_member_ids, group.id
        )
