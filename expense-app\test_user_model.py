#!/usr/bin/env python3
"""
Test script to verify the User model works with the updated database schema
This simulates what happens during login without needing the full FastAPI server
"""

import sqlite3
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_direct_sql_query():
    """Test direct SQL query to ensure all columns exist"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        # This is the exact query that SQLAlchemy generates for the User model
        cursor.execute("""
            SELECT users.id, users.email, users.hashed_password, users.groq_api_key,
                   users.full_name, users.avatar_url, users.phone, users.timezone, 
                   users.currency, users.language, users.email_notifications, 
                   users.expense_notifications, users.approval_notifications, 
                   users.settlement_notifications, users.created_at, users.updated_at
            FROM users 
            WHERE users.email = ?
        """, ("<EMAIL>",))
        
        result = cursor.fetchone()
        
        if result:
            print("✅ Direct SQL query successful!")
            print(f"User found: {result[1]} (ID: {result[0]})")
            print(f"Profile fields: full_name={result[4]}, timezone={result[7]}, currency={result[8]}")
        else:
            print("✅ Direct SQL query successful (no user found)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Direct SQL query failed: {e}")
        return False

def test_user_model_import():
    """Test importing the User model"""
    try:
        from app.models import User
        print("✅ User model imported successfully")
        
        # Check if the model has all the expected attributes
        expected_attrs = [
            'id', 'email', 'hashed_password', 'groq_api_key',
            'full_name', 'avatar_url', 'phone', 'timezone', 'currency', 'language',
            'email_notifications', 'expense_notifications', 'approval_notifications', 
            'settlement_notifications', 'created_at', 'updated_at'
        ]
        
        model_attrs = [attr for attr in dir(User) if not attr.startswith('_')]
        
        print(f"✅ User model has {len(model_attrs)} attributes")
        
        missing_attrs = [attr for attr in expected_attrs if not hasattr(User, attr)]
        if missing_attrs:
            print(f"⚠️ Missing attributes in model: {missing_attrs}")
        else:
            print("✅ All expected attributes present in User model")
        
        return True
        
    except Exception as e:
        print(f"❌ User model import failed: {e}")
        return False

def test_database_connection():
    """Test database connection setup"""
    try:
        from app.database import engine, SessionLocal
        print("✅ Database connection setup imported successfully")
        
        # Test creating a session
        db = SessionLocal()
        db.close()
        print("✅ Database session created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        return False

def test_crud_function():
    """Test the CRUD function that was failing"""
    try:
        from app.database import SessionLocal
        from app.crud import get_user_by_email
        
        print("✅ CRUD functions imported successfully")
        
        # Test the function that was causing the error
        db = SessionLocal()
        try:
            user = get_user_by_email(db, "<EMAIL>")
            if user:
                print(f"✅ get_user_by_email successful! Found user: {user.email}")
                print(f"   Profile: {user.full_name}, {user.timezone}, {user.currency}")
            else:
                print("✅ get_user_by_email successful (no user found)")
        finally:
            db.close()
        
        return True
        
    except Exception as e:
        print(f"❌ CRUD function test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing User Model and Database Schema")
    print("=" * 50)
    
    tests = [
        ("Direct SQL Query", test_direct_sql_query),
        ("User Model Import", test_user_model_import),
        ("Database Connection", test_database_connection),
        ("CRUD Function", test_crud_function),
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed!")
        print("✅ The 'no such column: users.full_name' error is fixed!")
        print("✅ You can now start the FastAPI server and login should work.")
        print("\nTo start the server (make sure virtual environment is activated):")
        print("  uvicorn app.main:app --reload")
    else:
        print("❌ Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
