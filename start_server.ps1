# PowerShell script to start the expense tracker backend
Write-Host "Starting Expense Tracker Backend Server..." -ForegroundColor Green
Write-Host ""

# Change to the expense-app directory
Set-Location expense-app

# Activate virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Yellow
& ..\.venv\Scripts\Activate.ps1

# Install dependencies if needed
Write-Host "Installing/updating dependencies..." -ForegroundColor Yellow
pip install -r requirements.txt

# Start the FastAPI server
Write-Host ""
Write-Host "Starting FastAPI server..." -ForegroundColor Green
Write-Host "Server will be available at: http://localhost:8000" -ForegroundColor Cyan
Write-Host "API documentation at: http://localhost:8000/docs" -ForegroundColor Cyan
Write-Host ""

uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
