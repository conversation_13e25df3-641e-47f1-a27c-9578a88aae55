{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authAPI } from '../services/api';\nimport { notificationService } from '../services/notificationService';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const initAuth = async () => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          const response = await authAPI.getCurrentUser();\n          setUser(response.data);\n\n          // WebSocket connection is now handled automatically by WebSocketContext\n        } catch (error) {\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n        }\n      }\n      setLoading(false);\n    };\n    initAuth();\n  }, []);\n  const login = async (email, password) => {\n    try {\n      const response = await authAPI.login({\n        email,\n        password\n      });\n      const {\n        access_token\n      } = response.data;\n      localStorage.setItem('token', access_token);\n\n      // Get user data\n      const userResponse = await authAPI.getCurrentUser();\n      setUser(userResponse.data);\n      localStorage.setItem('user', JSON.stringify(userResponse.data));\n\n      // WebSocket connection is now handled automatically by WebSocketContext\n\n      toast.success('Welcome back!');\n      return true;\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Login failed';\n      toast.error(message);\n      return false;\n    }\n  };\n  const register = async (email, password, groqApiKey) => {\n    try {\n      await authAPI.register({\n        email,\n        password,\n        groq_api_key: groqApiKey\n      });\n\n      // Auto-login after registration\n      const loginSuccess = await login(email, password);\n      if (loginSuccess) {\n        toast.success('Account created successfully!');\n      }\n      return loginSuccess;\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || 'Registration failed';\n      toast.error(message);\n      return false;\n    }\n  };\n  const logout = () => {\n    // Disconnect WebSocket\n    notificationService.disconnectWebSocket();\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n    toast.success('Logged out successfully');\n  };\n  const value = {\n    user,\n    loading,\n    login,\n    register,\n    logout,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authAPI", "notificationService", "toast", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "initAuth", "token", "localStorage", "getItem", "response", "getCurrentUser", "data", "error", "removeItem", "login", "email", "password", "access_token", "setItem", "userResponse", "JSON", "stringify", "success", "_error$response", "_error$response$data", "message", "detail", "register", "groqApiKey", "groq_api_key", "loginSuccess", "_error$response2", "_error$response2$data", "logout", "disconnectWebSocket", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { User } from '../types';\nimport { authAPI } from '../services/api';\nimport { notificationService } from '../services/notificationService';\nimport toast from 'react-hot-toast';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  login: (email: string, password: string) => Promise<boolean>;\n  register: (email: string, password: string, groqApiKey: string) => Promise<boolean>;\n  logout: () => void;\n  isAuthenticated: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const initAuth = async () => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        try {\n          const response = await authAPI.getCurrentUser();\n          setUser(response.data);\n\n          // WebSocket connection is now handled automatically by WebSocketContext\n        } catch (error) {\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n        }\n      }\n      setLoading(false);\n    };\n\n    initAuth();\n  }, []);\n\n  const login = async (email: string, password: string): Promise<boolean> => {\n    try {\n      const response = await authAPI.login({ email, password });\n      const { access_token } = response.data;\n\n      localStorage.setItem('token', access_token);\n\n      // Get user data\n      const userResponse = await authAPI.getCurrentUser();\n      setUser(userResponse.data);\n      localStorage.setItem('user', JSON.stringify(userResponse.data));\n\n      // WebSocket connection is now handled automatically by WebSocketContext\n\n      toast.success('Welcome back!');\n      return true;\n    } catch (error: any) {\n      const message = error.response?.data?.detail || 'Login failed';\n      toast.error(message);\n      return false;\n    }\n  };\n\n  const register = async (email: string, password: string, groqApiKey: string): Promise<boolean> => {\n    try {\n      await authAPI.register({ email, password, groq_api_key: groqApiKey });\n      \n      // Auto-login after registration\n      const loginSuccess = await login(email, password);\n      if (loginSuccess) {\n        toast.success('Account created successfully!');\n      }\n      return loginSuccess;\n    } catch (error: any) {\n      const message = error.response?.data?.detail || 'Registration failed';\n      toast.error(message);\n      return false;\n    }\n  };\n\n  const logout = () => {\n    // Disconnect WebSocket\n    notificationService.disconnectWebSocket();\n\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n    toast.success('Logged out successfully');\n  };\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    login,\n    register,\n    logout,\n    isAuthenticated: !!user,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AAExF,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWpC,MAAMC,WAAW,gBAAGT,aAAa,CAA8BU,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGZ,UAAU,CAACQ,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAYpB,OAAO,MAAMI,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMmB,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACT,IAAI;UACF,MAAMG,QAAQ,GAAG,MAAMtB,OAAO,CAACuB,cAAc,CAAC,CAAC;UAC/CR,OAAO,CAACO,QAAQ,CAACE,IAAI,CAAC;;UAEtB;QACF,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdL,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;UAChCN,YAAY,CAACM,UAAU,CAAC,MAAM,CAAC;QACjC;MACF;MACAT,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDC,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,KAAK,GAAG,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,KAAuB;IACzE,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMtB,OAAO,CAAC2B,KAAK,CAAC;QAAEC,KAAK;QAAEC;MAAS,CAAC,CAAC;MACzD,MAAM;QAAEC;MAAa,CAAC,GAAGR,QAAQ,CAACE,IAAI;MAEtCJ,YAAY,CAACW,OAAO,CAAC,OAAO,EAAED,YAAY,CAAC;;MAE3C;MACA,MAAME,YAAY,GAAG,MAAMhC,OAAO,CAACuB,cAAc,CAAC,CAAC;MACnDR,OAAO,CAACiB,YAAY,CAACR,IAAI,CAAC;MAC1BJ,YAAY,CAACW,OAAO,CAAC,MAAM,EAAEE,IAAI,CAACC,SAAS,CAACF,YAAY,CAACR,IAAI,CAAC,CAAC;;MAE/D;;MAEAtB,KAAK,CAACiC,OAAO,CAAC,eAAe,CAAC;MAC9B,OAAO,IAAI;IACb,CAAC,CAAC,OAAOV,KAAU,EAAE;MAAA,IAAAW,eAAA,EAAAC,oBAAA;MACnB,MAAMC,OAAO,GAAG,EAAAF,eAAA,GAAAX,KAAK,CAACH,QAAQ,cAAAc,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBZ,IAAI,cAAAa,oBAAA,uBAApBA,oBAAA,CAAsBE,MAAM,KAAI,cAAc;MAC9DrC,KAAK,CAACuB,KAAK,CAACa,OAAO,CAAC;MACpB,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAME,QAAQ,GAAG,MAAAA,CAAOZ,KAAa,EAAEC,QAAgB,EAAEY,UAAkB,KAAuB;IAChG,IAAI;MACF,MAAMzC,OAAO,CAACwC,QAAQ,CAAC;QAAEZ,KAAK;QAAEC,QAAQ;QAAEa,YAAY,EAAED;MAAW,CAAC,CAAC;;MAErE;MACA,MAAME,YAAY,GAAG,MAAMhB,KAAK,CAACC,KAAK,EAAEC,QAAQ,CAAC;MACjD,IAAIc,YAAY,EAAE;QAChBzC,KAAK,CAACiC,OAAO,CAAC,+BAA+B,CAAC;MAChD;MACA,OAAOQ,YAAY;IACrB,CAAC,CAAC,OAAOlB,KAAU,EAAE;MAAA,IAAAmB,gBAAA,EAAAC,qBAAA;MACnB,MAAMP,OAAO,GAAG,EAAAM,gBAAA,GAAAnB,KAAK,CAACH,QAAQ,cAAAsB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpB,IAAI,cAAAqB,qBAAA,uBAApBA,qBAAA,CAAsBN,MAAM,KAAI,qBAAqB;MACrErC,KAAK,CAACuB,KAAK,CAACa,OAAO,CAAC;MACpB,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMQ,MAAM,GAAGA,CAAA,KAAM;IACnB;IACA7C,mBAAmB,CAAC8C,mBAAmB,CAAC,CAAC;IAEzC3B,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;IAChCN,YAAY,CAACM,UAAU,CAAC,MAAM,CAAC;IAC/BX,OAAO,CAAC,IAAI,CAAC;IACbb,KAAK,CAACiC,OAAO,CAAC,yBAAyB,CAAC;EAC1C,CAAC;EAED,MAAMa,KAAsB,GAAG;IAC7BlC,IAAI;IACJE,OAAO;IACPW,KAAK;IACLa,QAAQ;IACRM,MAAM;IACNG,eAAe,EAAE,CAAC,CAACnC;EACrB,CAAC;EAED,oBACEV,OAAA,CAACC,WAAW,CAAC6C,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAApC,QAAA,EAChCA;EAAQ;IAAAuC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACzC,GAAA,CAxFWF,YAAyC;AAAA4C,EAAA,GAAzC5C,YAAyC;AAAA,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}