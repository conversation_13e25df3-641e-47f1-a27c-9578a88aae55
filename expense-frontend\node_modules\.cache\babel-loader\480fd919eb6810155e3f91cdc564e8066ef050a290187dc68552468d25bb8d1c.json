{"ast": null, "code": "/**\n * Circuit Breaker pattern implementation to prevent repeated failed API calls\n */\nvar CircuitState = /*#__PURE__*/function (CircuitState) {\n  CircuitState[\"CLOSED\"] = \"CLOSED\";\n  // Normal operation\n  CircuitState[\"OPEN\"] = \"OPEN\";\n  // Failing, reject calls\n  CircuitState[\"HALF_OPEN\"] = \"HALF_OPEN\"; // Testing if service recovered\n  return CircuitState;\n}(CircuitState || {});\nclass CircuitBreaker {\n  constructor(config) {\n    this.config = config;\n    this.state = CircuitState.CLOSED;\n    this.failures = [];\n    this.lastFailureTime = 0;\n    this.nextAttemptTime = 0;\n  }\n  async execute(operation, operationName = 'operation') {\n    if (this.state === CircuitState.OPEN) {\n      if (Date.now() < this.nextAttemptTime) {\n        throw new Error(`Circuit breaker is OPEN for ${operationName}. Next attempt allowed at ${new Date(this.nextAttemptTime).toLocaleTimeString()}`);\n      } else {\n        this.state = CircuitState.HALF_OPEN;\n        console.log(`Circuit breaker transitioning to HALF_OPEN for ${operationName}`);\n      }\n    }\n    try {\n      const result = await operation();\n      this.onSuccess(operationName);\n      return result;\n    } catch (error) {\n      this.onFailure(error, operationName);\n      throw error;\n    }\n  }\n  onSuccess(operationName) {\n    this.failures = [];\n    if (this.state === CircuitState.HALF_OPEN) {\n      this.state = CircuitState.CLOSED;\n      console.log(`Circuit breaker CLOSED for ${operationName} - service recovered`);\n    }\n  }\n  onFailure(error, operationName) {\n    const now = Date.now();\n    this.failures.push({\n      timestamp: now,\n      error: error.message\n    });\n    this.lastFailureTime = now;\n\n    // Remove old failures outside monitoring period\n    this.failures = this.failures.filter(failure => now - failure.timestamp < this.config.monitoringPeriod);\n\n    // Check if we should open the circuit\n    if (this.failures.length >= this.config.failureThreshold) {\n      this.state = CircuitState.OPEN;\n      this.nextAttemptTime = now + this.config.resetTimeout;\n      console.log(`Circuit breaker OPENED for ${operationName} - too many failures (${this.failures.length}/${this.config.failureThreshold})`);\n      console.log(`Next attempt allowed at: ${new Date(this.nextAttemptTime).toLocaleTimeString()}`);\n    }\n  }\n  getState() {\n    return this.state;\n  }\n  getFailureCount() {\n    const now = Date.now();\n    return this.failures.filter(failure => now - failure.timestamp < this.config.monitoringPeriod).length;\n  }\n  getNextAttemptTime() {\n    return this.nextAttemptTime;\n  }\n  reset() {\n    this.state = CircuitState.CLOSED;\n    this.failures = [];\n    this.nextAttemptTime = 0;\n    console.log('Circuit breaker manually reset');\n  }\n  getStatus() {\n    return {\n      state: this.state,\n      failureCount: this.getFailureCount(),\n      nextAttemptTime: this.nextAttemptTime,\n      lastFailureTime: this.lastFailureTime,\n      recentFailures: this.failures.slice(-5) // Last 5 failures\n    };\n  }\n}\n\n// Create circuit breakers for different services\nexport const apiCircuitBreaker = new CircuitBreaker({\n  failureThreshold: 5,\n  // Open after 5 failures\n  resetTimeout: 30000,\n  // Wait 30 seconds before trying again\n  monitoringPeriod: 60000 // Monitor failures in last 60 seconds\n});\nexport const websocketCircuitBreaker = new CircuitBreaker({\n  failureThreshold: 3,\n  // Open after 3 failures\n  resetTimeout: 15000,\n  // Wait 15 seconds before trying again\n  monitoringPeriod: 30000 // Monitor failures in last 30 seconds\n});\nexport const dashboardCircuitBreaker = new CircuitBreaker({\n  failureThreshold: 3,\n  // Open after 3 failures\n  resetTimeout: 20000,\n  // Wait 20 seconds before trying again\n  monitoringPeriod: 45000 // Monitor failures in last 45 seconds\n});\n\n// Utility function to wrap API calls with circuit breaker\nexport async function withCircuitBreaker(operation, circuitBreaker, operationName) {\n  return circuitBreaker.execute(operation, operationName);\n}\n\n// Export types and enums\nexport { CircuitBreaker, CircuitState };", "map": {"version": 3, "names": ["CircuitState", "CircuitBreaker", "constructor", "config", "state", "CLOSED", "failures", "lastFailureTime", "nextAttemptTime", "execute", "operation", "operationName", "OPEN", "Date", "now", "Error", "toLocaleTimeString", "HALF_OPEN", "console", "log", "result", "onSuccess", "error", "onFailure", "push", "timestamp", "message", "filter", "failure", "monitoringPeriod", "length", "failureT<PERSON><PERSON>old", "resetTimeout", "getState", "getFailureCount", "getNextAttemptTime", "reset", "getStatus", "failureCount", "recentFailures", "slice", "apiCircuitBreaker", "websocketCircuitBreaker", "dashboardCircuitBreaker", "withCircuitBreaker", "circuitBreaker"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/utils/circuitBreaker.ts"], "sourcesContent": ["/**\n * Circuit Breaker pattern implementation to prevent repeated failed API calls\n */\n\ninterface CircuitBreakerConfig {\n  failureThreshold: number;\n  resetTimeout: number;\n  monitoringPeriod: number;\n}\n\nenum CircuitState {\n  CLOSED = 'CLOSED',     // Normal operation\n  OPEN = 'OPEN',         // Failing, reject calls\n  HALF_OPEN = 'HALF_OPEN' // Testing if service recovered\n}\n\ninterface FailureRecord {\n  timestamp: number;\n  error: string;\n}\n\nclass CircuitBreaker {\n  private state: CircuitState = CircuitState.CLOSED;\n  private failures: FailureRecord[] = [];\n  private lastFailureTime: number = 0;\n  private nextAttemptTime: number = 0;\n\n  constructor(private config: CircuitBreakerConfig) {}\n\n  async execute<T>(operation: () => Promise<T>, operationName: string = 'operation'): Promise<T> {\n    if (this.state === CircuitState.OPEN) {\n      if (Date.now() < this.nextAttemptTime) {\n        throw new Error(`Circuit breaker is OPEN for ${operationName}. Next attempt allowed at ${new Date(this.nextAttemptTime).toLocaleTimeString()}`);\n      } else {\n        this.state = CircuitState.HALF_OPEN;\n        console.log(`Circuit breaker transitioning to HALF_OPEN for ${operationName}`);\n      }\n    }\n\n    try {\n      const result = await operation();\n      this.onSuccess(operationName);\n      return result;\n    } catch (error) {\n      this.onFailure(error as Error, operationName);\n      throw error;\n    }\n  }\n\n  private onSuccess(operationName: string): void {\n    this.failures = [];\n    if (this.state === CircuitState.HALF_OPEN) {\n      this.state = CircuitState.CLOSED;\n      console.log(`Circuit breaker CLOSED for ${operationName} - service recovered`);\n    }\n  }\n\n  private onFailure(error: Error, operationName: string): void {\n    const now = Date.now();\n    this.failures.push({\n      timestamp: now,\n      error: error.message\n    });\n    this.lastFailureTime = now;\n\n    // Remove old failures outside monitoring period\n    this.failures = this.failures.filter(\n      failure => now - failure.timestamp < this.config.monitoringPeriod\n    );\n\n    // Check if we should open the circuit\n    if (this.failures.length >= this.config.failureThreshold) {\n      this.state = CircuitState.OPEN;\n      this.nextAttemptTime = now + this.config.resetTimeout;\n      console.log(`Circuit breaker OPENED for ${operationName} - too many failures (${this.failures.length}/${this.config.failureThreshold})`);\n      console.log(`Next attempt allowed at: ${new Date(this.nextAttemptTime).toLocaleTimeString()}`);\n    }\n  }\n\n  getState(): CircuitState {\n    return this.state;\n  }\n\n  getFailureCount(): number {\n    const now = Date.now();\n    return this.failures.filter(\n      failure => now - failure.timestamp < this.config.monitoringPeriod\n    ).length;\n  }\n\n  getNextAttemptTime(): number {\n    return this.nextAttemptTime;\n  }\n\n  reset(): void {\n    this.state = CircuitState.CLOSED;\n    this.failures = [];\n    this.nextAttemptTime = 0;\n    console.log('Circuit breaker manually reset');\n  }\n\n  getStatus() {\n    return {\n      state: this.state,\n      failureCount: this.getFailureCount(),\n      nextAttemptTime: this.nextAttemptTime,\n      lastFailureTime: this.lastFailureTime,\n      recentFailures: this.failures.slice(-5) // Last 5 failures\n    };\n  }\n}\n\n// Create circuit breakers for different services\nexport const apiCircuitBreaker = new CircuitBreaker({\n  failureThreshold: 5,      // Open after 5 failures\n  resetTimeout: 30000,      // Wait 30 seconds before trying again\n  monitoringPeriod: 60000   // Monitor failures in last 60 seconds\n});\n\nexport const websocketCircuitBreaker = new CircuitBreaker({\n  failureThreshold: 3,      // Open after 3 failures\n  resetTimeout: 15000,      // Wait 15 seconds before trying again\n  monitoringPeriod: 30000   // Monitor failures in last 30 seconds\n});\n\nexport const dashboardCircuitBreaker = new CircuitBreaker({\n  failureThreshold: 3,      // Open after 3 failures\n  resetTimeout: 20000,      // Wait 20 seconds before trying again\n  monitoringPeriod: 45000   // Monitor failures in last 45 seconds\n});\n\n// Utility function to wrap API calls with circuit breaker\nexport async function withCircuitBreaker<T>(\n  operation: () => Promise<T>,\n  circuitBreaker: CircuitBreaker,\n  operationName: string\n): Promise<T> {\n  return circuitBreaker.execute(operation, operationName);\n}\n\n// Export types and enums\nexport { CircuitBreaker, CircuitState };\nexport type { CircuitBreakerConfig, FailureRecord };\n"], "mappings": "AAAA;AACA;AACA;AAFA,IAUKA,YAAY,0BAAZA,YAAY;EAAZA,YAAY;EACQ;EADpBA,YAAY;EAEQ;EAFpBA,YAAY,6BAGS;EAAA,OAHrBA,YAAY;AAAA,EAAZA,YAAY;AAWjB,MAAMC,cAAc,CAAC;EAMnBC,WAAWA,CAASC,MAA4B,EAAE;IAAA,KAA9BA,MAA4B,GAA5BA,MAA4B;IAAA,KALxCC,KAAK,GAAiBJ,YAAY,CAACK,MAAM;IAAA,KACzCC,QAAQ,GAAoB,EAAE;IAAA,KAC9BC,eAAe,GAAW,CAAC;IAAA,KAC3BC,eAAe,GAAW,CAAC;EAEgB;EAEnD,MAAMC,OAAOA,CAAIC,SAA2B,EAAEC,aAAqB,GAAG,WAAW,EAAc;IAC7F,IAAI,IAAI,CAACP,KAAK,KAAKJ,YAAY,CAACY,IAAI,EAAE;MACpC,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACN,eAAe,EAAE;QACrC,MAAM,IAAIO,KAAK,CAAC,+BAA+BJ,aAAa,6BAA6B,IAAIE,IAAI,CAAC,IAAI,CAACL,eAAe,CAAC,CAACQ,kBAAkB,CAAC,CAAC,EAAE,CAAC;MACjJ,CAAC,MAAM;QACL,IAAI,CAACZ,KAAK,GAAGJ,YAAY,CAACiB,SAAS;QACnCC,OAAO,CAACC,GAAG,CAAC,kDAAkDR,aAAa,EAAE,CAAC;MAChF;IACF;IAEA,IAAI;MACF,MAAMS,MAAM,GAAG,MAAMV,SAAS,CAAC,CAAC;MAChC,IAAI,CAACW,SAAS,CAACV,aAAa,CAAC;MAC7B,OAAOS,MAAM;IACf,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,IAAI,CAACC,SAAS,CAACD,KAAK,EAAWX,aAAa,CAAC;MAC7C,MAAMW,KAAK;IACb;EACF;EAEQD,SAASA,CAACV,aAAqB,EAAQ;IAC7C,IAAI,CAACL,QAAQ,GAAG,EAAE;IAClB,IAAI,IAAI,CAACF,KAAK,KAAKJ,YAAY,CAACiB,SAAS,EAAE;MACzC,IAAI,CAACb,KAAK,GAAGJ,YAAY,CAACK,MAAM;MAChCa,OAAO,CAACC,GAAG,CAAC,8BAA8BR,aAAa,sBAAsB,CAAC;IAChF;EACF;EAEQY,SAASA,CAACD,KAAY,EAAEX,aAAqB,EAAQ;IAC3D,MAAMG,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACR,QAAQ,CAACkB,IAAI,CAAC;MACjBC,SAAS,EAAEX,GAAG;MACdQ,KAAK,EAAEA,KAAK,CAACI;IACf,CAAC,CAAC;IACF,IAAI,CAACnB,eAAe,GAAGO,GAAG;;IAE1B;IACA,IAAI,CAACR,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACqB,MAAM,CAClCC,OAAO,IAAId,GAAG,GAAGc,OAAO,CAACH,SAAS,GAAG,IAAI,CAACtB,MAAM,CAAC0B,gBACnD,CAAC;;IAED;IACA,IAAI,IAAI,CAACvB,QAAQ,CAACwB,MAAM,IAAI,IAAI,CAAC3B,MAAM,CAAC4B,gBAAgB,EAAE;MACxD,IAAI,CAAC3B,KAAK,GAAGJ,YAAY,CAACY,IAAI;MAC9B,IAAI,CAACJ,eAAe,GAAGM,GAAG,GAAG,IAAI,CAACX,MAAM,CAAC6B,YAAY;MACrDd,OAAO,CAACC,GAAG,CAAC,8BAA8BR,aAAa,yBAAyB,IAAI,CAACL,QAAQ,CAACwB,MAAM,IAAI,IAAI,CAAC3B,MAAM,CAAC4B,gBAAgB,GAAG,CAAC;MACxIb,OAAO,CAACC,GAAG,CAAC,4BAA4B,IAAIN,IAAI,CAAC,IAAI,CAACL,eAAe,CAAC,CAACQ,kBAAkB,CAAC,CAAC,EAAE,CAAC;IAChG;EACF;EAEAiB,QAAQA,CAAA,EAAiB;IACvB,OAAO,IAAI,CAAC7B,KAAK;EACnB;EAEA8B,eAAeA,CAAA,EAAW;IACxB,MAAMpB,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;IACtB,OAAO,IAAI,CAACR,QAAQ,CAACqB,MAAM,CACzBC,OAAO,IAAId,GAAG,GAAGc,OAAO,CAACH,SAAS,GAAG,IAAI,CAACtB,MAAM,CAAC0B,gBACnD,CAAC,CAACC,MAAM;EACV;EAEAK,kBAAkBA,CAAA,EAAW;IAC3B,OAAO,IAAI,CAAC3B,eAAe;EAC7B;EAEA4B,KAAKA,CAAA,EAAS;IACZ,IAAI,CAAChC,KAAK,GAAGJ,YAAY,CAACK,MAAM;IAChC,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACE,eAAe,GAAG,CAAC;IACxBU,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;EAC/C;EAEAkB,SAASA,CAAA,EAAG;IACV,OAAO;MACLjC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBkC,YAAY,EAAE,IAAI,CAACJ,eAAe,CAAC,CAAC;MACpC1B,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCD,eAAe,EAAE,IAAI,CAACA,eAAe;MACrCgC,cAAc,EAAE,IAAI,CAACjC,QAAQ,CAACkC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC;EACH;AACF;;AAEA;AACA,OAAO,MAAMC,iBAAiB,GAAG,IAAIxC,cAAc,CAAC;EAClD8B,gBAAgB,EAAE,CAAC;EAAO;EAC1BC,YAAY,EAAE,KAAK;EAAO;EAC1BH,gBAAgB,EAAE,KAAK,CAAG;AAC5B,CAAC,CAAC;AAEF,OAAO,MAAMa,uBAAuB,GAAG,IAAIzC,cAAc,CAAC;EACxD8B,gBAAgB,EAAE,CAAC;EAAO;EAC1BC,YAAY,EAAE,KAAK;EAAO;EAC1BH,gBAAgB,EAAE,KAAK,CAAG;AAC5B,CAAC,CAAC;AAEF,OAAO,MAAMc,uBAAuB,GAAG,IAAI1C,cAAc,CAAC;EACxD8B,gBAAgB,EAAE,CAAC;EAAO;EAC1BC,YAAY,EAAE,KAAK;EAAO;EAC1BH,gBAAgB,EAAE,KAAK,CAAG;AAC5B,CAAC,CAAC;;AAEF;AACA,OAAO,eAAee,kBAAkBA,CACtClC,SAA2B,EAC3BmC,cAA8B,EAC9BlC,aAAqB,EACT;EACZ,OAAOkC,cAAc,CAACpC,OAAO,CAACC,SAAS,EAAEC,aAAa,CAAC;AACzD;;AAEA;AACA,SAASV,cAAc,EAAED,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}