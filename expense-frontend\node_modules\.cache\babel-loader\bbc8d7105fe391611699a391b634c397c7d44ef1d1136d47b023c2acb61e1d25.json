{"ast": null, "code": "/**\n * Real-time notification service for expense tracking application\n */\n\nimport toast from 'react-hot-toast';\n\n// WebSocketMessage interface removed - now handled by WebSocketContext\n\nclass NotificationService {\n  constructor() {\n    this.notifications = [];\n    this.listeners = [];\n    this.refreshCallbacks = [];\n  }\n  // WebSocket-related properties removed - now handled by WebSocketContext\n\n  addNotification(notification) {\n    const newNotification = {\n      ...notification,\n      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n      timestamp: new Date(),\n      read: false\n    };\n    this.notifications.unshift(newNotification);\n\n    // Keep only last 50 notifications\n    if (this.notifications.length > 50) {\n      this.notifications = this.notifications.slice(0, 50);\n    }\n\n    // Show toast notification\n    this.showToast(newNotification);\n\n    // Notify listeners\n    this.notifyListeners();\n\n    // Trigger data refresh\n    this.triggerRefresh();\n  }\n  showToast(notification) {\n    const toastOptions = {\n      duration: 4000,\n      position: 'top-right'\n    };\n    switch (notification.type) {\n      case 'expense_added':\n        toast.success(notification.message, toastOptions);\n        break;\n      case 'approval_required':\n        toast(notification.message, {\n          ...toastOptions,\n          icon: '⏳'\n        });\n        break;\n      case 'settlement_pending':\n        toast(notification.message, {\n          ...toastOptions,\n          icon: '💰'\n        });\n        break;\n      case 'request_approved':\n        toast.success(notification.message, toastOptions);\n        break;\n      case 'request_rejected':\n        toast.error(notification.message, toastOptions);\n        break;\n      default:\n        toast(notification.message, toastOptions);\n    }\n  }\n  getNotifications() {\n    return [...this.notifications];\n  }\n  getUnreadCount() {\n    return this.notifications.filter(n => !n.read).length;\n  }\n  markAsRead(notificationId) {\n    const notification = this.notifications.find(n => n.id === notificationId);\n    if (notification) {\n      notification.read = true;\n      this.notifyListeners();\n    }\n  }\n  markAllAsRead() {\n    this.notifications.forEach(n => n.read = true);\n    this.notifyListeners();\n  }\n  subscribe(callback) {\n    this.listeners.push(callback);\n\n    // Return unsubscribe function\n    return () => {\n      this.listeners = this.listeners.filter(listener => listener !== callback);\n    };\n  }\n  subscribeToRefresh(callback) {\n    this.refreshCallbacks.push(callback);\n\n    // Return unsubscribe function\n    return () => {\n      this.refreshCallbacks = this.refreshCallbacks.filter(cb => cb !== callback);\n    };\n  }\n  notifyListeners() {\n    this.listeners.forEach(listener => listener([...this.notifications]));\n  }\n  triggerRefresh() {\n    this.refreshCallbacks.forEach(callback => callback());\n  }\n\n  // Real notification methods for actual user activities\n  notifyExpenseCreated(expenseData) {\n    this.addNotification({\n      type: 'expense_added',\n      title: 'Expense Created',\n      message: `You created \"${expenseData.description}\" for $${expenseData.amount.toFixed(2)}${expenseData.groupName ? ` in ${expenseData.groupName}` : ''}`,\n      data: expenseData\n    });\n  }\n  notifyExpenseNeedsApproval(expenseData) {\n    this.addNotification({\n      type: 'approval_required',\n      title: 'Approval Required',\n      message: `${expenseData.payer} added \"${expenseData.description}\" for $${expenseData.amount.toFixed(2)} - requires your approval`,\n      data: expenseData\n    });\n  }\n  notifySettlementReceived(settlementData) {\n    this.addNotification({\n      type: 'settlement_pending',\n      title: 'Settlement Received',\n      message: `${settlementData.payer} sent you $${settlementData.amount.toFixed(2)} - please confirm receipt`,\n      data: settlementData\n    });\n  }\n  notifySettlementSent(settlementData) {\n    this.addNotification({\n      type: 'expense_added',\n      title: 'Settlement Sent',\n      message: `Settlement of $${settlementData.amount.toFixed(2)} sent to ${settlementData.recipient} for confirmation`,\n      data: settlementData\n    });\n  }\n  notifyJoinRequestApproved(requestData) {\n    this.addNotification({\n      type: 'request_approved',\n      title: 'Join Request Approved',\n      message: `Your request to join \"${requestData.groupName}\" has been approved`,\n      data: requestData\n    });\n  }\n  notifyJoinRequestRejected(requestData) {\n    this.addNotification({\n      type: 'request_rejected',\n      title: 'Join Request Rejected',\n      message: `Your request to join \"${requestData.groupName}\" has been rejected`,\n      data: requestData\n    });\n  }\n  notifyExpenseApproved(expenseData) {\n    this.addNotification({\n      type: 'expense_added',\n      title: 'Expense Approved',\n      message: `${expenseData.approver} approved \"${expenseData.description}\" for $${expenseData.amount.toFixed(2)}`,\n      data: expenseData\n    });\n  }\n  notifySettlementConfirmed(settlementData) {\n    this.addNotification({\n      type: 'expense_added',\n      title: 'Settlement Confirmed',\n      message: `${settlementData.confirmer} confirmed receipt of $${settlementData.amount.toFixed(2)}`,\n      data: settlementData\n    });\n  }\n\n  // WebSocket functionality has been moved to WebSocketContext\n  // This service now focuses only on local notification management\n\n  // Legacy methods kept for backward compatibility but no longer create connections\n  connectWebSocket(token) {\n    console.log('NotificationService: WebSocket connection is now handled by WebSocketContext');\n    // No-op: WebSocketContext handles all WebSocket connections\n  }\n  disconnectWebSocket() {\n    console.log('NotificationService: WebSocket disconnection is now handled by WebSocketContext');\n    // No-op: WebSocketContext handles all WebSocket connections\n  }\n  isWebSocketConnected() {\n    console.log('NotificationService: WebSocket status is now available through WebSocketContext');\n    return false; // Always return false since this service no longer manages connections\n  }\n}\n\n// Create singleton instance\nexport const notificationService = new NotificationService();\nexport default notificationService;", "map": {"version": 3, "names": ["toast", "NotificationService", "constructor", "notifications", "listeners", "refreshCallbacks", "addNotification", "notification", "newNotification", "id", "Date", "now", "toString", "Math", "random", "substr", "timestamp", "read", "unshift", "length", "slice", "showToast", "notifyListeners", "triggerRefresh", "toastOptions", "duration", "position", "type", "success", "message", "icon", "error", "getNotifications", "getUnreadCount", "filter", "n", "mark<PERSON><PERSON><PERSON>", "notificationId", "find", "markAllAsRead", "for<PERSON>ach", "subscribe", "callback", "push", "listener", "subscribeToRefresh", "cb", "notifyExpenseCreated", "expenseData", "title", "description", "amount", "toFixed", "groupName", "data", "notifyExpenseNeedsApproval", "payer", "notifySettlementReceived", "settlementData", "notifySettlementSent", "recipient", "notifyJoinRequestApproved", "requestData", "notifyJoinRequestRejected", "notifyExpenseApproved", "approver", "notifySettlementConfirmed", "confirmer", "connectWebSocket", "token", "console", "log", "disconnectWebSocket", "isWebSocketConnected", "notificationService"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/services/notificationService.ts"], "sourcesContent": ["/**\n * Real-time notification service for expense tracking application\n */\n\nimport toast from 'react-hot-toast';\n\n// WebSocketMessage interface removed - now handled by WebSocketContext\n\nexport interface AppNotification {\n  id: string;\n  type: 'expense_added' | 'approval_required' | 'settlement_pending' | 'request_approved' | 'request_rejected';\n  title: string;\n  message: string;\n  timestamp: Date;\n  read: boolean;\n  data?: any;\n}\n\nclass NotificationService {\n  private notifications: AppNotification[] = [];\n  private listeners: ((notifications: AppNotification[]) => void)[] = [];\n  private refreshCallbacks: (() => void)[] = [];\n\n  // WebSocket-related properties removed - now handled by WebSocketContext\n\n  addNotification(notification: Omit<AppNotification, 'id' | 'timestamp' | 'read'>) {\n    const newNotification: AppNotification = {\n      ...notification,\n      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n      timestamp: new Date(),\n      read: false\n    };\n\n    this.notifications.unshift(newNotification);\n    \n    // Keep only last 50 notifications\n    if (this.notifications.length > 50) {\n      this.notifications = this.notifications.slice(0, 50);\n    }\n\n    // Show toast notification\n    this.showToast(newNotification);\n\n    // Notify listeners\n    this.notifyListeners();\n\n    // Trigger data refresh\n    this.triggerRefresh();\n  }\n\n  private showToast(notification: AppNotification) {\n    const toastOptions = {\n      duration: 4000,\n      position: 'top-right' as const,\n    };\n\n    switch (notification.type) {\n      case 'expense_added':\n        toast.success(notification.message, toastOptions);\n        break;\n      case 'approval_required':\n        toast(notification.message, {\n          ...toastOptions,\n          icon: '⏳',\n        });\n        break;\n      case 'settlement_pending':\n        toast(notification.message, {\n          ...toastOptions,\n          icon: '💰',\n        });\n        break;\n      case 'request_approved':\n        toast.success(notification.message, toastOptions);\n        break;\n      case 'request_rejected':\n        toast.error(notification.message, toastOptions);\n        break;\n      default:\n        toast(notification.message, toastOptions);\n    }\n  }\n\n  getNotifications(): AppNotification[] {\n    return [...this.notifications];\n  }\n\n  getUnreadCount(): number {\n    return this.notifications.filter(n => !n.read).length;\n  }\n\n  markAsRead(notificationId: string) {\n    const notification = this.notifications.find(n => n.id === notificationId);\n    if (notification) {\n      notification.read = true;\n      this.notifyListeners();\n    }\n  }\n\n  markAllAsRead() {\n    this.notifications.forEach(n => n.read = true);\n    this.notifyListeners();\n  }\n\n  subscribe(callback: (notifications: AppNotification[]) => void) {\n    this.listeners.push(callback);\n    \n    // Return unsubscribe function\n    return () => {\n      this.listeners = this.listeners.filter(listener => listener !== callback);\n    };\n  }\n\n  subscribeToRefresh(callback: () => void) {\n    this.refreshCallbacks.push(callback);\n    \n    // Return unsubscribe function\n    return () => {\n      this.refreshCallbacks = this.refreshCallbacks.filter(cb => cb !== callback);\n    };\n  }\n\n  private notifyListeners() {\n    this.listeners.forEach(listener => listener([...this.notifications]));\n  }\n\n  private triggerRefresh() {\n    this.refreshCallbacks.forEach(callback => callback());\n  }\n\n  // Real notification methods for actual user activities\n  notifyExpenseCreated(expenseData: { description: string; amount: number; payer: string; groupName?: string }) {\n    this.addNotification({\n      type: 'expense_added',\n      title: 'Expense Created',\n      message: `You created \"${expenseData.description}\" for $${expenseData.amount.toFixed(2)}${expenseData.groupName ? ` in ${expenseData.groupName}` : ''}`,\n      data: expenseData\n    });\n  }\n\n  notifyExpenseNeedsApproval(expenseData: { description: string; amount: number; payer: string; groupName?: string }) {\n    this.addNotification({\n      type: 'approval_required',\n      title: 'Approval Required',\n      message: `${expenseData.payer} added \"${expenseData.description}\" for $${expenseData.amount.toFixed(2)} - requires your approval`,\n      data: expenseData\n    });\n  }\n\n  notifySettlementReceived(settlementData: { amount: number; payer: string }) {\n    this.addNotification({\n      type: 'settlement_pending',\n      title: 'Settlement Received',\n      message: `${settlementData.payer} sent you $${settlementData.amount.toFixed(2)} - please confirm receipt`,\n      data: settlementData\n    });\n  }\n\n  notifySettlementSent(settlementData: { amount: number; recipient: string }) {\n    this.addNotification({\n      type: 'expense_added',\n      title: 'Settlement Sent',\n      message: `Settlement of $${settlementData.amount.toFixed(2)} sent to ${settlementData.recipient} for confirmation`,\n      data: settlementData\n    });\n  }\n\n  notifyJoinRequestApproved(requestData: { groupName: string }) {\n    this.addNotification({\n      type: 'request_approved',\n      title: 'Join Request Approved',\n      message: `Your request to join \"${requestData.groupName}\" has been approved`,\n      data: requestData\n    });\n  }\n\n  notifyJoinRequestRejected(requestData: { groupName: string }) {\n    this.addNotification({\n      type: 'request_rejected',\n      title: 'Join Request Rejected',\n      message: `Your request to join \"${requestData.groupName}\" has been rejected`,\n      data: requestData\n    });\n  }\n\n  notifyExpenseApproved(expenseData: { description: string; amount: number; approver: string }) {\n    this.addNotification({\n      type: 'expense_added',\n      title: 'Expense Approved',\n      message: `${expenseData.approver} approved \"${expenseData.description}\" for $${expenseData.amount.toFixed(2)}`,\n      data: expenseData\n    });\n  }\n\n  notifySettlementConfirmed(settlementData: { amount: number; confirmer: string }) {\n    this.addNotification({\n      type: 'expense_added',\n      title: 'Settlement Confirmed',\n      message: `${settlementData.confirmer} confirmed receipt of $${settlementData.amount.toFixed(2)}`,\n      data: settlementData\n    });\n  }\n\n  // WebSocket functionality has been moved to WebSocketContext\n  // This service now focuses only on local notification management\n\n  // Legacy methods kept for backward compatibility but no longer create connections\n  connectWebSocket(token: string) {\n    console.log('NotificationService: WebSocket connection is now handled by WebSocketContext');\n    // No-op: WebSocketContext handles all WebSocket connections\n  }\n\n  disconnectWebSocket() {\n    console.log('NotificationService: WebSocket disconnection is now handled by WebSocketContext');\n    // No-op: WebSocketContext handles all WebSocket connections\n  }\n\n  isWebSocketConnected(): boolean {\n    console.log('NotificationService: WebSocket status is now available through WebSocketContext');\n    return false; // Always return false since this service no longer manages connections\n  }\n}\n\n// Create singleton instance\nexport const notificationService = new NotificationService();\n\nexport default notificationService;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,iBAAiB;;AAEnC;;AAYA,MAAMC,mBAAmB,CAAC;EAAAC,YAAA;IAAA,KAChBC,aAAa,GAAsB,EAAE;IAAA,KACrCC,SAAS,GAAmD,EAAE;IAAA,KAC9DC,gBAAgB,GAAmB,EAAE;EAAA;EAE7C;;EAEAC,eAAeA,CAACC,YAAgE,EAAE;IAChF,MAAMC,eAAgC,GAAG;MACvC,GAAGD,YAAY;MACfE,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MACnEC,SAAS,EAAE,IAAIN,IAAI,CAAC,CAAC;MACrBO,IAAI,EAAE;IACR,CAAC;IAED,IAAI,CAACd,aAAa,CAACe,OAAO,CAACV,eAAe,CAAC;;IAE3C;IACA,IAAI,IAAI,CAACL,aAAa,CAACgB,MAAM,GAAG,EAAE,EAAE;MAClC,IAAI,CAAChB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACiB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IACtD;;IAEA;IACA,IAAI,CAACC,SAAS,CAACb,eAAe,CAAC;;IAE/B;IACA,IAAI,CAACc,eAAe,CAAC,CAAC;;IAEtB;IACA,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;EAEQF,SAASA,CAACd,YAA6B,EAAE;IAC/C,MAAMiB,YAAY,GAAG;MACnBC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;IACZ,CAAC;IAED,QAAQnB,YAAY,CAACoB,IAAI;MACvB,KAAK,eAAe;QAClB3B,KAAK,CAAC4B,OAAO,CAACrB,YAAY,CAACsB,OAAO,EAAEL,YAAY,CAAC;QACjD;MACF,KAAK,mBAAmB;QACtBxB,KAAK,CAACO,YAAY,CAACsB,OAAO,EAAE;UAC1B,GAAGL,YAAY;UACfM,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF,KAAK,oBAAoB;QACvB9B,KAAK,CAACO,YAAY,CAACsB,OAAO,EAAE;UAC1B,GAAGL,YAAY;UACfM,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF,KAAK,kBAAkB;QACrB9B,KAAK,CAAC4B,OAAO,CAACrB,YAAY,CAACsB,OAAO,EAAEL,YAAY,CAAC;QACjD;MACF,KAAK,kBAAkB;QACrBxB,KAAK,CAAC+B,KAAK,CAACxB,YAAY,CAACsB,OAAO,EAAEL,YAAY,CAAC;QAC/C;MACF;QACExB,KAAK,CAACO,YAAY,CAACsB,OAAO,EAAEL,YAAY,CAAC;IAC7C;EACF;EAEAQ,gBAAgBA,CAAA,EAAsB;IACpC,OAAO,CAAC,GAAG,IAAI,CAAC7B,aAAa,CAAC;EAChC;EAEA8B,cAAcA,CAAA,EAAW;IACvB,OAAO,IAAI,CAAC9B,aAAa,CAAC+B,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAClB,IAAI,CAAC,CAACE,MAAM;EACvD;EAEAiB,UAAUA,CAACC,cAAsB,EAAE;IACjC,MAAM9B,YAAY,GAAG,IAAI,CAACJ,aAAa,CAACmC,IAAI,CAACH,CAAC,IAAIA,CAAC,CAAC1B,EAAE,KAAK4B,cAAc,CAAC;IAC1E,IAAI9B,YAAY,EAAE;MAChBA,YAAY,CAACU,IAAI,GAAG,IAAI;MACxB,IAAI,CAACK,eAAe,CAAC,CAAC;IACxB;EACF;EAEAiB,aAAaA,CAAA,EAAG;IACd,IAAI,CAACpC,aAAa,CAACqC,OAAO,CAACL,CAAC,IAAIA,CAAC,CAAClB,IAAI,GAAG,IAAI,CAAC;IAC9C,IAAI,CAACK,eAAe,CAAC,CAAC;EACxB;EAEAmB,SAASA,CAACC,QAAoD,EAAE;IAC9D,IAAI,CAACtC,SAAS,CAACuC,IAAI,CAACD,QAAQ,CAAC;;IAE7B;IACA,OAAO,MAAM;MACX,IAAI,CAACtC,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC8B,MAAM,CAACU,QAAQ,IAAIA,QAAQ,KAAKF,QAAQ,CAAC;IAC3E,CAAC;EACH;EAEAG,kBAAkBA,CAACH,QAAoB,EAAE;IACvC,IAAI,CAACrC,gBAAgB,CAACsC,IAAI,CAACD,QAAQ,CAAC;;IAEpC;IACA,OAAO,MAAM;MACX,IAAI,CAACrC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAAC6B,MAAM,CAACY,EAAE,IAAIA,EAAE,KAAKJ,QAAQ,CAAC;IAC7E,CAAC;EACH;EAEQpB,eAAeA,CAAA,EAAG;IACxB,IAAI,CAAClB,SAAS,CAACoC,OAAO,CAACI,QAAQ,IAAIA,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACzC,aAAa,CAAC,CAAC,CAAC;EACvE;EAEQoB,cAAcA,CAAA,EAAG;IACvB,IAAI,CAAClB,gBAAgB,CAACmC,OAAO,CAACE,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC;EACvD;;EAEA;EACAK,oBAAoBA,CAACC,WAAuF,EAAE;IAC5G,IAAI,CAAC1C,eAAe,CAAC;MACnBqB,IAAI,EAAE,eAAe;MACrBsB,KAAK,EAAE,iBAAiB;MACxBpB,OAAO,EAAE,gBAAgBmB,WAAW,CAACE,WAAW,UAAUF,WAAW,CAACG,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,GAAGJ,WAAW,CAACK,SAAS,GAAG,OAAOL,WAAW,CAACK,SAAS,EAAE,GAAG,EAAE,EAAE;MACvJC,IAAI,EAAEN;IACR,CAAC,CAAC;EACJ;EAEAO,0BAA0BA,CAACP,WAAuF,EAAE;IAClH,IAAI,CAAC1C,eAAe,CAAC;MACnBqB,IAAI,EAAE,mBAAmB;MACzBsB,KAAK,EAAE,mBAAmB;MAC1BpB,OAAO,EAAE,GAAGmB,WAAW,CAACQ,KAAK,WAAWR,WAAW,CAACE,WAAW,UAAUF,WAAW,CAACG,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,2BAA2B;MACjIE,IAAI,EAAEN;IACR,CAAC,CAAC;EACJ;EAEAS,wBAAwBA,CAACC,cAAiD,EAAE;IAC1E,IAAI,CAACpD,eAAe,CAAC;MACnBqB,IAAI,EAAE,oBAAoB;MAC1BsB,KAAK,EAAE,qBAAqB;MAC5BpB,OAAO,EAAE,GAAG6B,cAAc,CAACF,KAAK,cAAcE,cAAc,CAACP,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,2BAA2B;MACzGE,IAAI,EAAEI;IACR,CAAC,CAAC;EACJ;EAEAC,oBAAoBA,CAACD,cAAqD,EAAE;IAC1E,IAAI,CAACpD,eAAe,CAAC;MACnBqB,IAAI,EAAE,eAAe;MACrBsB,KAAK,EAAE,iBAAiB;MACxBpB,OAAO,EAAE,kBAAkB6B,cAAc,CAACP,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,YAAYM,cAAc,CAACE,SAAS,mBAAmB;MAClHN,IAAI,EAAEI;IACR,CAAC,CAAC;EACJ;EAEAG,yBAAyBA,CAACC,WAAkC,EAAE;IAC5D,IAAI,CAACxD,eAAe,CAAC;MACnBqB,IAAI,EAAE,kBAAkB;MACxBsB,KAAK,EAAE,uBAAuB;MAC9BpB,OAAO,EAAE,yBAAyBiC,WAAW,CAACT,SAAS,qBAAqB;MAC5EC,IAAI,EAAEQ;IACR,CAAC,CAAC;EACJ;EAEAC,yBAAyBA,CAACD,WAAkC,EAAE;IAC5D,IAAI,CAACxD,eAAe,CAAC;MACnBqB,IAAI,EAAE,kBAAkB;MACxBsB,KAAK,EAAE,uBAAuB;MAC9BpB,OAAO,EAAE,yBAAyBiC,WAAW,CAACT,SAAS,qBAAqB;MAC5EC,IAAI,EAAEQ;IACR,CAAC,CAAC;EACJ;EAEAE,qBAAqBA,CAAChB,WAAsE,EAAE;IAC5F,IAAI,CAAC1C,eAAe,CAAC;MACnBqB,IAAI,EAAE,eAAe;MACrBsB,KAAK,EAAE,kBAAkB;MACzBpB,OAAO,EAAE,GAAGmB,WAAW,CAACiB,QAAQ,cAAcjB,WAAW,CAACE,WAAW,UAAUF,WAAW,CAACG,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE;MAC9GE,IAAI,EAAEN;IACR,CAAC,CAAC;EACJ;EAEAkB,yBAAyBA,CAACR,cAAqD,EAAE;IAC/E,IAAI,CAACpD,eAAe,CAAC;MACnBqB,IAAI,EAAE,eAAe;MACrBsB,KAAK,EAAE,sBAAsB;MAC7BpB,OAAO,EAAE,GAAG6B,cAAc,CAACS,SAAS,0BAA0BT,cAAc,CAACP,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE;MAChGE,IAAI,EAAEI;IACR,CAAC,CAAC;EACJ;;EAEA;EACA;;EAEA;EACAU,gBAAgBA,CAACC,KAAa,EAAE;IAC9BC,OAAO,CAACC,GAAG,CAAC,8EAA8E,CAAC;IAC3F;EACF;EAEAC,mBAAmBA,CAAA,EAAG;IACpBF,OAAO,CAACC,GAAG,CAAC,iFAAiF,CAAC;IAC9F;EACF;EAEAE,oBAAoBA,CAAA,EAAY;IAC9BH,OAAO,CAACC,GAAG,CAAC,iFAAiF,CAAC;IAC9F,OAAO,KAAK,CAAC,CAAC;EAChB;AACF;;AAEA;AACA,OAAO,MAAMG,mBAAmB,GAAG,IAAIzE,mBAAmB,CAAC,CAAC;AAE5D,eAAeyE,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}