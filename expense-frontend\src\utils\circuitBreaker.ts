/**
 * Circuit Breaker pattern implementation to prevent repeated failed API calls
 */

interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeout: number;
  monitoringPeriod: number;
}

enum CircuitState {
  CLOSED = 'CLOSED',     // Normal operation
  OPEN = 'OPEN',         // Failing, reject calls
  HALF_OPEN = 'HALF_OPEN' // Testing if service recovered
}

interface FailureRecord {
  timestamp: number;
  error: string;
}

class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private failures: FailureRecord[] = [];
  private lastFailureTime: number = 0;
  private nextAttemptTime: number = 0;

  constructor(private config: CircuitBreakerConfig) {}

  async execute<T>(operation: () => Promise<T>, operationName: string = 'operation'): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (Date.now() < this.nextAttemptTime) {
        throw new Error(`Circuit breaker is OPEN for ${operationName}. Next attempt allowed at ${new Date(this.nextAttemptTime).toLocaleTimeString()}`);
      } else {
        this.state = CircuitState.HALF_OPEN;
        console.log(`Circuit breaker transitioning to HALF_OPEN for ${operationName}`);
      }
    }

    try {
      const result = await operation();
      this.onSuccess(operationName);
      return result;
    } catch (error) {
      this.onFailure(error as Error, operationName);
      throw error;
    }
  }

  private onSuccess(operationName: string): void {
    this.failures = [];
    if (this.state === CircuitState.HALF_OPEN) {
      this.state = CircuitState.CLOSED;
      console.log(`Circuit breaker CLOSED for ${operationName} - service recovered`);
    }
  }

  private onFailure(error: Error, operationName: string): void {
    const now = Date.now();
    this.failures.push({
      timestamp: now,
      error: error.message
    });
    this.lastFailureTime = now;

    // Remove old failures outside monitoring period
    this.failures = this.failures.filter(
      failure => now - failure.timestamp < this.config.monitoringPeriod
    );

    // Check if we should open the circuit
    if (this.failures.length >= this.config.failureThreshold) {
      this.state = CircuitState.OPEN;
      this.nextAttemptTime = now + this.config.resetTimeout;
      console.log(`Circuit breaker OPENED for ${operationName} - too many failures (${this.failures.length}/${this.config.failureThreshold})`);
      console.log(`Next attempt allowed at: ${new Date(this.nextAttemptTime).toLocaleTimeString()}`);
    }
  }

  getState(): CircuitState {
    return this.state;
  }

  getFailureCount(): number {
    const now = Date.now();
    return this.failures.filter(
      failure => now - failure.timestamp < this.config.monitoringPeriod
    ).length;
  }

  getNextAttemptTime(): number {
    return this.nextAttemptTime;
  }

  reset(): void {
    this.state = CircuitState.CLOSED;
    this.failures = [];
    this.nextAttemptTime = 0;
    console.log('Circuit breaker manually reset');
  }

  getStatus() {
    return {
      state: this.state,
      failureCount: this.getFailureCount(),
      nextAttemptTime: this.nextAttemptTime,
      lastFailureTime: this.lastFailureTime,
      recentFailures: this.failures.slice(-5) // Last 5 failures
    };
  }
}

// Create circuit breakers for different services
export const apiCircuitBreaker = new CircuitBreaker({
  failureThreshold: 5,      // Open after 5 failures
  resetTimeout: 30000,      // Wait 30 seconds before trying again
  monitoringPeriod: 60000   // Monitor failures in last 60 seconds
});

export const websocketCircuitBreaker = new CircuitBreaker({
  failureThreshold: 3,      // Open after 3 failures
  resetTimeout: 15000,      // Wait 15 seconds before trying again
  monitoringPeriod: 30000   // Monitor failures in last 30 seconds
});

export const dashboardCircuitBreaker = new CircuitBreaker({
  failureThreshold: 3,      // Open after 3 failures
  resetTimeout: 20000,      // Wait 20 seconds before trying again
  monitoringPeriod: 45000   // Monitor failures in last 45 seconds
});

// Utility function to wrap API calls with circuit breaker
export async function withCircuitBreaker<T>(
  operation: () => Promise<T>,
  circuitBreaker: CircuitBreaker,
  operationName: string
): Promise<T> {
  return circuitBreaker.execute(operation, operationName);
}

// Export types and enums
export { CircuitBreaker, CircuitState };
export type { CircuitBreakerConfig, FailureRecord };
