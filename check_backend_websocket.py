#!/usr/bin/env python3
"""
Quick script to check if the FastAPI backend and WebSocket service are running
"""

import requests
import asyncio
import websockets
import json
import sys

API_BASE_URL = "http://localhost:8000"
WS_BASE_URL = "ws://localhost:8000"

def check_backend_health():
    """Check if the FastAPI backend is running"""
    print("🔍 Checking FastAPI backend health...")
    
    try:
        # Check main API health
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ FastAPI backend is running")
        else:
            print(f"⚠️ FastAPI backend returned status {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ FastAPI backend is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Error checking backend: {e}")
        return False
    
    # Check WebSocket health endpoint
    try:
        response = requests.get(f"{API_BASE_URL}/ws/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ WebSocket service is healthy - {data.get('connected_users', 0)} connected users")
        else:
            print(f"⚠️ WebSocket health endpoint returned status {response.status_code}")
    except Exception as e:
        print(f"⚠️ WebSocket health check failed: {e}")
    
    # Check WebSocket status endpoint
    try:
        response = requests.get(f"{API_BASE_URL}/ws/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ WebSocket status - {data.get('connected_users', 0)} users, {data.get('active_connections', 0)} connections")
        else:
            print(f"⚠️ WebSocket status endpoint returned status {response.status_code}")
    except Exception as e:
        print(f"⚠️ WebSocket status check failed: {e}")
    
    return True

def check_auth_endpoint():
    """Check if authentication endpoints are working"""
    print("\n🔍 Checking authentication endpoints...")
    
    try:
        # Check login endpoint (should return 422 for missing data, not 500)
        response = requests.post(f"{API_BASE_URL}/auth/login", json={}, timeout=5)
        if response.status_code == 422:
            print("✅ Auth login endpoint is accessible")
        else:
            print(f"⚠️ Auth login endpoint returned unexpected status {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking auth endpoint: {e}")
        return False
    
    return True

async def test_websocket_connection():
    """Test WebSocket connection without authentication"""
    print("\n🔍 Testing WebSocket connection...")
    
    try:
        # Try to connect without token (should fail gracefully)
        ws_url = f"{WS_BASE_URL}/ws/notifications"
        print(f"Attempting to connect to: {ws_url}")
        
        async with websockets.connect(ws_url) as websocket:
            print("❌ WebSocket connected without token (this shouldn't happen)")
            
    except websockets.exceptions.ConnectionClosedError as e:
        if e.code == 1008:  # Policy violation (expected for missing token)
            print("✅ WebSocket correctly rejects connections without authentication token")
        else:
            print(f"⚠️ WebSocket closed with unexpected code: {e.code} - {e.reason}")
    except websockets.exceptions.InvalidStatusCode as e:
        if e.status_code == 403:
            print("✅ WebSocket correctly returns 403 for missing authentication")
        else:
            print(f"⚠️ WebSocket returned unexpected status: {e.status_code}")
    except Exception as e:
        print(f"❌ WebSocket connection test failed: {e}")
        return False
    
    return True

def check_cors_headers():
    """Check CORS headers for frontend compatibility"""
    print("\n🔍 Checking CORS configuration...")
    
    try:
        # Make an OPTIONS request to check CORS
        response = requests.options(f"{API_BASE_URL}/auth/login", 
                                  headers={'Origin': 'http://localhost:3000'}, 
                                  timeout=5)
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        
        if cors_headers['Access-Control-Allow-Origin']:
            print("✅ CORS headers are configured")
            for header, value in cors_headers.items():
                if value:
                    print(f"   {header}: {value}")
        else:
            print("⚠️ CORS headers not found - this might cause frontend connection issues")
            
    except Exception as e:
        print(f"⚠️ CORS check failed: {e}")

def check_environment():
    """Check environment and provide recommendations"""
    print("\n🔍 Environment Check...")
    
    print("📋 Expected setup:")
    print("   - FastAPI server running on http://localhost:8000")
    print("   - WebSocket endpoint available at ws://localhost:8000/ws/notifications")
    print("   - React frontend running on http://localhost:3000")
    print("   - CORS configured to allow localhost:3000")
    
    print("\n📋 To start the backend:")
    print("   cd expense-app")
    print("   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    
    print("\n📋 To start the frontend:")
    print("   cd expense-frontend")
    print("   npm start")

async def main():
    """Main function to run all checks"""
    print("🧪 Backend WebSocket Health Check")
    print("=" * 50)
    
    # Check backend health
    backend_ok = check_backend_health()
    
    if not backend_ok:
        print("\n❌ Backend is not running. Please start the FastAPI server first.")
        check_environment()
        sys.exit(1)
    
    # Check auth endpoints
    auth_ok = check_auth_endpoint()
    
    # Test WebSocket connection
    ws_ok = await test_websocket_connection()
    
    # Check CORS
    check_cors_headers()
    
    print("\n" + "=" * 50)
    
    if backend_ok and auth_ok and ws_ok:
        print("🎉 All checks passed! Backend is ready for WebSocket connections.")
        print("\n✅ Next steps:")
        print("   1. Start the React frontend: cd expense-frontend && npm start")
        print("   2. Login with valid credentials")
        print("   3. Check the WebSocket connection indicator in the notification panel")
        print("   4. Create an expense to test real-time notifications")
    else:
        print("⚠️ Some checks failed. Please review the issues above.")
        print("\n🔧 Common fixes:")
        print("   - Make sure FastAPI server is running: uvicorn app.main:app --reload")
        print("   - Check if port 8000 is available")
        print("   - Verify database is accessible")
        print("   - Check firewall settings")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ Check interrupted by user")
    except Exception as e:
        print(f"\n❌ Check failed with error: {e}")
        sys.exit(1)
