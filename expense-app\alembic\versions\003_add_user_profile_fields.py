"""Add user profile fields

Revision ID: 003_add_user_profile_fields
Revises: 7fc7841449b7
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '003_add_user_profile_fields'
down_revision = '7fc7841449b7'
branch_labels = None
depends_on = None


def upgrade():
    # Add profile fields to users table
    op.add_column('users', sa.Column('full_name', sa.String(), nullable=True))
    op.add_column('users', sa.Column('avatar_url', sa.String(), nullable=True))
    op.add_column('users', sa.Column('phone', sa.String(), nullable=True))
    op.add_column('users', sa.Column('timezone', sa.String(), nullable=True))
    op.add_column('users', sa.Column('currency', sa.String(), nullable=True))
    op.add_column('users', sa.Column('language', sa.String(), nullable=True))

    # Add notification preferences
    op.add_column('users', sa.Column('email_notifications', sa.<PERSON>(), nullable=True))
    op.add_column('users', sa.Column('expense_notifications', sa.Boolean(), nullable=True))
    op.add_column('users', sa.Column('approval_notifications', sa.Boolean(), nullable=True))
    op.add_column('users', sa.Column('settlement_notifications', sa.Boolean(), nullable=True))

    # Add timestamps
    op.add_column('users', sa.Column('created_at', sa.DateTime(), nullable=True))
    op.add_column('users', sa.Column('updated_at', sa.DateTime(), nullable=True))

    # Set default values for existing users (SQLite compatible)
    op.execute("UPDATE users SET timezone = 'UTC' WHERE timezone IS NULL")
    op.execute("UPDATE users SET currency = 'PKR' WHERE currency IS NULL")
    op.execute("UPDATE users SET language = 'en' WHERE language IS NULL")
    op.execute("UPDATE users SET email_notifications = 1 WHERE email_notifications IS NULL")
    op.execute("UPDATE users SET expense_notifications = 1 WHERE expense_notifications IS NULL")
    op.execute("UPDATE users SET approval_notifications = 1 WHERE approval_notifications IS NULL")
    op.execute("UPDATE users SET settlement_notifications = 1 WHERE settlement_notifications IS NULL")
    op.execute("UPDATE users SET created_at = datetime('now') WHERE created_at IS NULL")
    op.execute("UPDATE users SET updated_at = datetime('now') WHERE updated_at IS NULL")


def downgrade():
    # Remove the added columns
    op.drop_column('users', 'updated_at')
    op.drop_column('users', 'created_at')
    op.drop_column('users', 'settlement_notifications')
    op.drop_column('users', 'approval_notifications')
    op.drop_column('users', 'expense_notifications')
    op.drop_column('users', 'email_notifications')
    op.drop_column('users', 'language')
    op.drop_column('users', 'currency')
    op.drop_column('users', 'timezone')
    op.drop_column('users', 'phone')
    op.drop_column('users', 'avatar_url')
    op.drop_column('users', 'full_name')
