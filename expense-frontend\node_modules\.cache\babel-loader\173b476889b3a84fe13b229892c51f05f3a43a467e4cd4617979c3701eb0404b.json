{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport LoginForm from './components/Auth/LoginForm';\nimport RegisterForm from './components/Auth/RegisterForm';\nimport Dashboard from './pages/Dashboard';\nimport AIChat from './pages/AIChat';\nimport Groups from './pages/Groups';\nimport Expenses from './pages/Expenses';\nimport Settlements from './pages/Settlements';\nimport Approvals from './pages/Approvals';\nimport SettlementConfirmations from './pages/SettlementConfirmations';\nimport Settings from './pages/Settings';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"App bg-white dark:bg-gray-900 min-h-screen transition-colors\",\n          children: [/*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(LoginForm, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 43\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/register\",\n              element: /*#__PURE__*/_jsxDEV(RegisterForm, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 33,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/groups\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Groups, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/expenses\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Expenses, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 45,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/settlements\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Settlements, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/approvals\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Approvals, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/settlement-confirmations\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(SettlementConfirmations, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/ai-chat\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AIChat, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/settings\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Settings, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 38\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n            position: \"top-right\",\n            toastOptions: {\n              duration: 4000,\n              className: 'dark:bg-gray-800 dark:text-white',\n              style: {\n                background: 'var(--toast-bg, #363636)',\n                color: 'var(--toast-color, #fff)'\n              },\n              success: {\n                duration: 3000,\n                iconTheme: {\n                  primary: '#10B981',\n                  secondary: '#fff'\n                }\n              },\n              error: {\n                duration: 4000,\n                iconTheme: {\n                  primary: '#EF4444',\n                  secondary: '#fff'\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Toaster", "<PERSON>th<PERSON><PERSON><PERSON>", "ThemeProvider", "ProtectedRoute", "LoginForm", "RegisterForm", "Dashboard", "AIChat", "Groups", "Expenses", "Settlements", "Approvals", "SettlementConfirmations", "Settings", "jsxDEV", "_jsxDEV", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "position", "toastOptions", "duration", "style", "background", "color", "success", "iconTheme", "primary", "secondary", "error", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport { WebSocketProvider } from './contexts/WebSocketContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport LoginForm from './components/Auth/LoginForm';\nimport RegisterForm from './components/Auth/RegisterForm';\nimport Dashboard from './pages/Dashboard';\nimport AIChat from './pages/AIChat';\nimport Groups from './pages/Groups';\nimport Expenses from './pages/Expenses';\nimport Settlements from './pages/Settlements';\nimport Approvals from './pages/Approvals';\nimport SettlementConfirmations from './pages/SettlementConfirmations';\nimport Settings from './pages/Settings';\n\nfunction App() {\n  return (\n    <ThemeProvider>\n      <AuthProvider>\n        <Router>\n          <div className=\"App bg-white dark:bg-gray-900 min-h-screen transition-colors\">\n          <Routes>\n            {/* Public routes */}\n            <Route path=\"/login\" element={<LoginForm />} />\n            <Route path=\"/register\" element={<RegisterForm />} />\n            \n            {/* Protected routes */}\n            <Route path=\"/\" element={\n              <ProtectedRoute>\n                <Dashboard />\n              </ProtectedRoute>\n            } />\n            \n            <Route path=\"/groups\" element={\n              <ProtectedRoute>\n                <Groups />\n              </ProtectedRoute>\n            } />\n\n            <Route path=\"/expenses\" element={\n              <ProtectedRoute>\n                <Expenses />\n              </ProtectedRoute>\n            } />\n\n            <Route path=\"/settlements\" element={\n              <ProtectedRoute>\n                <Settlements />\n              </ProtectedRoute>\n            } />\n\n            <Route path=\"/approvals\" element={\n              <ProtectedRoute>\n                <Approvals />\n              </ProtectedRoute>\n            } />\n\n            <Route path=\"/settlement-confirmations\" element={\n              <ProtectedRoute>\n                <SettlementConfirmations />\n              </ProtectedRoute>\n            } />\n\n            <Route path=\"/ai-chat\" element={\n              <ProtectedRoute>\n                <AIChat />\n              </ProtectedRoute>\n            } />\n            \n            <Route path=\"/settings\" element={\n              <ProtectedRoute>\n                <Settings />\n              </ProtectedRoute>\n            } />\n            \n            {/* Catch all route */}\n            <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n          </Routes>\n\n          {/* Toast notifications */}\n          <Toaster\n            position=\"top-right\"\n            toastOptions={{\n              duration: 4000,\n              className: 'dark:bg-gray-800 dark:text-white',\n              style: {\n                background: 'var(--toast-bg, #363636)',\n                color: 'var(--toast-color, #fff)',\n              },\n              success: {\n                duration: 3000,\n                iconTheme: {\n                  primary: '#10B981',\n                  secondary: '#fff',\n                },\n              },\n              error: {\n                duration: 4000,\n                iconTheme: {\n                  primary: '#EF4444',\n                  secondary: '#fff',\n                },\n              },\n            }}\n          />\n          </div>\n        </Router>\n      </AuthProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,aAAa,QAAQ,yBAAyB;AAEvD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,YAAY,MAAM,gCAAgC;AACzD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,uBAAuB,MAAM,iCAAiC;AACrE,OAAOC,QAAQ,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACb,aAAa;IAAAe,QAAA,eACZF,OAAA,CAACd,YAAY;MAAAgB,QAAA,eACXF,OAAA,CAACnB,MAAM;QAAAqB,QAAA,eACLF,OAAA;UAAKG,SAAS,EAAC,8DAA8D;UAAAD,QAAA,gBAC7EF,OAAA,CAAClB,MAAM;YAAAoB,QAAA,gBAELF,OAAA,CAACjB,KAAK;cAACqB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEL,OAAA,CAACX,SAAS;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CT,OAAA,CAACjB,KAAK;cAACqB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEL,OAAA,CAACV,YAAY;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGrDT,OAAA,CAACjB,KAAK;cAACqB,IAAI,EAAC,GAAG;cAACC,OAAO,eACrBL,OAAA,CAACZ,cAAc;gBAAAc,QAAA,eACbF,OAAA,CAACT,SAAS;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEJT,OAAA,CAACjB,KAAK;cAACqB,IAAI,EAAC,SAAS;cAACC,OAAO,eAC3BL,OAAA,CAACZ,cAAc;gBAAAc,QAAA,eACbF,OAAA,CAACP,MAAM;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEJT,OAAA,CAACjB,KAAK;cAACqB,IAAI,EAAC,WAAW;cAACC,OAAO,eAC7BL,OAAA,CAACZ,cAAc;gBAAAc,QAAA,eACbF,OAAA,CAACN,QAAQ;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEJT,OAAA,CAACjB,KAAK;cAACqB,IAAI,EAAC,cAAc;cAACC,OAAO,eAChCL,OAAA,CAACZ,cAAc;gBAAAc,QAAA,eACbF,OAAA,CAACL,WAAW;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEJT,OAAA,CAACjB,KAAK;cAACqB,IAAI,EAAC,YAAY;cAACC,OAAO,eAC9BL,OAAA,CAACZ,cAAc;gBAAAc,QAAA,eACbF,OAAA,CAACJ,SAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEJT,OAAA,CAACjB,KAAK;cAACqB,IAAI,EAAC,2BAA2B;cAACC,OAAO,eAC7CL,OAAA,CAACZ,cAAc;gBAAAc,QAAA,eACbF,OAAA,CAACH,uBAAuB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEJT,OAAA,CAACjB,KAAK;cAACqB,IAAI,EAAC,UAAU;cAACC,OAAO,eAC5BL,OAAA,CAACZ,cAAc;gBAAAc,QAAA,eACbF,OAAA,CAACR,MAAM;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEJT,OAAA,CAACjB,KAAK;cAACqB,IAAI,EAAC,WAAW;cAACC,OAAO,eAC7BL,OAAA,CAACZ,cAAc;gBAAAc,QAAA,eACbF,OAAA,CAACF,QAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGJT,OAAA,CAACjB,KAAK;cAACqB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEL,OAAA,CAAChB,QAAQ;gBAAC0B,EAAE,EAAC,GAAG;gBAACC,OAAO;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eAGTT,OAAA,CAACf,OAAO;YACN2B,QAAQ,EAAC,WAAW;YACpBC,YAAY,EAAE;cACZC,QAAQ,EAAE,IAAI;cACdX,SAAS,EAAE,kCAAkC;cAC7CY,KAAK,EAAE;gBACLC,UAAU,EAAE,0BAA0B;gBACtCC,KAAK,EAAE;cACT,CAAC;cACDC,OAAO,EAAE;gBACPJ,QAAQ,EAAE,IAAI;gBACdK,SAAS,EAAE;kBACTC,OAAO,EAAE,SAAS;kBAClBC,SAAS,EAAE;gBACb;cACF,CAAC;cACDC,KAAK,EAAE;gBACLR,QAAQ,EAAE,IAAI;gBACdK,SAAS,EAAE;kBACTC,OAAO,EAAE,SAAS;kBAClBC,SAAS,EAAE;gBACb;cACF;YACF;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB;AAACc,EAAA,GA/FQtB,GAAG;AAiGZ,eAAeA,GAAG;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}