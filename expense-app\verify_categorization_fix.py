#!/usr/bin/env python3
"""
Comprehensive verification that the categorization system is working
"""

import sqlite3
import os

def verify_expenses_schema():
    """Verify expenses table has all required columns"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        cursor.execute("PRAGMA table_info(expenses);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        required_columns = [
            'id', 'payer_id', 'group_id', 'total', 'description', 
            'created_at', 'status', 'approved_at', 'category_id'
        ]
        
        print("📋 Expenses table verification:")
        missing_columns = []
        for col in required_columns:
            if col in column_names:
                print(f"  ✅ {col}")
            else:
                print(f"  ❌ {col} - MISSING")
                missing_columns.append(col)
        
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            return False
        
        print("✅ All required columns present in expenses table")
        return True
        
    except Exception as e:
        print(f"❌ Error verifying expenses schema: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def verify_categorization_tables():
    """Verify all categorization tables exist with correct schema"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        # Check expense_categories table
        cursor.execute("PRAGMA table_info(expense_categories);")
        cat_columns = [col[1] for col in cursor.fetchall()]
        expected_cat_columns = ['id', 'name', 'description', 'color', 'icon', 'parent_id', 'is_system', 'created_by', 'created_at', 'updated_at']
        
        print("\n📋 Expense categories table:")
        for col in expected_cat_columns:
            if col in cat_columns:
                print(f"  ✅ {col}")
            else:
                print(f"  ❌ {col} - MISSING")
        
        # Check expense_tags table
        cursor.execute("PRAGMA table_info(expense_tags);")
        tag_columns = [col[1] for col in cursor.fetchall()]
        expected_tag_columns = ['id', 'name', 'color', 'created_by', 'created_at']
        
        print("\n📋 Expense tags table:")
        for col in expected_tag_columns:
            if col in tag_columns:
                print(f"  ✅ {col}")
            else:
                print(f"  ❌ {col} - MISSING")
        
        # Check expense_tag_associations table
        cursor.execute("PRAGMA table_info(expense_tag_associations);")
        assoc_columns = [col[1] for col in cursor.fetchall()]
        expected_assoc_columns = ['expense_id', 'tag_id']
        
        print("\n📋 Expense tag associations table:")
        for col in expected_assoc_columns:
            if col in assoc_columns:
                print(f"  ✅ {col}")
            else:
                print(f"  ❌ {col} - MISSING")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying categorization tables: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_problematic_queries():
    """Test the specific queries that were causing 500 errors"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        print("\n🧪 Testing problematic endpoint queries:")
        
        # Test 1: Expenses history query (GET /expenses/history)
        print("\n1. Testing expenses history query...")
        cursor.execute("""
            SELECT expenses.id, expenses.payer_id, expenses.group_id, 
                   expenses.total, expenses.description, expenses.created_at,
                   expenses.status, expenses.approved_at, expenses.category_id
            FROM expenses
            ORDER BY expenses.created_at DESC
            LIMIT 10
        """)
        results = cursor.fetchall()
        print(f"   ✅ Expenses history query successful - {len(results)} expenses found")
        
        # Test 2: Group details query (GET /group-management/groups/{id}/details)
        print("\n2. Testing group details with expenses query...")
        cursor.execute("""
            SELECT g.id, g.name, g.creator_id,
                   e.id as expense_id, e.total, e.description, e.category_id
            FROM groups g
            LEFT JOIN expenses e ON g.id = e.group_id
            LIMIT 10
        """)
        results = cursor.fetchall()
        print(f"   ✅ Group details query successful - {len(results)} records found")
        
        # Test 3: Categorization join query
        print("\n3. Testing categorization join query...")
        cursor.execute("""
            SELECT e.id, e.description, e.category_id, 
                   c.name as category_name, c.color as category_color
            FROM expenses e
            LEFT JOIN expense_categories c ON e.category_id = c.id
            LIMIT 10
        """)
        results = cursor.fetchall()
        print(f"   ✅ Categorization join query successful - {len(results)} records found")
        
        # Test 4: Tag associations query
        print("\n4. Testing tag associations query...")
        cursor.execute("""
            SELECT e.id, e.description, t.name as tag_name, t.color as tag_color
            FROM expenses e
            LEFT JOIN expense_tag_associations eta ON e.id = eta.expense_id
            LEFT JOIN expense_tags t ON eta.tag_id = t.id
            LIMIT 10
        """)
        results = cursor.fetchall()
        print(f"   ✅ Tag associations query successful - {len(results)} records found")
        
        return True
        
    except Exception as e:
        print(f"❌ Query test failed: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def create_sample_categories():
    """Create some sample categories for testing"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        # Check if categories already exist
        cursor.execute("SELECT COUNT(*) FROM expense_categories")
        count = cursor.fetchone()[0]
        
        if count > 0:
            print(f"\n📦 Sample categories already exist ({count} categories)")
            return True
        
        print("\n📦 Creating sample categories...")
        
        sample_categories = [
            ("Food & Dining", "Restaurants, groceries, food delivery", "#F59E0B", "🍽️", 1, None),
            ("Transportation", "Fuel, public transport, taxi, parking", "#3B82F6", "🚗", 1, None),
            ("Office Supplies", "Stationery, equipment, software", "#10B981", "📎", 1, None),
            ("Utilities", "Electricity, water, gas, internet", "#8B5CF6", "⚡", 1, None),
        ]
        
        for name, desc, color, icon, is_system, created_by in sample_categories:
            cursor.execute("""
                INSERT INTO expense_categories 
                (name, description, color, icon, is_system, created_by, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            """, (name, desc, color, icon, is_system, created_by))
        
        conn.commit()
        print(f"✅ Created {len(sample_categories)} sample categories")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating sample categories: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def show_database_summary():
    """Show a summary of the current database state"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        print("\n📊 Database Summary:")
        
        # Count tables
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
        table_count = cursor.fetchone()[0]
        print(f"   Total tables: {table_count}")
        
        # Count users
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"   Users: {user_count}")
        
        # Count groups
        cursor.execute("SELECT COUNT(*) FROM groups")
        group_count = cursor.fetchone()[0]
        print(f"   Groups: {group_count}")
        
        # Count expenses
        cursor.execute("SELECT COUNT(*) FROM expenses")
        expense_count = cursor.fetchone()[0]
        print(f"   Expenses: {expense_count}")
        
        # Count categories
        cursor.execute("SELECT COUNT(*) FROM expense_categories")
        category_count = cursor.fetchone()[0]
        print(f"   Categories: {category_count}")
        
        # Count tags
        cursor.execute("SELECT COUNT(*) FROM expense_tags")
        tag_count = cursor.fetchone()[0]
        print(f"   Tags: {tag_count}")
        
        # Count categorized expenses
        cursor.execute("SELECT COUNT(*) FROM expenses WHERE category_id IS NOT NULL")
        categorized_count = cursor.fetchone()[0]
        print(f"   Categorized expenses: {categorized_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error showing database summary: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """Main verification function"""
    print("🔍 Comprehensive Categorization System Verification")
    print("=" * 60)
    
    # Step 1: Verify expenses table schema
    if not verify_expenses_schema():
        print("❌ Expenses schema verification failed")
        return
    
    # Step 2: Verify categorization tables
    if not verify_categorization_tables():
        print("❌ Categorization tables verification failed")
        return
    
    # Step 3: Test problematic queries
    if not test_problematic_queries():
        print("❌ Query tests failed")
        return
    
    # Step 4: Create sample categories
    create_sample_categories()
    
    # Step 5: Show database summary
    show_database_summary()
    
    print("\n" + "=" * 60)
    print("🎉 Categorization System Verification Complete!")
    print("✅ All database schema issues resolved")
    print("✅ Endpoints should work without 'no such column' errors:")
    print("   - GET /expenses/history")
    print("   - GET /group-management/groups/{id}/details")
    print("   - All categorization endpoints")
    print("✅ Categorization system is fully functional")

if __name__ == "__main__":
    main()
