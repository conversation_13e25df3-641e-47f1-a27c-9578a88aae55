#!/usr/bin/env python3
"""
Fix script to add the missing category_id column to the expenses table
"""

import sqlite3
import os

def add_category_id_column():
    """Add the missing category_id column to expenses table"""
    db_path = "./dev.db"
    
    if not os.path.exists(db_path):
        print("❌ Database file 'dev.db' does not exist!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if category_id column already exists
        cursor.execute("PRAGMA table_info(expenses);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'category_id' in column_names:
            print("✅ category_id column already exists in expenses table")
            return True
        
        print("🔧 Adding category_id column to expenses table...")
        
        # Add the category_id column
        cursor.execute("ALTER TABLE expenses ADD COLUMN category_id INTEGER")
        print("✅ Added category_id column")
        
        # Note: SQLite doesn't support adding foreign key constraints to existing tables
        # The foreign key relationship will be enforced by SQLAlchemy ORM
        
        conn.commit()
        print("✅ Changes committed to database")
        
        # Verify the column was added
        cursor.execute("PRAGMA table_info(expenses);")
        new_columns = cursor.fetchall()
        new_column_names = [col[1] for col in new_columns]
        
        if 'category_id' in new_column_names:
            print("✅ Verification: category_id column successfully added")
            return True
        else:
            print("❌ Verification failed: category_id column not found")
            return False
        
    except Exception as e:
        print(f"❌ Error adding category_id column: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_category_queries():
    """Test queries that were failing"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        print("\n🧪 Testing category-related queries...")
        
        # Test 1: Basic category_id selection
        cursor.execute("SELECT category_id FROM expenses LIMIT 1")
        result = cursor.fetchone()
        print("✅ Test 1: SELECT category_id FROM expenses - SUCCESS")
        
        # Test 2: Full expenses query with category_id
        cursor.execute("SELECT expenses.category_id AS expenses_category_id FROM expenses LIMIT 1")
        result = cursor.fetchone()
        print("✅ Test 2: SELECT expenses.category_id AS expenses_category_id - SUCCESS")
        
        # Test 3: Join with expense_categories (if any categories exist)
        cursor.execute("""
            SELECT e.id, e.description, e.category_id, c.name as category_name
            FROM expenses e
            LEFT JOIN expense_categories c ON e.category_id = c.id
            LIMIT 1
        """)
        result = cursor.fetchone()
        print("✅ Test 3: LEFT JOIN with expense_categories - SUCCESS")
        
        # Test 4: Count expenses by category
        cursor.execute("""
            SELECT category_id, COUNT(*) as count
            FROM expenses
            GROUP BY category_id
            LIMIT 5
        """)
        results = cursor.fetchall()
        print("✅ Test 4: GROUP BY category_id - SUCCESS")
        
        return True
        
    except Exception as e:
        print(f"❌ Query test failed: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def show_final_schema():
    """Show the final expenses table schema"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        cursor.execute("PRAGMA table_info(expenses);")
        columns = cursor.fetchall()
        
        print("\n📋 Final expenses table schema:")
        for i, col in enumerate(columns, 1):
            print(f"  {i:2d}. {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        
        print(f"\n✅ Total columns: {len(columns)}")
        
        # Check if we have any expenses with categories
        cursor.execute("SELECT COUNT(*) FROM expenses WHERE category_id IS NOT NULL")
        categorized_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM expenses")
        total_count = cursor.fetchone()[0]
        
        print(f"📊 Expenses summary:")
        print(f"   Total expenses: {total_count}")
        print(f"   Categorized: {categorized_count}")
        print(f"   Uncategorized: {total_count - categorized_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error showing schema: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """Main function"""
    print("🔧 Fix Expenses Table Category Column")
    print("=" * 50)
    
    # Step 1: Add the missing column
    if not add_category_id_column():
        print("❌ Failed to add category_id column")
        return
    
    # Step 2: Test the queries that were failing
    if not test_category_queries():
        print("❌ Query tests failed")
        return
    
    # Step 3: Show final schema
    show_final_schema()
    
    print("\n" + "=" * 50)
    print("🎉 Expenses table category column fix completed!")
    print("✅ The 'no such column: expenses.category_id' error should be resolved")
    print("✅ Endpoints /expenses/history and /group-management/groups/{id}/details should work")
    print("✅ Categorization system is now fully functional")

if __name__ == "__main__":
    main()
