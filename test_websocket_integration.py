#!/usr/bin/env python3
"""
Test script to verify WebSocket integration and notification system
"""

import asyncio
import json
import websockets
import requests
import sys
import os

# Configuration
API_BASE_URL = "http://localhost:8000"
WS_BASE_URL = "ws://localhost:8000"

class WebSocketTester:
    def __init__(self):
        self.token = None
        self.user_id = None
        
    async def authenticate(self):
        """Authenticate and get token"""
        try:
            # Try to login with test credentials
            login_data = {
                "email": "<EMAIL>",
                "password": "testpassword"
            }
            
            response = requests.post(f"{API_BASE_URL}/auth/login", json=login_data)
            
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                print("✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code}")
                print("Response:", response.text)
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False
    
    async def test_websocket_connection(self):
        """Test WebSocket connection"""
        if not self.token:
            print("❌ No authentication token available")
            return False
        
        try:
            ws_url = f"{WS_BASE_URL}/ws/notifications?token={self.token}"
            print(f"🔗 Connecting to WebSocket: {ws_url}")
            
            async with websockets.connect(ws_url) as websocket:
                print("✅ WebSocket connected successfully")
                
                # Wait for welcome message
                try:
                    welcome_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    welcome_data = json.loads(welcome_msg)
                    print(f"📨 Welcome message: {welcome_data}")
                    
                    if welcome_data.get("type") == "connection_established":
                        self.user_id = welcome_data.get("user_id")
                        print(f"✅ Connection established for user ID: {self.user_id}")
                    
                except asyncio.TimeoutError:
                    print("⚠️ No welcome message received within timeout")
                
                # Send ping message
                ping_msg = {"type": "ping", "timestamp": "test"}
                await websocket.send(json.dumps(ping_msg))
                print("📤 Sent ping message")
                
                # Wait for pong response
                try:
                    pong_msg = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    pong_data = json.loads(pong_msg)
                    print(f"📨 Pong response: {pong_data}")
                    
                    if pong_data.get("type") == "pong":
                        print("✅ Ping-pong test successful")
                    
                except asyncio.TimeoutError:
                    print("⚠️ No pong response received within timeout")
                
                # Test status request
                status_msg = {"type": "get_status"}
                await websocket.send(json.dumps(status_msg))
                print("📤 Sent status request")
                
                # Wait for status response
                try:
                    status_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    status_data = json.loads(status_response)
                    print(f"📨 Status response: {status_data}")
                    
                except asyncio.TimeoutError:
                    print("⚠️ No status response received within timeout")
                
                print("✅ WebSocket connection test completed successfully")
                return True
                
        except Exception as e:
            print(f"❌ WebSocket connection error: {e}")
            return False
    
    async def test_notification_api(self):
        """Test notification API endpoints"""
        if not self.token:
            print("❌ No authentication token available")
            return False
        
        headers = {"Authorization": f"Bearer {self.token}"}
        
        try:
            # Test get notifications
            response = requests.get(f"{API_BASE_URL}/notifications/", headers=headers)
            if response.status_code == 200:
                notifications = response.json()
                print(f"✅ Retrieved {len(notifications)} notifications")
            else:
                print(f"⚠️ Get notifications failed: {response.status_code}")
            
            # Test get summary
            response = requests.get(f"{API_BASE_URL}/notifications/summary", headers=headers)
            if response.status_code == 200:
                summary = response.json()
                print(f"✅ Notification summary: {summary}")
            else:
                print(f"⚠️ Get summary failed: {response.status_code}")
            
            # Test get unread count
            response = requests.get(f"{API_BASE_URL}/notifications/unread/count", headers=headers)
            if response.status_code == 200:
                count = response.json()
                print(f"✅ Unread count: {count}")
            else:
                print(f"⚠️ Get unread count failed: {response.status_code}")
            
            # Test get templates
            response = requests.get(f"{API_BASE_URL}/notifications/templates", headers=headers)
            if response.status_code == 200:
                templates = response.json()
                print(f"✅ Retrieved {len(templates)} notification templates")
            else:
                print(f"⚠️ Get templates failed: {response.status_code}")
            
            return True
            
        except Exception as e:
            print(f"❌ Notification API test error: {e}")
            return False
    
    async def test_websocket_status(self):
        """Test WebSocket status endpoint"""
        try:
            response = requests.get(f"{API_BASE_URL}/ws/status")
            if response.status_code == 200:
                status = response.json()
                print(f"✅ WebSocket status: {status}")
                return True
            else:
                print(f"❌ WebSocket status failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ WebSocket status error: {e}")
            return False
    
    async def test_websocket_health(self):
        """Test WebSocket health endpoint"""
        try:
            response = requests.get(f"{API_BASE_URL}/ws/health")
            if response.status_code == 200:
                health = response.json()
                print(f"✅ WebSocket health: {health}")
                return True
            else:
                print(f"❌ WebSocket health failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ WebSocket health error: {e}")
            return False

async def main():
    """Main test function"""
    print("🧪 WebSocket Integration Test")
    print("=" * 50)
    
    tester = WebSocketTester()
    
    # Test 1: Authentication
    print("\n1. Testing Authentication...")
    if not await tester.authenticate():
        print("❌ Authentication failed. Cannot proceed with WebSocket tests.")
        print("Make sure the FastAPI server is running and test user exists.")
        sys.exit(1)
    
    # Test 2: WebSocket Status
    print("\n2. Testing WebSocket Status...")
    await tester.test_websocket_status()
    
    # Test 3: WebSocket Health
    print("\n3. Testing WebSocket Health...")
    await tester.test_websocket_health()
    
    # Test 4: Notification API
    print("\n4. Testing Notification API...")
    await tester.test_notification_api()
    
    # Test 5: WebSocket Connection
    print("\n5. Testing WebSocket Connection...")
    if await tester.test_websocket_connection():
        print("✅ All WebSocket tests passed!")
    else:
        print("❌ WebSocket connection test failed!")
    
    print("\n" + "=" * 50)
    print("🎉 WebSocket Integration Test Complete!")
    print("\nNext steps:")
    print("1. Start the React frontend: cd expense-frontend && npm start")
    print("2. Open browser and check notification panel")
    print("3. Create an expense to test real-time notifications")
    print("4. Check browser console for WebSocket connection logs")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
