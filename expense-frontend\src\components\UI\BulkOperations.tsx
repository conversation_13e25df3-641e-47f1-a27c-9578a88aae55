import React, { useState } from 'react';
import { Check, X, Trash2, Tag, ChevronDown } from 'lucide-react';
import toast from 'react-hot-toast';
import { expensesAPI } from '../../services/api';

interface BulkOperationsProps {
  selectedItems: number[];
  onClearSelection: () => void;
  onRefresh: () => void;
  itemType: 'expenses' | 'approvals';
}

const BulkOperations: React.FC<BulkOperationsProps> = ({
  selectedItems,
  onClearSelection,
  onRefresh,
  itemType
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showActions, setShowActions] = useState(false);

  if (selectedItems.length === 0) {
    return null;
  }

  const handleBulkApprove = async (approved: boolean) => {
    setIsLoading(true);
    try {
      const response = await expensesAPI.bulkApprove({
        expense_ids: selectedItems,
        approved
      });

      const action = approved ? 'approved' : 'rejected';
      toast.success(`${response.data.success_count} expenses ${action} successfully`);
      
      if (response.data.failed_count > 0) {
        toast.error(`${response.data.failed_count} expenses failed to ${action.slice(0, -1)}`);
      }

      onClearSelection();
      onRefresh();
    } catch (error: any) {
      toast.error(error.response?.data?.detail || `Failed to ${approved ? 'approve' : 'reject'} expenses`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBulkDelete = async () => {
    if (!window.confirm(`Are you sure you want to delete ${selectedItems.length} expenses? This action cannot be undone.`)) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await expensesAPI.bulkDelete({
        expense_ids: selectedItems
      });

      toast.success(`${response.data.success_count} expenses deleted successfully`);
      
      if (response.data.failed_count > 0) {
        toast.error(`${response.data.failed_count} expenses failed to delete`);
      }

      onClearSelection();
      onRefresh();
    } catch (error: any) {
      toast.error(error.response?.data?.detail || 'Failed to delete expenses');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBulkCategorize = async (category: string) => {
    // This would be implemented when categorization system is added
    toast('Bulk categorization will be available when categorization system is implemented', {
      icon: 'ℹ️',
    });
  };

  return (
    <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 min-w-96">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-medium">
              {selectedItems.length} selected
            </div>
            <span className="text-gray-600 text-sm">
              {itemType === 'expenses' ? 'expenses' : 'approvals'}
            </span>
          </div>
          <button
            onClick={onClearSelection}
            className="text-gray-400 hover:text-gray-600"
            disabled={isLoading}
          >
            <X className="h-4 w-4" />
          </button>
        </div>

        <div className="flex items-center space-x-2">
          {itemType === 'approvals' && (
            <>
              <button
                onClick={() => handleBulkApprove(true)}
                disabled={isLoading}
                className="flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                <Check className="h-4 w-4" />
                <span>Approve All</span>
              </button>
              <button
                onClick={() => handleBulkApprove(false)}
                disabled={isLoading}
                className="flex items-center space-x-1 px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                <X className="h-4 w-4" />
                <span>Reject All</span>
              </button>
            </>
          )}

          {itemType === 'expenses' && (
            <button
              onClick={handleBulkDelete}
              disabled={isLoading}
              className="flex items-center space-x-1 px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <Trash2 className="h-4 w-4" />
              <span>Delete All</span>
            </button>
          )}

          {/* More Actions Dropdown */}
          <div className="relative">
            <button
              onClick={() => setShowActions(!showActions)}
              disabled={isLoading}
              className="flex items-center space-x-1 px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              <span>More</span>
              <ChevronDown className="h-4 w-4" />
            </button>

            {showActions && (
              <div className="absolute bottom-full mb-2 right-0 bg-white rounded-md shadow-lg border border-gray-200 py-1 min-w-48">
                <button
                  onClick={() => {
                    handleBulkCategorize('food');
                    setShowActions(false);
                  }}
                  disabled={isLoading}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                >
                  <Tag className="h-4 w-4" />
                  <span>Categorize as Food</span>
                </button>
                <button
                  onClick={() => {
                    handleBulkCategorize('transport');
                    setShowActions(false);
                  }}
                  disabled={isLoading}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                >
                  <Tag className="h-4 w-4" />
                  <span>Categorize as Transport</span>
                </button>
                <button
                  onClick={() => {
                    handleBulkCategorize('utilities');
                    setShowActions(false);
                  }}
                  disabled={isLoading}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                >
                  <Tag className="h-4 w-4" />
                  <span>Categorize as Utilities</span>
                </button>
              </div>
            )}
          </div>
        </div>

        {isLoading && (
          <div className="mt-3 flex items-center justify-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-sm text-gray-600">Processing...</span>
          </div>
        )}
      </div>

      {/* Overlay to close dropdown */}
      {showActions && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowActions(false)}
        ></div>
      )}
    </div>
  );
};

export default BulkOperations;
