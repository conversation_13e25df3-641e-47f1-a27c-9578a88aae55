from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import io

from .. import schemas
from ..deps import get_db, get_current_user
from ..models import User
from ..services.expense_service import ExpenseService
from ..services.export_service import ExportService
from ..services.enhanced_notification_service import ComprehensiveNotificationService

router = APIRouter(prefix="/expenses", tags=["expenses"])

@router.post("/create", response_model=schemas.ExpenseOut,
            summary="Create a new expense",
            description="Add a new expense that will be split equally among all group members (excluding the payer)")
async def create_expense(
    expense_in: schemas.ExpenseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new expense split among group members.

    - **group_id**: ID of the group to split the expense among
    - **total**: Total amount of the expense
    - **description**: Description of what the expense was for

    The expense will be split equally among all group members except the payer.
    Each member (except payer) will have a share created representing what they owe.
    """
    try:
        expense_service = ExpenseService(db)
        expense = expense_service.create_expense(current_user, expense_in)

        # Send enhanced notifications to all group members
        notification_service = ComprehensiveNotificationService(db)
        await notification_service.send_expense_created_notifications(expense, current_user)

        return expense
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/balances", response_model=List[schemas.Balance])
def read_balances(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    expense_service = ExpenseService(db)
    return expense_service.get_user_balances(current_user)

@router.post("/settle", response_model=schemas.SettlementSummary,
            summary="Settle debt with another user",
            description="Pay money to another user to settle outstanding debts from shared expenses")
def settle_expenses(
    req: schemas.SettlementRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Settle debt with another user.

    - **target_user_id**: ID of the user you want to pay
    - **amount**: Amount you want to pay

    This will apply the payment to your oldest unpaid debts to that user first.
    If the payment amount exceeds what you owe, an error will be returned.

    Returns detailed information about which expenses were settled and any remaining balances.
    """
    try:
        expense_service = ExpenseService(db)
        return expense_service.settle_debt(current_user, req)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/debts", response_model=List[schemas.Balance])
def get_my_debts(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get detailed information about debts - who you owe and who owes you.
    Positive amounts mean they owe you, negative amounts mean you owe them.
    """
    expense_service = ExpenseService(db)
    return expense_service.get_user_balances(current_user)

@router.get("/history", response_model=List[schemas.ExpenseOut])
def get_expense_history(
    group_id: int = None,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get expense history for the current user, optionally filtered by group.
    """
    expense_service = ExpenseService(db)
    return expense_service.get_expense_history(current_user, group_id)[:limit]

# Bulk operation endpoints
@router.post("/bulk/approve", response_model=schemas.BulkOperationResult,
            summary="Bulk approve or reject expenses",
            description="Approve or reject multiple expenses at once")
def bulk_approve_expenses(
    request: schemas.BulkApprovalRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Bulk approve or reject multiple expenses.

    - **expense_ids**: List of expense IDs to approve/reject
    - **approved**: True to approve, False to reject

    Only expenses that the current user has permission to approve will be processed.
    """
    try:
        expense_service = ExpenseService(db)
        return expense_service.bulk_approve_expenses(current_user, request.expense_ids, request.approved)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/bulk/delete", response_model=schemas.BulkOperationResult,
            summary="Bulk delete expenses",
            description="Delete multiple expenses at once (only pending expenses created by the user)")
def bulk_delete_expenses(
    request: schemas.BulkDeleteRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Bulk delete multiple expenses.

    - **expense_ids**: List of expense IDs to delete

    Only pending expenses created by the current user can be deleted.
    """
    try:
        expense_service = ExpenseService(db)
        return expense_service.bulk_delete_expenses(current_user, request.expense_ids)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/filter", response_model=schemas.PaginatedExpenseResponse,
           summary="Get filtered expenses with pagination",
           description="Get expenses with advanced filtering options and pagination")
def get_filtered_expenses(
    start_date: Optional[str] = Query(None, description="Start date in ISO format"),
    end_date: Optional[str] = Query(None, description="End date in ISO format"),
    min_amount: Optional[float] = Query(None, description="Minimum expense amount"),
    max_amount: Optional[float] = Query(None, description="Maximum expense amount"),
    status: Optional[str] = Query(None, description="Expense status (pending, approved, rejected)"),
    payer_id: Optional[int] = Query(None, description="Filter by payer user ID"),
    group_id: Optional[int] = Query(None, description="Filter by group ID"),
    category: Optional[str] = Query(None, description="Filter by category"),
    description_contains: Optional[str] = Query(None, description="Filter by description containing text"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    sort_by: Optional[str] = Query("created_at", description="Sort by field"),
    sort_order: Optional[str] = Query("desc", description="Sort order (asc, desc)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get expenses with advanced filtering and pagination.

    Supports filtering by:
    - Date range (start_date, end_date)
    - Amount range (min_amount, max_amount)
    - Status (pending, approved, rejected)
    - Payer (payer_id)
    - Group (group_id)
    - Category (category) - when categorization is implemented
    - Description text search (description_contains)

    Results are paginated and can be sorted by various fields.
    """
    try:
        filters = schemas.ExpenseFilter(
            start_date=start_date,
            end_date=end_date,
            min_amount=min_amount,
            max_amount=max_amount,
            status=status,
            payer_id=payer_id,
            group_id=group_id,
            category=category,
            description_contains=description_contains,
            page=page,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order
        )

        expense_service = ExpenseService(db)
        return expense_service.get_filtered_expenses(current_user, filters)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# Export endpoints
@router.get("/export/summary", response_model=schemas.ExportSummary,
           summary="Get export summary",
           description="Get summary information for expenses that would be exported with given filters")
def get_export_summary(
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    min_amount: Optional[float] = Query(None),
    max_amount: Optional[float] = Query(None),
    status: Optional[str] = Query(None),
    payer_id: Optional[int] = Query(None),
    group_id: Optional[int] = Query(None),
    category: Optional[str] = Query(None),
    description_contains: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get summary of expenses that would be exported with the given filters."""
    try:
        filters = schemas.ExpenseFilter(
            start_date=start_date,
            end_date=end_date,
            min_amount=min_amount,
            max_amount=max_amount,
            status=status,
            payer_id=payer_id,
            group_id=group_id,
            category=category,
            description_contains=description_contains
        )

        export_service = ExportService(db)
        return export_service.get_export_summary(current_user, filters)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/export/csv",
           summary="Export expenses to CSV",
           description="Export filtered expenses to CSV format")
def export_expenses_csv(
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    min_amount: Optional[float] = Query(None),
    max_amount: Optional[float] = Query(None),
    status: Optional[str] = Query(None),
    payer_id: Optional[int] = Query(None),
    group_id: Optional[int] = Query(None),
    category: Optional[str] = Query(None),
    description_contains: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Export expenses to CSV format."""
    try:
        filters = schemas.ExpenseFilter(
            start_date=start_date,
            end_date=end_date,
            min_amount=min_amount,
            max_amount=max_amount,
            status=status,
            payer_id=payer_id,
            group_id=group_id,
            category=category,
            description_contains=description_contains
        )

        export_service = ExportService(db)
        csv_data = export_service.export_expenses_csv(current_user, filters)

        # Create filename with timestamp
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"expenses_export_{timestamp}.csv"

        return StreamingResponse(
            io.StringIO(csv_data.getvalue()),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/export/pdf",
           summary="Export expenses to PDF",
           description="Export filtered expenses to PDF format")
def export_expenses_pdf(
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    min_amount: Optional[float] = Query(None),
    max_amount: Optional[float] = Query(None),
    status: Optional[str] = Query(None),
    payer_id: Optional[int] = Query(None),
    group_id: Optional[int] = Query(None),
    category: Optional[str] = Query(None),
    description_contains: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Export expenses to PDF format."""
    try:
        filters = schemas.ExpenseFilter(
            start_date=start_date,
            end_date=end_date,
            min_amount=min_amount,
            max_amount=max_amount,
            status=status,
            payer_id=payer_id,
            group_id=group_id,
            category=category,
            description_contains=description_contains
        )

        export_service = ExportService(db)
        pdf_data = export_service.export_expenses_pdf(current_user, filters)

        # Create filename with timestamp
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"expenses_report_{timestamp}.pdf"

        return StreamingResponse(
            io.BytesIO(pdf_data.getvalue()),
            media_type="application/pdf",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
