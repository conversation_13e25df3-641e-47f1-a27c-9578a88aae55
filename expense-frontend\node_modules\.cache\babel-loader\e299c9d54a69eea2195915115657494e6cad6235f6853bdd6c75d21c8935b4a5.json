{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport Layout from '../components/Layout/Layout';\nimport { DollarSign, Users, Receipt, TrendingUp, ArrowUpRight, ArrowDownRight, Plus } from 'lucide-react';\nimport { expensesAPI, groupsAPI } from '../services/api';\nimport { formatCurrency, toNumber } from '../utils/formatters';\nimport { useAutoRefresh } from '../hooks/useAutoRefresh';\nimport { dashboardCircuitBreaker, withCircuitBreaker } from '../utils/circuitBreaker';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const navigate = useNavigate();\n  const [stats, setStats] = useState({\n    totalOwed: 0,\n    totalOwing: 0,\n    totalGroups: 0,\n    recentExpenses: 0\n  });\n  const [balances, setBalances] = useState([]);\n  const [groups, setGroups] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [lastLoadAttempt, setLastLoadAttempt] = useState(0);\n  const loadDashboardData = async () => {\n    const now = Date.now();\n\n    // Prevent rapid successive calls\n    if (now - lastLoadAttempt < 5000) {\n      // 5 second cooldown\n      console.log('Dashboard data load skipped - too soon since last attempt');\n      return;\n    }\n    setLastLoadAttempt(now);\n    try {\n      const result = await withCircuitBreaker(async () => {\n        const [balancesRes, groupsRes] = await Promise.all([expensesAPI.getBalances(), groupsAPI.getMyGroups()]);\n        return {\n          balancesRes,\n          groupsRes\n        };\n      }, dashboardCircuitBreaker, 'Dashboard Data Load');\n      const {\n        balancesRes,\n        groupsRes\n      } = result;\n      const balanceData = balancesRes.data;\n      const groupData = groupsRes.data;\n      setBalances(balanceData);\n      setGroups(groupData);\n\n      // Calculate stats\n      const totalOwed = balanceData.filter(b => toNumber(b.amount) > 0).reduce((sum, b) => sum + toNumber(b.amount), 0);\n      const totalOwing = Math.abs(balanceData.filter(b => toNumber(b.amount) < 0).reduce((sum, b) => sum + toNumber(b.amount), 0));\n      setStats({\n        totalOwed,\n        totalOwing,\n        totalGroups: groupData.length,\n        recentExpenses: balanceData.length\n      });\n      console.log('Dashboard data loaded successfully');\n    } catch (error) {\n      var _error$message;\n      console.error('Dashboard data load failed:', error);\n\n      // Only show toast if it's not a circuit breaker error\n      if (!((_error$message = error.message) !== null && _error$message !== void 0 && _error$message.includes('Circuit breaker is OPEN'))) {\n        toast.error('Failed to load dashboard data');\n      } else {\n        console.log('Dashboard load blocked by circuit breaker');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  // Auto-refresh data when notifications indicate changes\n  useAutoRefresh(loadDashboardData, []);\n  const StatCard = ({\n    title,\n    value,\n    icon,\n    trend,\n    trendUp,\n    color\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm font-medium text-gray-600\",\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), trend && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center mt-1 text-sm ${trendUp ? 'text-green-600' : 'text-red-600'}`,\n            children: [trendUp ? /*#__PURE__*/_jsxDEV(ArrowUpRight, {\n              className: \"w-4 h-4 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(ArrowDownRight, {\n              className: \"w-4 h-4 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 19\n            }, this), trend]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `p-3 rounded-lg ${color}`,\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"Dashboard\",\n      subtitle: \"Overview of your expenses and balances\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    title: \"Dashboard\",\n    subtitle: \"Overview of your expenses and balances\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total Owed to You\",\n          value: formatCurrency(stats.totalOwed),\n          icon: /*#__PURE__*/_jsxDEV(TrendingUp, {\n            className: \"w-6 h-6 text-green-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 19\n          }, this),\n          color: \"bg-green-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Total You Owe\",\n          value: formatCurrency(stats.totalOwing),\n          icon: /*#__PURE__*/_jsxDEV(DollarSign, {\n            className: \"w-6 h-6 text-red-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 19\n          }, this),\n          color: \"bg-red-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Active Groups\",\n          value: stats.totalGroups.toString(),\n          icon: /*#__PURE__*/_jsxDEV(Users, {\n            className: \"w-6 h-6 text-blue-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 19\n          }, this),\n          color: \"bg-blue-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Recent Balances\",\n          value: stats.recentExpenses.toString(),\n          icon: /*#__PURE__*/_jsxDEV(Receipt, {\n            className: \"w-6 h-6 text-purple-600\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 19\n          }, this),\n          color: \"bg-purple-100\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Recent Balances\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: balances.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(Receipt, {\n                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500\",\n                children: \"No balances yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-400\",\n                children: \"Start by creating an expense\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: balances.slice(0, 5).map((balance, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between py-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-gray-600\",\n                      children: balance.email.charAt(0).toUpperCase()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: balance.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `text-sm font-medium ${toNumber(balance.amount) > 0 ? 'text-green-600' : 'text-red-600'}`,\n                  children: [toNumber(balance.amount) > 0 ? '+' : '', formatCurrency(balance.amount)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Your Groups\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-ghost\",\n              onClick: () => navigate('/groups'),\n              children: [/*#__PURE__*/_jsxDEV(Plus, {\n                className: \"w-4 h-4 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), \"New Group\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-content\",\n            children: groups.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-8\",\n              children: [/*#__PURE__*/_jsxDEV(Users, {\n                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-500\",\n                children: \"No groups yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-400\",\n                children: \"Create or join a group to start\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: groups.slice(0, 5).map(group => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between py-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(Users, {\n                      className: \"w-4 h-4 text-primary-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ml-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: group.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [group.members.length, \" members\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 23\n                }, this)\n              }, group.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"42LRZVI61p6PoRn/IaF9f7iHBqU=\", false, function () {\n  return [useNavigate, useAutoRefresh];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Layout", "DollarSign", "Users", "Receipt", "TrendingUp", "ArrowUpRight", "ArrowDownRight", "Plus", "expensesAPI", "groupsAPI", "formatCurrency", "toNumber", "useAutoRefresh", "dashboardCircuitBreaker", "withCircuitBreaker", "toast", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "navigate", "stats", "setStats", "totalOwed", "totalOwing", "totalGroups", "recentExpenses", "balances", "setBalances", "groups", "setGroups", "loading", "setLoading", "lastLoadAttempt", "setLastLoadAttempt", "loadDashboardData", "now", "Date", "console", "log", "result", "balancesRes", "groupsRes", "Promise", "all", "getBalances", "getMyGroups", "balanceData", "data", "groupData", "filter", "b", "amount", "reduce", "sum", "Math", "abs", "length", "error", "_error$message", "message", "includes", "StatCard", "title", "value", "icon", "trend", "trendUp", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "subtitle", "toString", "slice", "map", "balance", "index", "email", "char<PERSON>t", "toUpperCase", "onClick", "group", "name", "members", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport Layout from '../components/Layout/Layout';\nimport {\n  DollarSign,\n  Users,\n  Receipt,\n  TrendingUp,\n  ArrowUpRight,\n  ArrowDownRight,\n  Plus\n} from 'lucide-react';\nimport { Balance, Group } from '../types';\nimport { expensesAPI, groupsAPI } from '../services/api';\nimport { formatCurrency, toNumber } from '../utils/formatters';\nimport { useAutoRefresh } from '../hooks/useAutoRefresh';\nimport { dashboardCircuitBreaker, withCircuitBreaker } from '../utils/circuitBreaker';\nimport toast from 'react-hot-toast';\n\ninterface DashboardStats {\n  totalOwed: number;\n  totalOwing: number;\n  totalGroups: number;\n  recentExpenses: number;\n}\n\nconst Dashboard: React.FC = () => {\n  const navigate = useNavigate();\n  const [stats, setStats] = useState<DashboardStats>({\n    totalOwed: 0,\n    totalOwing: 0,\n    totalGroups: 0,\n    recentExpenses: 0,\n  });\n  const [balances, setBalances] = useState<Balance[]>([]);\n  const [groups, setGroups] = useState<Group[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [lastLoadAttempt, setLastLoadAttempt] = useState<number>(0);\n\n  const loadDashboardData = async () => {\n    const now = Date.now();\n\n    // Prevent rapid successive calls\n    if (now - lastLoadAttempt < 5000) { // 5 second cooldown\n      console.log('Dashboard data load skipped - too soon since last attempt');\n      return;\n    }\n\n    setLastLoadAttempt(now);\n\n    try {\n      const result = await withCircuitBreaker(\n        async () => {\n          const [balancesRes, groupsRes] = await Promise.all([\n            expensesAPI.getBalances(),\n            groupsAPI.getMyGroups(),\n          ]);\n          return { balancesRes, groupsRes };\n        },\n        dashboardCircuitBreaker,\n        'Dashboard Data Load'\n      );\n\n      const { balancesRes, groupsRes } = result;\n      const balanceData = balancesRes.data;\n      const groupData = groupsRes.data;\n\n      setBalances(balanceData);\n      setGroups(groupData);\n\n      // Calculate stats\n      const totalOwed = balanceData\n        .filter(b => toNumber(b.amount) > 0)\n        .reduce((sum, b) => sum + toNumber(b.amount), 0);\n\n      const totalOwing = Math.abs(balanceData\n        .filter(b => toNumber(b.amount) < 0)\n        .reduce((sum, b) => sum + toNumber(b.amount), 0));\n\n      setStats({\n        totalOwed,\n        totalOwing,\n        totalGroups: groupData.length,\n        recentExpenses: balanceData.length,\n      });\n\n      console.log('Dashboard data loaded successfully');\n    } catch (error: any) {\n      console.error('Dashboard data load failed:', error);\n\n      // Only show toast if it's not a circuit breaker error\n      if (!error.message?.includes('Circuit breaker is OPEN')) {\n        toast.error('Failed to load dashboard data');\n      } else {\n        console.log('Dashboard load blocked by circuit breaker');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  // Auto-refresh data when notifications indicate changes\n  useAutoRefresh(loadDashboardData, []);\n\n  const StatCard: React.FC<{\n    title: string;\n    value: string;\n    icon: React.ReactNode;\n    trend?: string;\n    trendUp?: boolean;\n    color: string;\n  }> = ({ title, value, icon, trend, trendUp, color }) => (\n    <div className=\"card\">\n      <div className=\"card-content\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <p className=\"text-sm font-medium text-gray-600\">{title}</p>\n            <p className=\"text-2xl font-bold text-gray-900\">{value}</p>\n            {trend && (\n              <div className={`flex items-center mt-1 text-sm ${\n                trendUp ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {trendUp ? (\n                  <ArrowUpRight className=\"w-4 h-4 mr-1\" />\n                ) : (\n                  <ArrowDownRight className=\"w-4 h-4 mr-1\" />\n                )}\n                {trend}\n              </div>\n            )}\n          </div>\n          <div className={`p-3 rounded-lg ${color}`}>\n            {icon}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  if (loading) {\n    return (\n      <Layout title=\"Dashboard\" subtitle=\"Overview of your expenses and balances\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout title=\"Dashboard\" subtitle=\"Overview of your expenses and balances\">\n      <div className=\"space-y-6\">\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <StatCard\n            title=\"Total Owed to You\"\n            value={formatCurrency(stats.totalOwed)}\n            icon={<TrendingUp className=\"w-6 h-6 text-green-600\" />}\n            color=\"bg-green-100\"\n          />\n          <StatCard\n            title=\"Total You Owe\"\n            value={formatCurrency(stats.totalOwing)}\n            icon={<DollarSign className=\"w-6 h-6 text-red-600\" />}\n            color=\"bg-red-100\"\n          />\n          <StatCard\n            title=\"Active Groups\"\n            value={stats.totalGroups.toString()}\n            icon={<Users className=\"w-6 h-6 text-blue-600\" />}\n            color=\"bg-blue-100\"\n          />\n          <StatCard\n            title=\"Recent Balances\"\n            value={stats.recentExpenses.toString()}\n            icon={<Receipt className=\"w-6 h-6 text-purple-600\" />}\n            color=\"bg-purple-100\"\n          />\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Recent Balances */}\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Recent Balances</h3>\n            </div>\n            <div className=\"card-content\">\n              {balances.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <Receipt className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-500\">No balances yet</p>\n                  <p className=\"text-sm text-gray-400\">Start by creating an expense</p>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  {balances.slice(0, 5).map((balance, index) => (\n                    <div key={index} className=\"flex items-center justify-between py-2\">\n                      <div className=\"flex items-center\">\n                        <div className=\"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center\">\n                          <span className=\"text-sm font-medium text-gray-600\">\n                            {balance.email.charAt(0).toUpperCase()}\n                          </span>\n                        </div>\n                        <div className=\"ml-3\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {balance.email}\n                          </p>\n                        </div>\n                      </div>\n                      <div className={`text-sm font-medium ${\n                        toNumber(balance.amount) > 0 ? 'text-green-600' : 'text-red-600'\n                      }`}>\n                        {toNumber(balance.amount) > 0 ? '+' : ''}{formatCurrency(balance.amount)}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Groups */}\n          <div className=\"card\">\n            <div className=\"card-header flex items-center justify-between\">\n              <h3 className=\"text-lg font-semibold text-gray-900\">Your Groups</h3>\n              <button\n                className=\"btn-ghost\"\n                onClick={() => navigate('/groups')}\n              >\n                <Plus className=\"w-4 h-4 mr-1\" />\n                New Group\n              </button>\n            </div>\n            <div className=\"card-content\">\n              {groups.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <Users className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n                  <p className=\"text-gray-500\">No groups yet</p>\n                  <p className=\"text-sm text-gray-400\">Create or join a group to start</p>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  {groups.slice(0, 5).map((group) => (\n                    <div key={group.id} className=\"flex items-center justify-between py-2\">\n                      <div className=\"flex items-center\">\n                        <div className=\"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\">\n                          <Users className=\"w-4 h-4 text-primary-600\" />\n                        </div>\n                        <div className=\"ml-3\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {group.name}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">\n                            {group.members.length} members\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </Layout>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,6BAA6B;AAChD,SACEC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,YAAY,EACZC,cAAc,EACdC,IAAI,QACC,cAAc;AAErB,SAASC,WAAW,EAAEC,SAAS,QAAQ,iBAAiB;AACxD,SAASC,cAAc,EAAEC,QAAQ,QAAQ,qBAAqB;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,uBAAuB,EAAEC,kBAAkB,QAAQ,yBAAyB;AACrF,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AASpC,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAiB;IACjD0B,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,CAAC;IACdC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACgC,MAAM,EAAEC,SAAS,CAAC,GAAGjC,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAS,CAAC,CAAC;EAEjE,MAAMsC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAIA,GAAG,GAAGH,eAAe,GAAG,IAAI,EAAE;MAAE;MAClCK,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxE;IACF;IAEAL,kBAAkB,CAACE,GAAG,CAAC;IAEvB,IAAI;MACF,MAAMI,MAAM,GAAG,MAAM1B,kBAAkB,CACrC,YAAY;QACV,MAAM,CAAC2B,WAAW,EAAEC,SAAS,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACjDpC,WAAW,CAACqC,WAAW,CAAC,CAAC,EACzBpC,SAAS,CAACqC,WAAW,CAAC,CAAC,CACxB,CAAC;QACF,OAAO;UAAEL,WAAW;UAAEC;QAAU,CAAC;MACnC,CAAC,EACD7B,uBAAuB,EACvB,qBACF,CAAC;MAED,MAAM;QAAE4B,WAAW;QAAEC;MAAU,CAAC,GAAGF,MAAM;MACzC,MAAMO,WAAW,GAAGN,WAAW,CAACO,IAAI;MACpC,MAAMC,SAAS,GAAGP,SAAS,CAACM,IAAI;MAEhCpB,WAAW,CAACmB,WAAW,CAAC;MACxBjB,SAAS,CAACmB,SAAS,CAAC;;MAEpB;MACA,MAAM1B,SAAS,GAAGwB,WAAW,CAC1BG,MAAM,CAACC,CAAC,IAAIxC,QAAQ,CAACwC,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,CAAC,CACnCC,MAAM,CAAC,CAACC,GAAG,EAAEH,CAAC,KAAKG,GAAG,GAAG3C,QAAQ,CAACwC,CAAC,CAACC,MAAM,CAAC,EAAE,CAAC,CAAC;MAElD,MAAM5B,UAAU,GAAG+B,IAAI,CAACC,GAAG,CAACT,WAAW,CACpCG,MAAM,CAACC,CAAC,IAAIxC,QAAQ,CAACwC,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,CAAC,CACnCC,MAAM,CAAC,CAACC,GAAG,EAAEH,CAAC,KAAKG,GAAG,GAAG3C,QAAQ,CAACwC,CAAC,CAACC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;MAEnD9B,QAAQ,CAAC;QACPC,SAAS;QACTC,UAAU;QACVC,WAAW,EAAEwB,SAAS,CAACQ,MAAM;QAC7B/B,cAAc,EAAEqB,WAAW,CAACU;MAC9B,CAAC,CAAC;MAEFnB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IACnD,CAAC,CAAC,OAAOmB,KAAU,EAAE;MAAA,IAAAC,cAAA;MACnBrB,OAAO,CAACoB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;MAEnD;MACA,IAAI,GAAAC,cAAA,GAACD,KAAK,CAACE,OAAO,cAAAD,cAAA,eAAbA,cAAA,CAAeE,QAAQ,CAAC,yBAAyB,CAAC,GAAE;QACvD9C,KAAK,CAAC2C,KAAK,CAAC,+BAA+B,CAAC;MAC9C,CAAC,MAAM;QACLpB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MAC1D;IACF,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDlC,SAAS,CAAC,MAAM;IACdqC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvB,cAAc,CAACuB,iBAAiB,EAAE,EAAE,CAAC;EAErC,MAAM2B,QAOJ,GAAGA,CAAC;IAAEC,KAAK;IAAEC,KAAK;IAAEC,IAAI;IAAEC,KAAK;IAAEC,OAAO;IAAEC;EAAM,CAAC,kBACjDnD,OAAA;IAAKoD,SAAS,EAAC,MAAM;IAAAC,QAAA,eACnBrD,OAAA;MAAKoD,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BrD,OAAA;QAAKoD,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDrD,OAAA;UAAAqD,QAAA,gBACErD,OAAA;YAAGoD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAEP;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5DzD,OAAA;YAAGoD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEN;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC1DR,KAAK,iBACJjD,OAAA;YAAKoD,SAAS,EAAE,kCACdF,OAAO,GAAG,gBAAgB,GAAG,cAAc,EAC1C;YAAAG,QAAA,GACAH,OAAO,gBACNlD,OAAA,CAACZ,YAAY;cAACgE,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEzCzD,OAAA,CAACX,cAAc;cAAC+D,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAC3C,EACAR,KAAK;UAAA;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNzD,OAAA;UAAKoD,SAAS,EAAE,kBAAkBD,KAAK,EAAG;UAAAE,QAAA,EACvCL;QAAI;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,IAAI3C,OAAO,EAAE;IACX,oBACEd,OAAA,CAACjB,MAAM;MAAC+D,KAAK,EAAC,WAAW;MAACY,QAAQ,EAAC,wCAAwC;MAAAL,QAAA,eACzErD,OAAA;QAAKoD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDrD,OAAA;UAAKoD,SAAS,EAAC;QAAiE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;EAEA,oBACEzD,OAAA,CAACjB,MAAM;IAAC+D,KAAK,EAAC,WAAW;IAACY,QAAQ,EAAC,wCAAwC;IAAAL,QAAA,eACzErD,OAAA;MAAKoD,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExBrD,OAAA;QAAKoD,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBACnErD,OAAA,CAAC6C,QAAQ;UACPC,KAAK,EAAC,mBAAmB;UACzBC,KAAK,EAAEtD,cAAc,CAACW,KAAK,CAACE,SAAS,CAAE;UACvC0C,IAAI,eAAEhD,OAAA,CAACb,UAAU;YAACiE,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxDN,KAAK,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACFzD,OAAA,CAAC6C,QAAQ;UACPC,KAAK,EAAC,eAAe;UACrBC,KAAK,EAAEtD,cAAc,CAACW,KAAK,CAACG,UAAU,CAAE;UACxCyC,IAAI,eAAEhD,OAAA,CAAChB,UAAU;YAACoE,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtDN,KAAK,EAAC;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,eACFzD,OAAA,CAAC6C,QAAQ;UACPC,KAAK,EAAC,eAAe;UACrBC,KAAK,EAAE3C,KAAK,CAACI,WAAW,CAACmD,QAAQ,CAAC,CAAE;UACpCX,IAAI,eAAEhD,OAAA,CAACf,KAAK;YAACmE,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAClDN,KAAK,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACFzD,OAAA,CAAC6C,QAAQ;UACPC,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAE3C,KAAK,CAACK,cAAc,CAACkD,QAAQ,CAAC,CAAE;UACvCX,IAAI,eAAEhD,OAAA,CAACd,OAAO;YAACkE,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtDN,KAAK,EAAC;QAAe;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENzD,OAAA;QAAKoD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDrD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBrD,OAAA;YAAKoD,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BrD,OAAA;cAAIoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACNzD,OAAA;YAAKoD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1B3C,QAAQ,CAAC8B,MAAM,KAAK,CAAC,gBACpBxC,OAAA;cAAKoD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BrD,OAAA,CAACd,OAAO;gBAACkE,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5DzD,OAAA;gBAAGoD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChDzD,OAAA;gBAAGoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,gBAENzD,OAAA;cAAKoD,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB3C,QAAQ,CAACkD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACvC/D,OAAA;gBAAiBoD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACjErD,OAAA;kBAAKoD,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCrD,OAAA;oBAAKoD,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChFrD,OAAA;sBAAMoD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAChDS,OAAO,CAACE,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;oBAAC;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNzD,OAAA;oBAAKoD,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBrD,OAAA;sBAAGoD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC7CS,OAAO,CAACE;oBAAK;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzD,OAAA;kBAAKoD,SAAS,EAAE,uBACd1D,QAAQ,CAACoE,OAAO,CAAC3B,MAAM,CAAC,GAAG,CAAC,GAAG,gBAAgB,GAAG,cAAc,EAC/D;kBAAAkB,QAAA,GACA3D,QAAQ,CAACoE,OAAO,CAAC3B,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE1C,cAAc,CAACqE,OAAO,CAAC3B,MAAM,CAAC;gBAAA;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC;cAAA,GAjBEM,KAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkBV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzD,OAAA;UAAKoD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBrD,OAAA;YAAKoD,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5DrD,OAAA;cAAIoD,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpEzD,OAAA;cACEoD,SAAS,EAAC,WAAW;cACrBe,OAAO,EAAEA,CAAA,KAAMhE,QAAQ,CAAC,SAAS,CAAE;cAAAkD,QAAA,gBAEnCrD,OAAA,CAACV,IAAI;gBAAC8D,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNzD,OAAA;YAAKoD,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BzC,MAAM,CAAC4B,MAAM,KAAK,CAAC,gBAClBxC,OAAA;cAAKoD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BrD,OAAA,CAACf,KAAK;gBAACmE,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1DzD,OAAA;gBAAGoD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9CzD,OAAA;gBAAGoD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,gBAENzD,OAAA;cAAKoD,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBzC,MAAM,CAACgD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEO,KAAK,iBAC5BpE,OAAA;gBAAoBoD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACpErD,OAAA;kBAAKoD,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChCrD,OAAA;oBAAKoD,SAAS,EAAC,sEAAsE;oBAAAC,QAAA,eACnFrD,OAAA,CAACf,KAAK;sBAACmE,SAAS,EAAC;oBAA0B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACNzD,OAAA;oBAAKoD,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBACnBrD,OAAA;sBAAGoD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC7Ce,KAAK,CAACC;oBAAI;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACJzD,OAAA;sBAAGoD,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GACjCe,KAAK,CAACE,OAAO,CAAC9B,MAAM,EAAC,UACxB;oBAAA;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAbEW,KAAK,CAACG,EAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACvD,EAAA,CArPID,SAAmB;EAAA,QACNnB,WAAW,EA+E5Ba,cAAc;AAAA;AAAA6E,EAAA,GAhFVvE,SAAmB;AAuPzB,eAAeA,SAAS;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}