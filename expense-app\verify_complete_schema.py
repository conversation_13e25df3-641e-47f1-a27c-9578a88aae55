#!/usr/bin/env python3
"""
Verify that all required tables exist in the database
"""

import sqlite3
import os

def check_all_tables():
    """Check if all required tables exist"""
    db_path = "./dev.db"
    
    if not os.path.exists(db_path):
        print("❌ Database file 'dev.db' does not exist!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        
        print("📋 Existing tables in database:")
        for table in table_names:
            print(f"  - {table}")
        
        # Required tables for the application
        required_tables = [
            'users',
            'groups', 
            'expenses',
            'shares',
            'expense_approvals',
            'group_join_requests'
        ]
        
        missing_tables = [table for table in required_tables if table not in table_names]
        
        if missing_tables:
            print(f"\n⚠️ Missing tables: {', '.join(missing_tables)}")
            print("These tables will be created when you first use the application.")
        else:
            print(f"\n✅ All core tables exist!")
        
        # Check users table specifically
        if 'users' in table_names:
            cursor.execute("PRAGMA table_info(users);")
            columns = cursor.fetchall()
            print(f"\n👤 Users table has {len(columns)} columns:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False

def test_login_simulation():
    """Simulate the login query that was failing"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        # This simulates what SQLAlchemy does during login
        cursor.execute("""
            SELECT id, email, hashed_password, groq_api_key, 
                   full_name, avatar_url, phone, timezone, currency, language,
                   email_notifications, expense_notifications, 
                   approval_notifications, settlement_notifications,
                   created_at, updated_at
            FROM users 
            WHERE email = ?
        """, ("<EMAIL>",))
        
        result = cursor.fetchone()
        
        if result:
            print("\n✅ Login query simulation successful!")
            print(f"Found user: {result[1]} (ID: {result[0]})")
        else:
            print("\n✅ Login query structure is valid (no user found with that email)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"\n❌ Login query simulation failed: {e}")
        return False

def main():
    """Main function"""
    print("🔍 Complete Database Schema Verification")
    print("=" * 50)
    
    if not check_all_tables():
        return
    
    if not test_login_simulation():
        return
    
    print("\n" + "=" * 50)
    print("🎉 Database verification complete!")
    print("\n✅ The 'no such column: users.full_name' error should be resolved.")
    print("✅ You can now start the FastAPI server and test login.")
    print("\nTo start the server:")
    print("  uvicorn app.main:app --reload")

if __name__ == "__main__":
    main()
