import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useWebSocket, EnhancedWebSocketMessage, UseWebSocketReturn } from '../hooks/useWebSocket';
import { useAuth } from './AuthContext';
import toast from 'react-hot-toast';

interface WebSocketContextType extends UseWebSocketReturn {
  lastMessage: EnhancedWebSocketMessage | null;
  messageHistory: EnhancedWebSocketMessage[];
  clearMessageHistory: () => void;
}

const WebSocketContext = createContext<WebSocketContextType | null>(null);

interface WebSocketProviderProps {
  children: ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [lastMessage, setLastMessage] = useState<EnhancedWebSocketMessage | null>(null);
  const [messageHistory, setMessageHistory] = useState<EnhancedWebSocketMessage[]>([]);
  const [hasShownConnectionError, setHasShownConnectionError] = useState(false);
  const [hasShownDisconnectionError, setHasShownDisconnectionError] = useState(false);

  const webSocketReturn = useWebSocket({
    onMessage: (message: EnhancedWebSocketMessage) => {
      console.log('WebSocket message received:', message);
      
      setLastMessage(message);
      setMessageHistory(prev => [message, ...prev.slice(0, 99)]); // Keep last 100 messages
      
      // Handle different message types
      switch (message.type) {
        case 'expense_created':
        case 'expense_approved':
        case 'expense_rejected':
        case 'expense_updated':
        case 'expense_deleted':
          // Show toast for expense-related notifications
          if (message.title && message.message) {
            toast(message.message, {
              icon: '💰',
              duration: 4000,
            });
          }
          break;
          
        case 'member_joined':
        case 'member_left':
        case 'group_settings_changed':
        case 'join_request_received':
          // Show toast for group-related notifications
          if (message.title && message.message) {
            toast(message.message, {
              icon: '👥',
              duration: 4000,
            });
          }
          break;
          
        case 'settlement_initiated':
        case 'settlement_received':
        case 'settlement_accepted':
        case 'settlement_disputed':
        case 'settlement_completed':
          // Show toast for settlement-related notifications
          if (message.title && message.message) {
            toast(message.message, {
              icon: '💸',
              duration: 5000,
            });
          }
          break;
          
        case 'expense_needs_approval':
        case 'expense_approval_reminder':
          // Show toast for approval-related notifications with higher priority
          if (message.title && message.message) {
            toast(message.message, {
              icon: '⏰',
              duration: 6000,
            });
          }
          break;
          
        case 'system_maintenance':
        case 'system_update':
          // Show toast for system notifications
          if (message.title && message.message) {
            toast(message.message, {
              icon: '🔧',
              duration: 8000,
            });
          }
          break;
          
        default:
          // Generic notification
          if (message.title && message.message) {
            toast(message.message, {
              icon: '📢',
              duration: 4000,
            });
          }
          break;
      }
    },
    onConnect: () => {
      console.log('WebSocket connected successfully');
      setHasShownConnectionError(false);
      setHasShownDisconnectionError(false);

      // Only show success toast if user is authenticated
      if (user) {
        toast.success('Real-time notifications connected', {
          duration: 2000,
        });
      }
    },
    onDisconnect: () => {
      console.log('WebSocket disconnected');

      // Only show disconnection toast once and if user is authenticated
      if (user && !hasShownDisconnectionError) {
        setHasShownDisconnectionError(true);
        toast.error('Real-time notifications disconnected', {
          duration: 3000,
        });
      }
    },
    onError: (error) => {
      console.error('WebSocket connection error:', error);

      // Only show error toast once and if user is authenticated
      if (user && !hasShownConnectionError) {
        setHasShownConnectionError(true);
        toast.error('Notification connection error', {
          duration: 4000,
        });
      }
    },
    autoReconnect: true,
    maxReconnectAttempts: 5,
    reconnectInterval: 2000,
  });

  const clearMessageHistory = () => {
    setMessageHistory([]);
    setLastMessage(null);
  };

  // Only connect when user is authenticated and token is available
  useEffect(() => {
    const token = localStorage.getItem('token');

    if (user && token) {
      console.log('User authenticated, connecting WebSocket...');
      setHasShownConnectionError(false);
      setHasShownDisconnectionError(false);
      webSocketReturn.connect();
    } else {
      console.log('User not authenticated or no token, disconnecting WebSocket...');
      webSocketReturn.disconnect();
      clearMessageHistory();
      setHasShownConnectionError(false);
      setHasShownDisconnectionError(false);
    }
  }, [user, webSocketReturn.connect, webSocketReturn.disconnect]);

  const contextValue: WebSocketContextType = {
    ...webSocketReturn,
    lastMessage,
    messageHistory,
    clearMessageHistory,
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocketContext = (): WebSocketContextType => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocketContext must be used within a WebSocketProvider');
  }
  return context;
};

export default WebSocketContext;
