"""Add categorization tables

Revision ID: 005_add_categorization_tables
Revises: 004_add_recurring_expenses
Create Date: 2024-01-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '005_add_categorization_tables'
down_revision = '004_add_recurring_expenses'
branch_labels = None
depends_on = None


def upgrade():
    # Create expense_categories table
    op.create_table('expense_categories',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('color', sa.String(), nullable=True),
        sa.Column('icon', sa.String(), nullable=True),
        sa.Column('parent_id', sa.Integer(), nullable=True),
        sa.Column('is_system', sa.<PERSON>(), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['parent_id'], ['expense_categories.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_expense_categories_id'), 'expense_categories', ['id'], unique=False)

    # Create expense_tags table
    op.create_table('expense_tags',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('color', sa.String(), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_expense_tags_id'), 'expense_tags', ['id'], unique=False)

    # Create expense_tag_associations table
    op.create_table('expense_tag_associations',
        sa.Column('expense_id', sa.Integer(), nullable=False),
        sa.Column('tag_id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['expense_id'], ['expenses.id'], ),
        sa.ForeignKeyConstraint(['tag_id'], ['expense_tags.id'], ),
        sa.PrimaryKeyConstraint('expense_id', 'tag_id')
    )

    # Add category_id column to expenses table
    op.add_column('expenses', sa.Column('category_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'expenses', 'expense_categories', ['category_id'], ['id'])

    # Set default values for new columns (SQLite compatible)
    op.execute("UPDATE expense_categories SET color = '#6B7280' WHERE color IS NULL")
    op.execute("UPDATE expense_categories SET is_system = 0 WHERE is_system IS NULL")
    op.execute("UPDATE expense_tags SET color = '#3B82F6' WHERE color IS NULL")
    op.execute("UPDATE expense_categories SET created_at = datetime('now') WHERE created_at IS NULL")
    op.execute("UPDATE expense_categories SET updated_at = datetime('now') WHERE updated_at IS NULL")
    op.execute("UPDATE expense_tags SET created_at = datetime('now') WHERE created_at IS NULL")


def downgrade():
    # Remove foreign key and column from expenses table
    op.drop_constraint(None, 'expenses', type_='foreignkey')
    op.drop_column('expenses', 'category_id')
    
    # Drop tables
    op.drop_table('expense_tag_associations')
    op.drop_index(op.f('ix_expense_tags_id'), table_name='expense_tags')
    op.drop_table('expense_tags')
    op.drop_index(op.f('ix_expense_categories_id'), table_name='expense_categories')
    op.drop_table('expense_categories')
