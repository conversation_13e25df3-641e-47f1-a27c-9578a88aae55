#!/usr/bin/env python3
"""
Setup script for the expense tracking application
"""

import os
import sys
import subprocess
import platform

def run_command(command, cwd=None, shell=False):
    """Run a command and return success status"""
    try:
        print(f"Running: {command}")
        if shell or platform.system() == "Windows":
            result = subprocess.run(command, shell=True, cwd=cwd, check=True)
        else:
            result = subprocess.run(command.split(), cwd=cwd, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {e}")
        return False

def setup_backend():
    """Setup backend dependencies and database"""
    print("\n🔧 Setting up backend...")
    
    backend_dir = "expense-app"
    
    if not os.path.exists(backend_dir):
        print(f"❌ Backend directory '{backend_dir}' not found!")
        return False
    
    # Install Python dependencies
    print("📦 Installing Python dependencies...")
    if not run_command("pip install -r requirements.txt", cwd=backend_dir):
        return False
    
    # Run database migrations
    print("🗄️ Running database migrations...")
    if not run_command("python run_migrations.py", cwd=backend_dir):
        print("⚠️ Migration failed, but continuing...")
    
    print("✅ Backend setup completed!")
    return True

def setup_frontend():
    """Setup frontend dependencies"""
    print("\n🔧 Setting up frontend...")
    
    frontend_dir = "expense-frontend"
    
    if not os.path.exists(frontend_dir):
        print(f"❌ Frontend directory '{frontend_dir}' not found!")
        return False
    
    # Install Node.js dependencies
    print("📦 Installing Node.js dependencies...")
    if not run_command("npm install", cwd=frontend_dir):
        return False
    
    print("✅ Frontend setup completed!")
    return True

def check_prerequisites():
    """Check if required tools are installed"""
    print("🔍 Checking prerequisites...")
    
    # Check Python
    try:
        result = subprocess.run([sys.executable, "--version"], capture_output=True, text=True)
        print(f"✅ Python: {result.stdout.strip()}")
    except:
        print("❌ Python not found!")
        return False
    
    # Check pip
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], capture_output=True, text=True)
        print(f"✅ pip: {result.stdout.strip()}")
    except:
        print("❌ pip not found!")
        return False
    
    # Check Node.js
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        print(f"✅ Node.js: {result.stdout.strip()}")
    except:
        print("❌ Node.js not found! Please install Node.js from https://nodejs.org/")
        return False
    
    # Check npm
    try:
        result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
        print(f"✅ npm: {result.stdout.strip()}")
    except:
        print("❌ npm not found!")
        return False
    
    return True

def create_env_files():
    """Create environment files if they don't exist"""
    print("\n📝 Creating environment files...")
    
    # Backend .env
    backend_env = "expense-app/.env"
    if not os.path.exists(backend_env):
        env_content = """# Database Configuration
DATABASE_URL=postgresql://username:password@localhost/expense_tracker

# Security
SECRET_KEY=your-secret-key-here-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
"""
        with open(backend_env, 'w') as f:
            f.write(env_content)
        print(f"✅ Created {backend_env}")
    else:
        print(f"✅ {backend_env} already exists")
    
    # Frontend .env
    frontend_env = "expense-frontend/.env"
    if not os.path.exists(frontend_env):
        env_content = """REACT_APP_API_URL=http://localhost:8000
"""
        with open(frontend_env, 'w') as f:
            f.write(env_content)
        print(f"✅ Created {frontend_env}")
    else:
        print(f"✅ {frontend_env} already exists")

def main():
    """Main setup function"""
    print("🚀 Expense Tracker Application Setup")
    print("=" * 50)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed!")
        sys.exit(1)
    
    # Create environment files
    create_env_files()
    
    # Setup backend
    if not setup_backend():
        print("\n❌ Backend setup failed!")
        sys.exit(1)
    
    # Setup frontend
    if not setup_frontend():
        print("\n❌ Frontend setup failed!")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Update the .env files with your actual database credentials")
    print("2. Start the backend: cd expense-app && uvicorn app.main:app --reload")
    print("3. Start the frontend: cd expense-frontend && npm start")
    print("4. Open http://localhost:3000 in your browser")
    print("\n📚 For more information, check the README.md file")

if __name__ == "__main__":
    main()
