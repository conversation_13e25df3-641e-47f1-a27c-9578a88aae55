"""
WebSocket manager for real-time notifications
"""
import json
import asyncio
from typing import Dict, Set, Any, Optional
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
from .models import User
import logging

logger = logging.getLogger(__name__)

class ConnectionManager:
    """Manages WebSocket connections for real-time notifications"""
    
    def __init__(self):
        # Store active connections by user_id
        self.active_connections: Dict[int, Set[WebSocket]] = {}
        # Store user_id by websocket for cleanup
        self.connection_users: Dict[WebSocket, int] = {}
    
    async def connect(self, websocket: WebSocket, user_id: int):
        """Accept a new WebSocket connection for a user"""
        await websocket.accept()
        
        if user_id not in self.active_connections:
            self.active_connections[user_id] = set()
        
        self.active_connections[user_id].add(websocket)
        self.connection_users[websocket] = user_id
        
        logger.info(f"User {user_id} connected via WebSocket")
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection"""
        if websocket in self.connection_users:
            user_id = self.connection_users[websocket]
            
            # Remove from active connections
            if user_id in self.active_connections:
                self.active_connections[user_id].discard(websocket)
                
                # Clean up empty sets
                if not self.active_connections[user_id]:
                    del self.active_connections[user_id]
            
            # Remove from connection mapping
            del self.connection_users[websocket]
            
            logger.info(f"User {user_id} disconnected from WebSocket")
    
    async def send_personal_message(self, message: dict, user_id: int):
        """Send a message to a specific user's connections"""
        if user_id in self.active_connections:
            disconnected_connections = []
            
            for connection in self.active_connections[user_id].copy():
                try:
                    await connection.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error sending message to user {user_id}: {e}")
                    disconnected_connections.append(connection)
            
            # Clean up disconnected connections
            for connection in disconnected_connections:
                self.disconnect(connection)
    
    async def send_group_message(self, message: dict, user_ids: list[int]):
        """Send a message to multiple users (e.g., group members)"""
        for user_id in user_ids:
            await self.send_personal_message(message, user_id)
    
    async def broadcast_message(self, message: dict):
        """Send a message to all connected users"""
        for user_id in list(self.active_connections.keys()):
            await self.send_personal_message(message, user_id)
    
    def get_connected_users(self) -> list[int]:
        """Get list of currently connected user IDs"""
        return list(self.active_connections.keys())
    
    def is_user_connected(self, user_id: int) -> bool:
        """Check if a user has any active connections"""
        return user_id in self.active_connections and len(self.active_connections[user_id]) > 0

# Global connection manager instance
manager = ConnectionManager()

class NotificationTypes:
    """Constants for notification types"""
    # Expense notifications
    EXPENSE_CREATED = "expense_created"
    EXPENSE_UPDATED = "expense_updated"
    EXPENSE_DELETED = "expense_deleted"
    EXPENSE_APPROVED = "expense_approved"
    EXPENSE_REJECTED = "expense_rejected"
    EXPENSE_NEEDS_APPROVAL = "expense_needs_approval"
    EXPENSE_APPROVAL_REMINDER = "expense_approval_reminder"

    # Group notifications
    GROUP_MEMBER_JOINED = "group_member_joined"
    GROUP_MEMBER_LEFT = "group_member_left"
    GROUP_SETTINGS_CHANGED = "group_settings_changed"
    GROUP_OWNERSHIP_TRANSFERRED = "group_ownership_transferred"

    # Join request notifications
    JOIN_REQUEST_RECEIVED = "join_request_received"
    JOIN_REQUEST_APPROVED = "join_request_approved"
    JOIN_REQUEST_REJECTED = "join_request_rejected"

    # Settlement notifications
    SETTLEMENT_INITIATED = "settlement_initiated"
    SETTLEMENT_RECEIVED = "settlement_received"
    SETTLEMENT_ACCEPTED = "settlement_accepted"
    SETTLEMENT_DISPUTED = "settlement_disputed"
    SETTLEMENT_COMPLETED = "settlement_completed"

    # System notifications
    SYSTEM_MAINTENANCE = "system_maintenance"
    SYSTEM_UPDATE = "system_update"

class EnhancedNotificationService:
    """Enhanced service for sending comprehensive multi-user notifications"""

    @staticmethod
    def _create_notification_data(notification_type: str, title: str, message: str,
                                action_url: str = None, action_text: str = None,
                                priority: str = "medium", **kwargs) -> dict:
        """Create standardized notification data structure"""
        return {
            "type": notification_type,
            "title": title,
            "message": message,
            "action_url": action_url,
            "action_text": action_text,
            "priority": priority,
            "timestamp": kwargs.get("timestamp"),
            "data": kwargs.get("data", {}),
            **kwargs
        }

    # Expense Management Notifications
    @staticmethod
    async def notify_expense_created(expense_data: dict, group_member_ids: list[int],
                                   creator_id: int, creator_name: str, group_name: str):
        """Notify all group members about a new expense"""
        notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.EXPENSE_CREATED,
            "New Expense Added",
            f"{creator_name} added '{expense_data['description']}' for PKR {expense_data['total']:.2f} in {group_name}",
            action_url="/approvals",
            action_text="Review Expense",
            data=expense_data,
            timestamp=expense_data.get("created_at")
        )

        # Send to all group members (including creator for confirmation)
        await manager.send_group_message(notification, group_member_ids)

    @staticmethod
    async def notify_expense_approved(expense_data: dict, approver_name: str,
                                    group_member_ids: list[int], group_name: str):
        """Notify expense creator and all group members about approval"""
        notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.EXPENSE_APPROVED,
            "Expense Approved",
            f"{approver_name} approved '{expense_data['description']}' in {group_name}",
            action_url="/expenses/history",
            action_text="View Expenses",
            data=expense_data,
            timestamp=expense_data.get("approved_at")
        )

        await manager.send_group_message(notification, group_member_ids)

    @staticmethod
    async def notify_expense_rejected(expense_data: dict, approver_name: str,
                                    group_member_ids: list[int], group_name: str):
        """Notify expense creator and all group members about rejection"""
        notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.EXPENSE_REJECTED,
            "Expense Rejected",
            f"{approver_name} rejected '{expense_data['description']}' in {group_name}",
            action_url="/approvals",
            action_text="View Details",
            priority="high",
            data=expense_data,
            timestamp=expense_data.get("approved_at")
        )

        await manager.send_group_message(notification, group_member_ids)

    @staticmethod
    async def notify_expense_updated(expense_data: dict, updater_name: str,
                                   group_member_ids: list[int], group_name: str):
        """Notify all group members about expense updates"""
        notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.EXPENSE_UPDATED,
            "Expense Updated",
            f"{updater_name} updated '{expense_data['description']}' in {group_name}",
            action_url="/expenses/history",
            action_text="View Changes",
            data=expense_data
        )

        await manager.send_group_message(notification, group_member_ids)

    @staticmethod
    async def notify_expense_deleted(expense_data: dict, deleter_name: str,
                                   group_member_ids: list[int], group_name: str):
        """Notify all group members about expense deletion"""
        notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.EXPENSE_DELETED,
            "Expense Deleted",
            f"{deleter_name} deleted '{expense_data['description']}' from {group_name}",
            action_url="/expenses/history",
            action_text="View Expenses",
            priority="high",
            data=expense_data
        )

        await manager.send_group_message(notification, group_member_ids)

    @staticmethod
    async def notify_expense_needs_approval(expense_data: dict, approver_ids: list[int],
                                          creator_name: str, group_name: str):
        """Notify users with approval permissions about pending expense"""
        notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.EXPENSE_NEEDS_APPROVAL,
            "Approval Required",
            f"{creator_name} submitted '{expense_data['description']}' for approval in {group_name}",
            action_url="/approvals",
            action_text="Review & Approve",
            priority="high",
            data=expense_data
        )

        await manager.send_group_message(notification, approver_ids)

    # Group Management Notifications
    @staticmethod
    async def notify_member_joined(new_member_name: str, group_name: str,
                                 existing_member_ids: list[int], group_id: int):
        """Notify existing group members about new member"""
        notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.GROUP_MEMBER_JOINED,
            "New Member Joined",
            f"{new_member_name} joined {group_name}",
            action_url=f"/group-management/groups/{group_id}/details",
            action_text="View Group",
            data={"group_name": group_name, "member_name": new_member_name}
        )

        await manager.send_group_message(notification, existing_member_ids)

    @staticmethod
    async def notify_member_left(departed_member_name: str, group_name: str,
                               remaining_member_ids: list[int], group_id: int):
        """Notify remaining group members about member leaving"""
        notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.GROUP_MEMBER_LEFT,
            "Member Left Group",
            f"{departed_member_name} left {group_name}",
            action_url=f"/group-management/groups/{group_id}/details",
            action_text="View Group",
            data={"group_name": group_name, "member_name": departed_member_name}
        )

        await manager.send_group_message(notification, remaining_member_ids)

    @staticmethod
    async def notify_group_settings_changed(changer_name: str, group_name: str,
                                          group_member_ids: list[int], group_id: int,
                                          changes: dict):
        """Notify all group members about settings changes"""
        notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.GROUP_SETTINGS_CHANGED,
            "Group Settings Updated",
            f"{changer_name} updated settings for {group_name}",
            action_url=f"/group-management/groups/{group_id}/details",
            action_text="View Changes",
            data={"group_name": group_name, "changes": changes}
        )

        await manager.send_group_message(notification, group_member_ids)

    @staticmethod
    async def notify_join_request_received(requester_name: str, group_name: str,
                                         owner_and_admin_ids: list[int], group_id: int):
        """Notify group owner and admins about join request"""
        notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.JOIN_REQUEST_RECEIVED,
            "Join Request Received",
            f"{requester_name} wants to join {group_name}",
            action_url=f"/group-management/groups/{group_id}/details",
            action_text="Review Request",
            priority="high",
            data={"group_name": group_name, "requester_name": requester_name}
        )

        await manager.send_group_message(notification, owner_and_admin_ids)

    @staticmethod
    async def notify_join_request_processed(user_id: int, group_name: str,
                                          approved: bool, processor_name: str):
        """Notify user about join request decision"""
        status = "approved" if approved else "rejected"
        notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.JOIN_REQUEST_APPROVED if approved else NotificationTypes.JOIN_REQUEST_REJECTED,
            f"Join Request {status.title()}",
            f"{processor_name} {status} your request to join '{group_name}'",
            action_url="/group-management" if approved else None,
            action_text="View Groups" if approved else None,
            priority="high" if not approved else "medium",
            data={"group_name": group_name, "approved": approved}
        )

        await manager.send_personal_message(notification, user_id)

    # Settlement Notifications
    @staticmethod
    async def notify_settlement_initiated(settlement_data: dict, recipient_id: int,
                                        sender_name: str, group_member_ids: list[int]):
        """Notify recipient and group members about settlement initiation"""
        # Notify recipient
        recipient_notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.SETTLEMENT_RECEIVED,
            "Settlement Received",
            f"{sender_name} sent you PKR {settlement_data['amount']:.2f}",
            action_url="/settlements",
            action_text="Review Settlement",
            priority="high",
            data=settlement_data
        )
        await manager.send_personal_message(recipient_notification, recipient_id)

        # Notify group members
        group_notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.SETTLEMENT_INITIATED,
            "Settlement Initiated",
            f"{sender_name} initiated a settlement of PKR {settlement_data['amount']:.2f}",
            action_url="/settlements",
            action_text="View Settlements",
            data=settlement_data
        )
        await manager.send_group_message(group_notification, group_member_ids)

    @staticmethod
    async def notify_settlement_accepted(settlement_data: dict, sender_id: int,
                                       accepter_name: str, group_member_ids: list[int]):
        """Notify sender and group members about settlement acceptance"""
        # Notify sender
        sender_notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.SETTLEMENT_ACCEPTED,
            "Settlement Accepted",
            f"{accepter_name} accepted your settlement of PKR {settlement_data['amount']:.2f}",
            action_url="/settlements",
            action_text="View Details",
            data=settlement_data
        )
        await manager.send_personal_message(sender_notification, sender_id)

        # Notify group members
        group_notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.SETTLEMENT_COMPLETED,
            "Settlement Completed",
            f"Settlement of PKR {settlement_data['amount']:.2f} between {settlement_data.get('sender_name', 'User')} and {accepter_name} completed",
            action_url="/settlements",
            action_text="View Settlements",
            data=settlement_data
        )
        await manager.send_group_message(group_notification, group_member_ids)

    @staticmethod
    async def notify_settlement_disputed(settlement_data: dict, disputer_name: str,
                                       owner_and_admin_ids: list[int], involved_party_ids: list[int]):
        """Notify group owner/admins and involved parties about settlement dispute"""
        # Notify owner and admins
        admin_notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.SETTLEMENT_DISPUTED,
            "Settlement Disputed",
            f"{disputer_name} disputed a settlement of PKR {settlement_data['amount']:.2f}",
            action_url="/settlements",
            action_text="Resolve Dispute",
            priority="urgent",
            data=settlement_data
        )
        await manager.send_group_message(admin_notification, owner_and_admin_ids)

        # Notify involved parties
        party_notification = EnhancedNotificationService._create_notification_data(
            NotificationTypes.SETTLEMENT_DISPUTED,
            "Settlement Disputed",
            f"Your settlement of PKR {settlement_data['amount']:.2f} has been disputed",
            action_url="/settlements",
            action_text="View Details",
            priority="high",
            data=settlement_data
        )
        await manager.send_group_message(party_notification, involved_party_ids)


# Maintain backward compatibility
NotificationService = EnhancedNotificationService

# Export the manager and service
__all__ = ["manager", "NotificationService", "NotificationTypes"]
