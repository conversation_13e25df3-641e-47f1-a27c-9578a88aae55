import { useEffect, useRef, useState, useCallback } from 'react';

// WebSocket message types based on backend NotificationTypes
export interface WebSocketMessage {
  type: string;
  title?: string;
  message?: string;
  action_url?: string;
  action_text?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  category?: 'expense' | 'group' | 'settlement' | 'approval' | 'system';
  timestamp?: string;
  data?: Record<string, any>;
  expense_id?: number;
  group_id?: number;
  settlement_id?: number;
  user_id?: number;
}

// Enhanced notification message structure
export interface EnhancedWebSocketMessage extends WebSocketMessage {
  // Additional fields for enhanced notifications
  status?: 'unread' | 'read' | 'archived';
  expires_at?: string;
}

// WebSocket hook configuration
export interface UseWebSocketConfig {
  onMessage?: (message: EnhancedWebSocketMessage) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Event) => void;
  autoReconnect?: boolean;
  maxReconnectAttempts?: number;
  reconnectInterval?: number;
}

// WebSocket hook return type
export interface UseWebSocketReturn {
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;
  sendMessage: (message: any) => void;
  connect: () => void;
  disconnect: () => void;
  reconnectAttempts: number;
}

// Custom hook for WebSocket connection
export const useWebSocket = (config: UseWebSocketConfig = {}): UseWebSocketReturn => {
  const {
    onMessage,
    onConnect,
    onDisconnect,
    onError,
    autoReconnect = true,
    maxReconnectAttempts = 5,
    reconnectInterval = 1000,
  } = config;

  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);

  const websocketRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const shouldReconnectRef = useRef(true);
  const connectionDebounceRef = useRef<NodeJS.Timeout | null>(null);
  const lastConnectionAttemptRef = useRef<number>(0);

  // Get authentication token from localStorage
  const getAuthToken = useCallback(() => {
    return localStorage.getItem('token') || '';
  }, []);

  // Create WebSocket URL
  const getWebSocketUrl = useCallback(() => {
    const token = getAuthToken();
    if (!token) {
      throw new Error('No authentication token available');
    }

    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsHost = process.env.REACT_APP_WS_URL || 'localhost:8000';
    const wsUrl = `${wsProtocol}//${wsHost}/ws/notifications?token=${encodeURIComponent(token)}`;

    console.log('WebSocket URL:', wsUrl.replace(/token=[^&]+/, 'token=***'));
    return wsUrl;
  }, [getAuthToken]);

  // Send message through WebSocket
  const sendMessage = useCallback((message: any) => {
    if (websocketRef.current && websocketRef.current.readyState === WebSocket.OPEN) {
      try {
        websocketRef.current.send(JSON.stringify(message));
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
      }
    } else {
      console.warn('WebSocket is not connected. Cannot send message:', message);
    }
  }, []);

  // Handle WebSocket message
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message: EnhancedWebSocketMessage = JSON.parse(event.data);
      
      // Handle system messages
      switch (message.type) {
        case 'connection_established':
          console.log('WebSocket connection established');
          setConnectionError(null);
          setReconnectAttempts(0);
          break;
        
        case 'heartbeat':
          // Respond to heartbeat
          sendMessage({ type: 'ping' });
          break;
        
        case 'error':
          console.error('WebSocket error message:', message);
          setConnectionError(message.message || 'WebSocket error');
          break;
        
        default:
          // Pass message to callback
          if (onMessage) {
            onMessage(message);
          }
          break;
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
      setConnectionError('Failed to parse message');
    }
  }, [onMessage, sendMessage]);

  // Connect to WebSocket with debouncing
  const connect = useCallback(() => {
    // Debounce connection attempts to prevent rapid-fire connections
    const now = Date.now();
    const timeSinceLastAttempt = now - lastConnectionAttemptRef.current;
    const minInterval = 1000; // Minimum 1 second between connection attempts

    if (timeSinceLastAttempt < minInterval) {
      console.log(`WebSocket connection debounced, waiting ${minInterval - timeSinceLastAttempt}ms`);

      // Clear any existing debounce timeout
      if (connectionDebounceRef.current) {
        clearTimeout(connectionDebounceRef.current);
      }

      // Schedule the connection attempt
      connectionDebounceRef.current = setTimeout(() => {
        connect();
      }, minInterval - timeSinceLastAttempt);
      return;
    }

    // Prevent multiple simultaneous connections
    if (websocketRef.current?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected, skipping connection attempt');
      return; // Already connected
    }

    if (websocketRef.current?.readyState === WebSocket.CONNECTING || isConnecting) {
      console.log('WebSocket connection already in progress, skipping duplicate attempt');
      return; // Connection in progress
    }

    // Clean up any existing connection in a bad state
    if (websocketRef.current && websocketRef.current.readyState !== WebSocket.CLOSED) {
      console.log('Cleaning up existing WebSocket connection');
      websocketRef.current.close();
      websocketRef.current = null;
    }

    const token = getAuthToken();
    if (!token) {
      console.warn('Cannot connect WebSocket: No authentication token available');
      setConnectionError('No authentication token available');
      return;
    }

    // Update last connection attempt timestamp
    lastConnectionAttemptRef.current = now;

    console.log('Initiating WebSocket connection...');
    setIsConnecting(true);
    setConnectionError(null);

    try {
      const wsUrl = getWebSocketUrl();
      console.log('Creating WebSocket connection to:', wsUrl.replace(/token=[^&]+/, 'token=***'));

      const ws = new WebSocket(wsUrl);
      websocketRef.current = ws;

      ws.onopen = () => {
        console.log('WebSocket connected successfully');
        setIsConnected(true);
        setIsConnecting(false);
        setConnectionError(null);
        setReconnectAttempts(0);
        
        if (onConnect) {
          onConnect();
        }
      };

      ws.onmessage = handleMessage;

      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        setIsConnecting(false);
        websocketRef.current = null;

        if (onDisconnect) {
          onDisconnect();
        }

        // Attempt to reconnect if not a normal closure and auto-reconnect is enabled
        if (
          autoReconnect &&
          shouldReconnectRef.current &&
          event.code !== 1000 &&
          reconnectAttempts < maxReconnectAttempts
        ) {
          const delay = Math.min(reconnectInterval * Math.pow(2, reconnectAttempts), 30000); // Cap at 30 seconds
          console.log(`Attempting to reconnect in ${delay}ms (attempt ${reconnectAttempts + 1}/${maxReconnectAttempts})`);

          setReconnectAttempts(prev => prev + 1);

          reconnectTimeoutRef.current = setTimeout(() => {
            if (shouldReconnectRef.current) {
              // Check if we still have a valid token before reconnecting
              const token = localStorage.getItem('token');
              if (token) {
                connect();
              } else {
                console.log('No token available, stopping reconnection attempts');
                setConnectionError('Authentication token not available');
                shouldReconnectRef.current = false;
              }
            }
          }, delay);
        } else if (reconnectAttempts >= maxReconnectAttempts) {
          console.log('Maximum reconnection attempts reached, stopping reconnection');
          setConnectionError('Maximum reconnection attempts reached');
          shouldReconnectRef.current = false;
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionError('WebSocket connection error');
        setIsConnecting(false);
        
        if (onError) {
          onError(error);
        }
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setConnectionError('Failed to create WebSocket connection');
      setIsConnecting(false);
    }
  }, [
    isConnecting,
    getAuthToken,
    getWebSocketUrl,
    handleMessage,
    onConnect,
    onDisconnect,
    onError,
    autoReconnect,
    maxReconnectAttempts,
    reconnectInterval,
    reconnectAttempts,
  ]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    shouldReconnectRef.current = false;

    // Clear all timeouts
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (connectionDebounceRef.current) {
      clearTimeout(connectionDebounceRef.current);
      connectionDebounceRef.current = null;
    }

    if (websocketRef.current) {
      websocketRef.current.close(1000, 'User disconnected');
      websocketRef.current = null;
    }

    setIsConnected(false);
    setIsConnecting(false);
    setConnectionError(null);
    setReconnectAttempts(0);
  }, []);

  // Auto-connect on mount and when token changes
  useEffect(() => {
    const token = getAuthToken();
    if (token && shouldReconnectRef.current) {
      connect();
    }

    return () => {
      shouldReconnectRef.current = false;
      disconnect();
    };
  }, [connect, disconnect, getAuthToken]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      shouldReconnectRef.current = false;

      // Clear all timeouts
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (connectionDebounceRef.current) {
        clearTimeout(connectionDebounceRef.current);
      }

      if (websocketRef.current) {
        websocketRef.current.close(1000, 'Component unmounted');
      }
    };
  }, []);

  return {
    isConnected,
    isConnecting,
    connectionError,
    sendMessage,
    connect,
    disconnect,
    reconnectAttempts,
  };
};

export default useWebSocket;
