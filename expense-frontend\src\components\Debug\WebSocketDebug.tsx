import React, { useState, useEffect } from 'react';
import { useWebSocketContext } from '../../contexts/WebSocketContext';
import { useAuth } from '../../contexts/AuthContext';
import { Wifi, WifiOff, AlertCircle, CheckCircle, RefreshCw, Eye, EyeOff } from 'lucide-react';

const WebSocketDebug: React.FC = () => {
  const { user } = useAuth();
  const {
    isConnected,
    isConnecting,
    connectionError,
    reconnectAttempts,
    connect,
    disconnect,
  } = useWebSocketContext();

  const [showDetails, setShowDetails] = useState(false);
  const [connectionLogs, setConnectionLogs] = useState<string[]>([]);

  // Monitor connection status changes
  useEffect(() => {
    const timestamp = new Date().toLocaleTimeString();
    
    if (isConnected) {
      setConnectionLogs(prev => [`${timestamp}: Connected successfully`, ...prev.slice(0, 9)]);
    } else if (isConnecting) {
      setConnectionLogs(prev => [`${timestamp}: Connecting...`, ...prev.slice(0, 9)]);
    } else if (connectionError) {
      setConnectionLogs(prev => [`${timestamp}: Error - ${connectionError}`, ...prev.slice(0, 9)]);
    }
  }, [isConnected, isConnecting, connectionError]);

  const getConnectionStatus = () => {
    if (isConnected) return { text: 'Connected', color: 'text-green-600', icon: CheckCircle };
    if (isConnecting) return { text: 'Connecting...', color: 'text-yellow-600', icon: RefreshCw };
    if (connectionError) return { text: 'Error', color: 'text-red-600', icon: AlertCircle };
    return { text: 'Disconnected', color: 'text-gray-600', icon: WifiOff };
  };

  const status = getConnectionStatus();
  const StatusIcon = status.icon;

  const checkBackendHealth = async () => {
    try {
      const response = await fetch('http://localhost:8000/ws/health');
      const data = await response.json();
      setConnectionLogs(prev => [
        `${new Date().toLocaleTimeString()}: Backend health - ${data.status} (${data.connected_users} users)`,
        ...prev.slice(0, 9)
      ]);
    } catch (error) {
      setConnectionLogs(prev => [
        `${new Date().toLocaleTimeString()}: Backend health check failed - ${error}`,
        ...prev.slice(0, 9)
      ]);
    }
  };

  const testWebSocketUrl = () => {
    const token = localStorage.getItem('token');
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsHost = process.env.REACT_APP_WS_URL || 'localhost:8000';
    const wsUrl = `${wsProtocol}//${wsHost}/ws/notifications?token=${token ? 'PRESENT' : 'MISSING'}`;
    
    setConnectionLogs(prev => [
      `${new Date().toLocaleTimeString()}: WebSocket URL - ${wsUrl}`,
      ...prev.slice(0, 9)
    ]);
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 max-w-sm z-50">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          {isConnected ? (
            <Wifi className="w-5 h-5 text-green-600" />
          ) : (
            <WifiOff className="w-5 h-5 text-red-600" />
          )}
          <span className="font-medium text-gray-900 dark:text-white">WebSocket</span>
        </div>
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          {showDetails ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
        </button>
      </div>

      {/* Status */}
      <div className="flex items-center space-x-2 mb-3">
        <StatusIcon className={`w-4 h-4 ${status.color}`} />
        <span className={`text-sm font-medium ${status.color}`}>
          {status.text}
        </span>
        {reconnectAttempts > 0 && (
          <span className="text-xs text-yellow-600">
            (Attempt {reconnectAttempts})
          </span>
        )}
      </div>

      {/* Quick Info */}
      <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
        <div>User: {user ? '✅ Authenticated' : '❌ Not authenticated'}</div>
        <div>Token: {localStorage.getItem('token') ? '✅ Present' : '❌ Missing'}</div>
        {connectionError && (
          <div className="text-red-600 dark:text-red-400">
            Error: {connectionError}
          </div>
        )}
      </div>

      {/* Detailed View */}
      {showDetails && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          {/* Controls */}
          <div className="flex space-x-2 mb-3">
            <button
              onClick={isConnected ? disconnect : connect}
              className={`px-3 py-1 rounded text-xs font-medium ${
                isConnected
                  ? 'bg-red-100 text-red-700 hover:bg-red-200'
                  : 'bg-green-100 text-green-700 hover:bg-green-200'
              }`}
            >
              {isConnected ? 'Disconnect' : 'Connect'}
            </button>
            <button
              onClick={checkBackendHealth}
              className="px-3 py-1 rounded text-xs font-medium bg-blue-100 text-blue-700 hover:bg-blue-200"
            >
              Health Check
            </button>
            <button
              onClick={testWebSocketUrl}
              className="px-3 py-1 rounded text-xs font-medium bg-purple-100 text-purple-700 hover:bg-purple-200"
            >
              Test URL
            </button>
          </div>

          {/* Connection Logs */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded p-2">
            <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
              Connection Logs:
            </div>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {connectionLogs.length === 0 ? (
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  No logs yet...
                </div>
              ) : (
                connectionLogs.map((log, index) => (
                  <div
                    key={index}
                    className="text-xs text-gray-600 dark:text-gray-400 font-mono"
                  >
                    {log}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Environment Info */}
          <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
            <div>Protocol: {window.location.protocol === 'https:' ? 'WSS' : 'WS'}</div>
            <div>Host: {process.env.REACT_APP_WS_URL || 'localhost:8000'}</div>
            <div>Environment: {process.env.NODE_ENV}</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WebSocketDebug;
