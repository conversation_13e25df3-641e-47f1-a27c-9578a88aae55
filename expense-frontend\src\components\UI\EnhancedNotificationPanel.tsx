import React, { useState, useEffect } from 'react';
import { <PERSON>, X, <PERSON>, Check<PERSON>he<PERSON>, ExternalLink, Clock, AlertCircle, Info, DollarSign, Users, Settings } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useWebSocketContext } from '../../contexts/WebSocketContext';
import { notificationsAPI } from '../../services/api';
import toast from 'react-hot-toast';

interface EnhancedNotification {
  id: number;
  title: string;
  message: string;
  category: string;
  priority: string;
  status: string;
  action_url?: string;
  action_text?: string;
  expense_id?: number;
  group_id?: number;
  settlement_id?: number;
  data?: string;
  created_at: string;
  read_at?: string;
  expires_at?: string;
}

interface NotificationSummary {
  total_count: number;
  unread_count: number;
  categories: Record<string, number>;
  priorities: Record<string, number>;
}

interface EnhancedNotificationPanelProps {
  className?: string;
}

const EnhancedNotificationPanel: React.FC<EnhancedNotificationPanelProps> = ({ className = '' }) => {
  const [notifications, setNotifications] = useState<EnhancedNotification[]>([]);
  const [summary, setSummary] = useState<NotificationSummary | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const navigate = useNavigate();

  // WebSocket for real-time notifications
  const { isConnected, connectionError, lastMessage } = useWebSocketContext();

  // Handle new WebSocket messages
  useEffect(() => {
    if (lastMessage && lastMessage.type && lastMessage.title && lastMessage.message) {
      // Add new real-time notification to local state
      const newNotification: EnhancedNotification = {
        id: Date.now(), // Temporary ID for real-time notifications
        title: lastMessage.title,
        message: lastMessage.message,
        category: lastMessage.category || 'system',
        priority: lastMessage.priority || 'medium',
        status: 'unread',
        action_url: lastMessage.action_url,
        action_text: lastMessage.action_text,
        expense_id: lastMessage.expense_id,
        group_id: lastMessage.group_id,
        settlement_id: lastMessage.settlement_id,
        data: lastMessage.data ? JSON.stringify(lastMessage.data) : undefined,
        created_at: new Date().toISOString(),
      };

      setNotifications(prev => [newNotification, ...prev]);
      loadSummary(); // Refresh summary
    }
  }, [lastMessage]);

  useEffect(() => {
    loadNotifications();
    loadSummary();
  }, []);

  const loadNotifications = async () => {
    try {
      setIsLoading(true);
      const response = await notificationsAPI.getNotifications(50, 0);
      setNotifications(response.data);
    } catch (error) {
      console.error('Failed to load notifications:', error);
      toast.error('Failed to load notifications');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSummary = async () => {
    try {
      const response = await notificationsAPI.getSummary();
      setSummary(response.data);
    } catch (error) {
      console.error('Failed to load notification summary:', error);
    }
  };

  const handleMarkAsRead = async (notificationId: number) => {
    try {
      await notificationsAPI.markAsRead(notificationId);
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId 
            ? { ...n, status: 'read', read_at: new Date().toISOString() }
            : n
        )
      );
      loadSummary();
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      toast.error('Failed to mark notification as read');
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      const unreadIds = notifications.filter(n => n.status === 'unread').map(n => n.id);
      if (unreadIds.length === 0) return;

      await notificationsAPI.markBulkAsRead(unreadIds);
      setNotifications(prev => 
        prev.map(n => ({ ...n, status: 'read', read_at: new Date().toISOString() }))
      );
      loadSummary();
      toast.success('All notifications marked as read');
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      toast.error('Failed to mark all notifications as read');
    }
  };

  const handleNotificationClick = async (notification: EnhancedNotification) => {
    // Mark as read if unread
    if (notification.status === 'unread') {
      await handleMarkAsRead(notification.id);
    }

    // Navigate to action URL if available
    if (notification.action_url) {
      navigate(notification.action_url);
      setIsOpen(false);
    }
  };

  const getNotificationIcon = (category: string) => {
    switch (category) {
      case 'expense':
        return <DollarSign className="w-4 h-4 text-green-600" />;
      case 'group':
        return <Users className="w-4 h-4 text-blue-600" />;
      case 'settlement':
        return <ExternalLink className="w-4 h-4 text-purple-600" />;
      case 'approval':
        return <Clock className="w-4 h-4 text-orange-600" />;
      case 'system':
        return <Settings className="w-4 h-4 text-gray-600" />;
      default:
        return <Info className="w-4 h-4 text-gray-600" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500 bg-red-50 dark:bg-red-900/20';
      case 'high':
        return 'border-l-orange-500 bg-orange-50 dark:bg-orange-900/20';
      case 'medium':
        return 'border-l-blue-500 bg-blue-50 dark:bg-blue-900/20';
      case 'low':
        return 'border-l-gray-500 bg-gray-50 dark:bg-gray-900/20';
      default:
        return 'border-l-gray-500 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getFilteredNotifications = () => {
    if (activeFilter === 'all') return notifications;
    if (activeFilter === 'unread') return notifications.filter(n => n.status === 'unread');
    return notifications.filter(n => n.category === activeFilter);
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const filteredNotifications = getFilteredNotifications();
  const unreadCount = summary?.unread_count || 0;

  return (
    <div className={`relative ${className}`}>
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
      >
        <Bell className="w-6 h-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
        {isConnected ? (
          <span className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800" title="Connected"></span>
        ) : connectionError ? (
          <span className="absolute -bottom-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white dark:border-gray-800" title={`Connection error: ${connectionError}`}></span>
        ) : (
          <span className="absolute -bottom-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full border-2 border-white dark:border-gray-800" title="Connecting..."></span>
        )}
      </button>

      {/* Notification Panel */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Notifications
            </h3>
            <div className="flex items-center space-x-2">
              {unreadCount > 0 && (
                <button
                  onClick={handleMarkAllAsRead}
                  className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  Mark all read
                </button>
              )}
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-2 p-3 border-b border-gray-200 dark:border-gray-700 overflow-x-auto">
            {['all', 'unread', 'expense', 'group', 'settlement', 'approval'].map((filter) => (
              <button
                key={filter}
                onClick={() => setActiveFilter(filter)}
                className={`px-3 py-1 rounded-full text-sm whitespace-nowrap ${
                  activeFilter === filter
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                {filter.charAt(0).toUpperCase() + filter.slice(1)}
                {filter === 'unread' && unreadCount > 0 && (
                  <span className="ml-1 bg-red-500 text-white text-xs rounded-full px-1">
                    {unreadCount}
                  </span>
                )}
              </button>
            ))}
          </div>

          {/* Notifications List */}
          <div className="max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                Loading notifications...
              </div>
            ) : filteredNotifications.length === 0 ? (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                {activeFilter === 'unread' ? 'No unread notifications' : 'No notifications'}
              </div>
            ) : (
              filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  onClick={() => handleNotificationClick(notification)}
                  className={`p-4 border-l-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                    getPriorityColor(notification.priority)
                  } ${
                    notification.status === 'unread' ? 'font-medium' : 'opacity-75'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.category)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {notification.title}
                        </p>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                          {formatTimeAgo(notification.created_at)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        {notification.message}
                      </p>
                      {notification.action_text && (
                        <div className="flex items-center mt-2 text-xs text-blue-600 dark:text-blue-400">
                          <span>{notification.action_text}</span>
                          <ExternalLink className="w-3 h-3 ml-1" />
                        </div>
                      )}
                    </div>
                    {notification.status === 'unread' && (
                      <div className="flex-shrink-0">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleMarkAsRead(notification.id);
                          }}
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          <Check className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          {filteredNotifications.length > 0 && (
            <div className="p-3 border-t border-gray-200 dark:border-gray-700 text-center">
              <button
                onClick={() => {
                  navigate('/notifications');
                  setIsOpen(false);
                }}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                View all notifications
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default EnhancedNotificationPanel;
