#!/usr/bin/env python3
"""
<PERSON>ript to reset and migrate the database with all new schema changes
"""

import os
import sys
import sqlite3
from alembic.config import Config
from alembic import command

def backup_existing_data():
    """Backup existing user data if database exists"""
    db_path = "./dev.db"
    backup_data = []
    
    if os.path.exists(db_path):
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check if users table exists and has data
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users';")
            if cursor.fetchone():
                cursor.execute("SELECT id, email, hashed_password, groq_api_key FROM users;")
                backup_data = cursor.fetchall()
                print(f"📦 Backed up {len(backup_data)} users")
            
            conn.close()
        except Exception as e:
            print(f"⚠️ Could not backup data: {e}")
    
    return backup_data

def restore_user_data(backup_data):
    """Restore user data to new database schema"""
    if not backup_data:
        return
    
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        for user_data in backup_data:
            user_id, email, hashed_password, groq_api_key = user_data
            
            # Insert with default values for new columns
            cursor.execute("""
                INSERT INTO users (
                    id, email, hashed_password, groq_api_key,
                    timezone, currency, language,
                    email_notifications, expense_notifications, 
                    approval_notifications, settlement_notifications,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            """, (
                user_id, email, hashed_password, groq_api_key,
                'UTC', 'PKR', 'en',  # Default values
                True, True, True, True  # Default notification preferences
            ))
        
        conn.commit()
        conn.close()
        print(f"✅ Restored {len(backup_data)} users with new schema")
        
    except Exception as e:
        print(f"❌ Error restoring data: {e}")

def reset_database():
    """Remove existing database file"""
    db_path = "./dev.db"
    if os.path.exists(db_path):
        os.remove(db_path)
        print("🗑️ Removed existing database")
    else:
        print("ℹ️ No existing database found")

def run_migrations():
    """Run all database migrations"""
    try:
        alembic_cfg = Config("alembic.ini")
        
        print("🔄 Running database migrations...")
        
        # Upgrade to head (latest migration)
        command.upgrade(alembic_cfg, "head")
        
        print("✅ Database migrations completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error running migrations: {e}")
        return False

def verify_schema():
    """Verify that all required columns exist"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        # Get users table schema
        cursor.execute("PRAGMA table_info(users);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        required_columns = [
            'id', 'email', 'hashed_password', 'groq_api_key',
            'full_name', 'avatar_url', 'phone', 'timezone', 'currency', 'language',
            'email_notifications', 'expense_notifications', 'approval_notifications', 
            'settlement_notifications', 'created_at', 'updated_at'
        ]
        
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"❌ Missing columns: {', '.join(missing_columns)}")
            return False
        
        print("✅ All required columns exist in users table")
        
        # Print current schema
        print("\n📋 Current users table schema:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error verifying schema: {e}")
        return False

def create_test_user():
    """Create a test user to verify the schema works"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        # Check if test user already exists
        cursor.execute("SELECT id FROM users WHERE email = ?", ("<EMAIL>",))
        if cursor.fetchone():
            print("ℹ️ Test user already exists")
            conn.close()
            return True
        
        # Create test user with all fields
        cursor.execute("""
            INSERT INTO users (
                email, hashed_password, groq_api_key,
                full_name, timezone, currency, language,
                email_notifications, expense_notifications, 
                approval_notifications, settlement_notifications,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        """, (
            "<EMAIL>",
            "$2b$12$example_hashed_password",  # This would be a real hash
            "test_groq_api_key",
            "Test User",
            "UTC",
            "PKR",
            "en",
            True, True, True, True
        ))
        
        conn.commit()
        conn.close()
        
        print("✅ Created test user successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error creating test user: {e}")
        return False

def main():
    """Main function"""
    print("🔄 Database Reset and Migration")
    print("=" * 50)
    
    # Step 1: Backup existing data
    print("\n1. Backing up existing data...")
    backup_data = backup_existing_data()
    
    # Step 2: Reset database
    print("\n2. Resetting database...")
    reset_database()
    
    # Step 3: Run migrations
    print("\n3. Running migrations...")
    if not run_migrations():
        print("❌ Migration failed!")
        sys.exit(1)
    
    # Step 4: Restore data
    if backup_data:
        print("\n4. Restoring user data...")
        restore_user_data(backup_data)
    
    # Step 5: Verify schema
    print("\n5. Verifying schema...")
    if not verify_schema():
        print("❌ Schema verification failed!")
        sys.exit(1)
    
    # Step 6: Create test user
    print("\n6. Creating test user...")
    create_test_user()
    
    print("\n" + "=" * 50)
    print("🎉 Database reset and migration completed successfully!")
    print("\nNext steps:")
    print("1. Start the FastAPI server: uvicorn app.main:app --reload")
    print("2. Test login with: <EMAIL>")
    print("3. The database now includes all profile fields")

if __name__ == "__main__":
    main()
