{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Folio3\\\\expense-frontend\\\\src\\\\contexts\\\\WebSocketContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { useWebSocket } from '../hooks/useWebSocket';\nimport { useAuth } from './AuthContext';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WebSocketContext = /*#__PURE__*/createContext(null);\nexport const WebSocketProvider = ({\n  children\n}) => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [lastMessage, setLastMessage] = useState(null);\n  const [messageHistory, setMessageHistory] = useState([]);\n  const [hasShownConnectionError, setHasShownConnectionError] = useState(false);\n  const [hasShownDisconnectionError, setHasShownDisconnectionError] = useState(false);\n  const webSocketReturn = useWebSocket({\n    onMessage: message => {\n      console.log('WebSocket message received:', message);\n      setLastMessage(message);\n      setMessageHistory(prev => [message, ...prev.slice(0, 99)]); // Keep last 100 messages\n\n      // Handle different message types\n      switch (message.type) {\n        case 'expense_created':\n        case 'expense_approved':\n        case 'expense_rejected':\n        case 'expense_updated':\n        case 'expense_deleted':\n          // Show toast for expense-related notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '💰',\n              duration: 4000\n            });\n          }\n          break;\n        case 'member_joined':\n        case 'member_left':\n        case 'group_settings_changed':\n        case 'join_request_received':\n          // Show toast for group-related notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '👥',\n              duration: 4000\n            });\n          }\n          break;\n        case 'settlement_initiated':\n        case 'settlement_received':\n        case 'settlement_accepted':\n        case 'settlement_disputed':\n        case 'settlement_completed':\n          // Show toast for settlement-related notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '💸',\n              duration: 5000\n            });\n          }\n          break;\n        case 'expense_needs_approval':\n        case 'expense_approval_reminder':\n          // Show toast for approval-related notifications with higher priority\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '⏰',\n              duration: 6000\n            });\n          }\n          break;\n        case 'system_maintenance':\n        case 'system_update':\n          // Show toast for system notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '🔧',\n              duration: 8000\n            });\n          }\n          break;\n        default:\n          // Generic notification\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '📢',\n              duration: 4000\n            });\n          }\n          break;\n      }\n    },\n    onConnect: () => {\n      console.log('WebSocket connected successfully');\n      toast.success('Real-time notifications connected', {\n        duration: 2000\n      });\n    },\n    onDisconnect: () => {\n      console.log('WebSocket disconnected');\n      toast.error('Real-time notifications disconnected', {\n        duration: 3000\n      });\n    },\n    onError: error => {\n      console.error('WebSocket connection error:', error);\n      toast.error('Notification connection error', {\n        duration: 4000\n      });\n    },\n    autoReconnect: true,\n    maxReconnectAttempts: 5,\n    reconnectInterval: 2000\n  });\n  const clearMessageHistory = () => {\n    setMessageHistory([]);\n    setLastMessage(null);\n  };\n\n  // Only connect when user is authenticated\n  useEffect(() => {\n    if (user) {\n      webSocketReturn.connect();\n    } else {\n      webSocketReturn.disconnect();\n      clearMessageHistory();\n    }\n  }, [user]);\n  const contextValue = {\n    ...webSocketReturn,\n    lastMessage,\n    messageHistory,\n    clearMessageHistory\n  };\n  return /*#__PURE__*/_jsxDEV(WebSocketContext.Provider, {\n    value: contextValue,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 5\n  }, this);\n};\n_s(WebSocketProvider, \"dp0lpcvzC6U57g7uMPEZPDNBz8Q=\", false, function () {\n  return [useAuth, useWebSocket];\n});\n_c = WebSocketProvider;\nexport const useWebSocketContext = () => {\n  _s2();\n  const context = useContext(WebSocketContext);\n  if (!context) {\n    throw new Error('useWebSocketContext must be used within a WebSocketProvider');\n  }\n  return context;\n};\n_s2(useWebSocketContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default WebSocketContext;\nvar _c;\n$RefreshReg$(_c, \"WebSocketProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "useWebSocket", "useAuth", "toast", "jsxDEV", "_jsxDEV", "WebSocketContext", "WebSocketProvider", "children", "_s", "user", "lastMessage", "setLastMessage", "messageHistory", "setMessageHistory", "hasShownConnectionError", "setHasShownConnectionError", "hasShownDisconnectionError", "setHasShownDisconnectionError", "webSocketReturn", "onMessage", "message", "console", "log", "prev", "slice", "type", "title", "icon", "duration", "onConnect", "success", "onDisconnect", "error", "onError", "autoReconnect", "maxReconnectAttempts", "reconnectInterval", "clearMessageHistory", "connect", "disconnect", "contextValue", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useWebSocketContext", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/contexts/WebSocketContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport { useWebSocket, EnhancedWebSocketMessage, UseWebSocketReturn } from '../hooks/useWebSocket';\nimport { useAuth } from './AuthContext';\nimport toast from 'react-hot-toast';\n\ninterface WebSocketContextType extends UseWebSocketReturn {\n  lastMessage: EnhancedWebSocketMessage | null;\n  messageHistory: EnhancedWebSocketMessage[];\n  clearMessageHistory: () => void;\n}\n\nconst WebSocketContext = createContext<WebSocketContextType | null>(null);\n\ninterface WebSocketProviderProps {\n  children: ReactNode;\n}\n\nexport const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {\n  const { user } = useAuth();\n  const [lastMessage, setLastMessage] = useState<EnhancedWebSocketMessage | null>(null);\n  const [messageHistory, setMessageHistory] = useState<EnhancedWebSocketMessage[]>([]);\n  const [hasShownConnectionError, setHasShownConnectionError] = useState(false);\n  const [hasShownDisconnectionError, setHasShownDisconnectionError] = useState(false);\n\n  const webSocketReturn = useWebSocket({\n    onMessage: (message: EnhancedWebSocketMessage) => {\n      console.log('WebSocket message received:', message);\n      \n      setLastMessage(message);\n      setMessageHistory(prev => [message, ...prev.slice(0, 99)]); // Keep last 100 messages\n      \n      // Handle different message types\n      switch (message.type) {\n        case 'expense_created':\n        case 'expense_approved':\n        case 'expense_rejected':\n        case 'expense_updated':\n        case 'expense_deleted':\n          // Show toast for expense-related notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '💰',\n              duration: 4000,\n            });\n          }\n          break;\n          \n        case 'member_joined':\n        case 'member_left':\n        case 'group_settings_changed':\n        case 'join_request_received':\n          // Show toast for group-related notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '👥',\n              duration: 4000,\n            });\n          }\n          break;\n          \n        case 'settlement_initiated':\n        case 'settlement_received':\n        case 'settlement_accepted':\n        case 'settlement_disputed':\n        case 'settlement_completed':\n          // Show toast for settlement-related notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '💸',\n              duration: 5000,\n            });\n          }\n          break;\n          \n        case 'expense_needs_approval':\n        case 'expense_approval_reminder':\n          // Show toast for approval-related notifications with higher priority\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '⏰',\n              duration: 6000,\n            });\n          }\n          break;\n          \n        case 'system_maintenance':\n        case 'system_update':\n          // Show toast for system notifications\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '🔧',\n              duration: 8000,\n            });\n          }\n          break;\n          \n        default:\n          // Generic notification\n          if (message.title && message.message) {\n            toast(message.message, {\n              icon: '📢',\n              duration: 4000,\n            });\n          }\n          break;\n      }\n    },\n    onConnect: () => {\n      console.log('WebSocket connected successfully');\n      toast.success('Real-time notifications connected', {\n        duration: 2000,\n      });\n    },\n    onDisconnect: () => {\n      console.log('WebSocket disconnected');\n      toast.error('Real-time notifications disconnected', {\n        duration: 3000,\n      });\n    },\n    onError: (error) => {\n      console.error('WebSocket connection error:', error);\n      toast.error('Notification connection error', {\n        duration: 4000,\n      });\n    },\n    autoReconnect: true,\n    maxReconnectAttempts: 5,\n    reconnectInterval: 2000,\n  });\n\n  const clearMessageHistory = () => {\n    setMessageHistory([]);\n    setLastMessage(null);\n  };\n\n  // Only connect when user is authenticated\n  useEffect(() => {\n    if (user) {\n      webSocketReturn.connect();\n    } else {\n      webSocketReturn.disconnect();\n      clearMessageHistory();\n    }\n  }, [user]);\n\n  const contextValue: WebSocketContextType = {\n    ...webSocketReturn,\n    lastMessage,\n    messageHistory,\n    clearMessageHistory,\n  };\n\n  return (\n    <WebSocketContext.Provider value={contextValue}>\n      {children}\n    </WebSocketContext.Provider>\n  );\n};\n\nexport const useWebSocketContext = (): WebSocketContextType => {\n  const context = useContext(WebSocketContext);\n  if (!context) {\n    throw new Error('useWebSocketContext must be used within a WebSocketProvider');\n  }\n  return context;\n};\n\nexport default WebSocketContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAmB,OAAO;AACxF,SAASC,YAAY,QAAsD,uBAAuB;AAClG,SAASC,OAAO,QAAQ,eAAe;AACvC,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQpC,MAAMC,gBAAgB,gBAAGT,aAAa,CAA8B,IAAI,CAAC;AAMzE,OAAO,MAAMU,iBAAmD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACnF,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAkC,IAAI,CAAC;EACrF,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAA6B,EAAE,CAAC;EACpF,MAAM,CAACe,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACiB,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEnF,MAAMmB,eAAe,GAAGlB,YAAY,CAAC;IACnCmB,SAAS,EAAGC,OAAiC,IAAK;MAChDC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,OAAO,CAAC;MAEnDT,cAAc,CAACS,OAAO,CAAC;MACvBP,iBAAiB,CAACU,IAAI,IAAI,CAACH,OAAO,EAAE,GAAGG,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5D;MACA,QAAQJ,OAAO,CAACK,IAAI;QAClB,KAAK,iBAAiB;QACtB,KAAK,kBAAkB;QACvB,KAAK,kBAAkB;QACvB,KAAK,iBAAiB;QACtB,KAAK,iBAAiB;UACpB;UACA,IAAIL,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACA,OAAO,EAAE;YACpClB,KAAK,CAACkB,OAAO,CAACA,OAAO,EAAE;cACrBO,IAAI,EAAE,IAAI;cACVC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UACA;QAEF,KAAK,eAAe;QACpB,KAAK,aAAa;QAClB,KAAK,wBAAwB;QAC7B,KAAK,uBAAuB;UAC1B;UACA,IAAIR,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACA,OAAO,EAAE;YACpClB,KAAK,CAACkB,OAAO,CAACA,OAAO,EAAE;cACrBO,IAAI,EAAE,IAAI;cACVC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UACA;QAEF,KAAK,sBAAsB;QAC3B,KAAK,qBAAqB;QAC1B,KAAK,qBAAqB;QAC1B,KAAK,qBAAqB;QAC1B,KAAK,sBAAsB;UACzB;UACA,IAAIR,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACA,OAAO,EAAE;YACpClB,KAAK,CAACkB,OAAO,CAACA,OAAO,EAAE;cACrBO,IAAI,EAAE,IAAI;cACVC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UACA;QAEF,KAAK,wBAAwB;QAC7B,KAAK,2BAA2B;UAC9B;UACA,IAAIR,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACA,OAAO,EAAE;YACpClB,KAAK,CAACkB,OAAO,CAACA,OAAO,EAAE;cACrBO,IAAI,EAAE,GAAG;cACTC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UACA;QAEF,KAAK,oBAAoB;QACzB,KAAK,eAAe;UAClB;UACA,IAAIR,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACA,OAAO,EAAE;YACpClB,KAAK,CAACkB,OAAO,CAACA,OAAO,EAAE;cACrBO,IAAI,EAAE,IAAI;cACVC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UACA;QAEF;UACE;UACA,IAAIR,OAAO,CAACM,KAAK,IAAIN,OAAO,CAACA,OAAO,EAAE;YACpClB,KAAK,CAACkB,OAAO,CAACA,OAAO,EAAE;cACrBO,IAAI,EAAE,IAAI;cACVC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;UACA;MACJ;IACF,CAAC;IACDC,SAAS,EAAEA,CAAA,KAAM;MACfR,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/CpB,KAAK,CAAC4B,OAAO,CAAC,mCAAmC,EAAE;QACjDF,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;IACDG,YAAY,EAAEA,CAAA,KAAM;MAClBV,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrCpB,KAAK,CAAC8B,KAAK,CAAC,sCAAsC,EAAE;QAClDJ,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;IACDK,OAAO,EAAGD,KAAK,IAAK;MAClBX,OAAO,CAACW,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD9B,KAAK,CAAC8B,KAAK,CAAC,+BAA+B,EAAE;QAC3CJ,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;IACDM,aAAa,EAAE,IAAI;IACnBC,oBAAoB,EAAE,CAAC;IACvBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EAEF,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCxB,iBAAiB,CAAC,EAAE,CAAC;IACrBF,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACAb,SAAS,CAAC,MAAM;IACd,IAAIW,IAAI,EAAE;MACRS,eAAe,CAACoB,OAAO,CAAC,CAAC;IAC3B,CAAC,MAAM;MACLpB,eAAe,CAACqB,UAAU,CAAC,CAAC;MAC5BF,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAAC5B,IAAI,CAAC,CAAC;EAEV,MAAM+B,YAAkC,GAAG;IACzC,GAAGtB,eAAe;IAClBR,WAAW;IACXE,cAAc;IACdyB;EACF,CAAC;EAED,oBACEjC,OAAA,CAACC,gBAAgB,CAACoC,QAAQ;IAACC,KAAK,EAAEF,YAAa;IAAAjC,QAAA,EAC5CA;EAAQ;IAAAoC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACgB,CAAC;AAEhC,CAAC;AAACtC,EAAA,CA5IWF,iBAAmD;EAAA,QAC7CL,OAAO,EAMAD,YAAY;AAAA;AAAA+C,EAAA,GAPzBzC,iBAAmD;AA8IhE,OAAO,MAAM0C,mBAAmB,GAAGA,CAAA,KAA4B;EAAAC,GAAA;EAC7D,MAAMC,OAAO,GAAGrD,UAAU,CAACQ,gBAAgB,CAAC;EAC5C,IAAI,CAAC6C,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6DAA6D,CAAC;EAChF;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,mBAAmB;AAQhC,eAAe3C,gBAAgB;AAAC,IAAA0C,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}