from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import StaticPool
from .config import settings
import logging

logger = logging.getLogger(__name__)

# Configure database engine with proper connection pooling
if settings.DATABASE_URL.startswith("sqlite"):
    # SQLite configuration with proper pooling
    engine = create_engine(
        settings.DATABASE_URL,
        connect_args={
            "check_same_thread": False,
            "timeout": 20  # 20 second timeout for SQLite
        },
        poolclass=StaticPool,
        pool_pre_ping=True,  # Verify connections before use
        pool_recycle=300,    # Recycle connections every 5 minutes
        echo=False  # Set to True for SQL debugging
    )
    logger.info("Database engine configured for SQLite")
else:
    # PostgreSQL/other database configuration
    engine = create_engine(
        settings.DATABASE_URL,
        pool_size=10,        # Increase pool size
        max_overflow=20,     # Allow more overflow connections
        pool_timeout=30,     # Connection timeout
        pool_pre_ping=True,  # Verify connections before use
        pool_recycle=3600,   # Recycle connections every hour
        echo=False
    )
    logger.info("Database engine configured for PostgreSQL/other")

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    """
    Database session dependency with proper error handling and connection management
    """
    db = SessionLocal()
    try:
        # Test the connection
        db.execute("SELECT 1")
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def get_db_health():
    """
    Check database health and connection status
    """
    try:
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        return {"status": "healthy", "database": "connected"}
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {"status": "unhealthy", "error": str(e)}

def close_db_connections():
    """
    Close all database connections (useful for cleanup)
    """
    try:
        engine.dispose()
        logger.info("Database connections closed")
    except Exception as e:
        logger.error(f"Error closing database connections: {e}")
