#!/usr/bin/env python3
"""
Manually create notification tables and initialize templates
"""

import sqlite3
import os
import json
from datetime import datetime

def create_notification_tables():
    """Manually create notification tables"""
    db_path = "./dev.db"
    
    if not os.path.exists(db_path):
        print("❌ Database file 'dev.db' does not exist!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 Creating notification tables...")
        
        # Create notifications table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY,
                user_id INTEGER NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                category TEXT NOT NULL,
                priority TEXT DEFAULT 'medium',
                status TEXT DEFAULT 'unread',
                action_url TEXT,
                action_text TEXT,
                expense_id INTEGER,
                group_id INTEGER,
                settlement_id INTEGER,
                data TEXT,
                expires_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                read_at DATETIME,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (expense_id) REFERENCES expenses (id),
                FOREIGN KEY (group_id) REFERENCES groups (id)
            )
        """)
        print("✅ Created notifications table")
        
        # Create notification_templates table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS notification_templates (
                id INTEGER PRIMARY KEY,
                template_key TEXT UNIQUE NOT NULL,
                category TEXT NOT NULL,
                title_template TEXT NOT NULL,
                message_template TEXT NOT NULL,
                action_text_template TEXT,
                action_url_template TEXT,
                priority TEXT DEFAULT 'medium',
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ Created notification_templates table")
        
        # Create user_notification_preferences table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_notification_preferences (
                id INTEGER PRIMARY KEY,
                user_id INTEGER NOT NULL,
                template_key TEXT NOT NULL,
                enabled BOOLEAN DEFAULT 1,
                email_enabled BOOLEAN DEFAULT 0,
                push_enabled BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                UNIQUE(user_id, template_key)
            )
        """)
        print("✅ Created user_notification_preferences table")
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS ix_notifications_user_status ON notifications(user_id, status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS ix_notifications_created_at ON notifications(created_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS ix_user_notification_preferences_user_template ON user_notification_preferences(user_id, template_key)")
        print("✅ Created indexes")
        
        conn.commit()
        conn.close()
        
        print("✅ All notification tables created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        return False

def initialize_notification_templates():
    """Initialize notification templates"""
    db_path = "./dev.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n📝 Initializing notification templates...")
        
        templates = [
            # Expense Management Templates
            ('expense_created', 'expense', 'New Expense Added', 
             '{creator_name} added "{expense_description}" for PKR {expense_total:.2f} in {group_name}',
             'Review Expense', '/approvals', 'medium'),
            
            ('expense_approved', 'expense', 'Expense Approved',
             '{approver_name} approved "{expense_description}" in {group_name}',
             'View Expenses', '/expenses/history', 'medium'),
            
            ('expense_rejected', 'expense', 'Expense Rejected',
             '{approver_name} rejected "{expense_description}" in {group_name}',
             'View Details', '/approvals', 'high'),
            
            ('expense_updated', 'expense', 'Expense Updated',
             '{updater_name} updated "{expense_description}" in {group_name}',
             'View Changes', '/expenses/history', 'medium'),
            
            ('expense_deleted', 'expense', 'Expense Deleted',
             '{deleter_name} deleted "{expense_description}" from {group_name}',
             'View Expenses', '/expenses/history', 'high'),
            
            ('expense_needs_approval', 'approval', 'Approval Required',
             '{creator_name} submitted "{expense_description}" for approval in {group_name}',
             'Review & Approve', '/approvals', 'high'),
            
            # Group Management Templates
            ('member_joined', 'group', 'New Member Joined',
             '{member_name} joined {group_name}',
             'View Group', '/group-management/groups/{group_id}/details', 'medium'),
            
            ('member_left', 'group', 'Member Left Group',
             '{member_name} left {group_name}',
             'View Group', '/group-management/groups/{group_id}/details', 'medium'),
            
            ('group_settings_changed', 'group', 'Group Settings Updated',
             '{changer_name} updated settings for {group_name}',
             'View Changes', '/group-management/groups/{group_id}/details', 'medium'),
            
            ('join_request_received', 'group', 'Join Request Received',
             '{requester_name} wants to join {group_name}',
             'Review Request', '/group-management/groups/{group_id}/details', 'high'),
            
            ('join_request_approved', 'group', 'Join Request Approved',
             '{processor_name} approved your request to join "{group_name}"',
             'View Groups', '/group-management', 'medium'),
            
            ('join_request_rejected', 'group', 'Join Request Rejected',
             '{processor_name} rejected your request to join "{group_name}"',
             None, None, 'high'),
            
            # Settlement Templates
            ('settlement_initiated', 'settlement', 'Settlement Initiated',
             '{sender_name} initiated a settlement of PKR {amount:.2f}',
             'View Settlements', '/settlements', 'medium'),
            
            ('settlement_received', 'settlement', 'Settlement Received',
             '{sender_name} sent you PKR {amount:.2f}',
             'Review Settlement', '/settlements', 'high'),
            
            ('settlement_accepted', 'settlement', 'Settlement Accepted',
             '{accepter_name} accepted your settlement of PKR {amount:.2f}',
             'View Details', '/settlements', 'medium'),
            
            ('settlement_disputed', 'settlement', 'Settlement Disputed',
             '{disputer_name} disputed a settlement of PKR {amount:.2f}',
             'Resolve Dispute', '/settlements', 'urgent'),
            
            ('settlement_completed', 'settlement', 'Settlement Completed',
             'Settlement of PKR {amount:.2f} between {sender_name} and {accepter_name} completed',
             'View Settlements', '/settlements', 'medium'),
        ]
        
        inserted_count = 0
        for template_data in templates:
            template_key, category, title, message, action_text, action_url, priority = template_data
            
            # Check if template already exists
            cursor.execute("SELECT id FROM notification_templates WHERE template_key = ?", (template_key,))
            if cursor.fetchone():
                continue
            
            cursor.execute("""
                INSERT INTO notification_templates 
                (template_key, category, title_template, message_template, action_text_template, action_url_template, priority)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (template_key, category, title, message, action_text, action_url, priority))
            inserted_count += 1
        
        conn.commit()
        conn.close()
        
        print(f"✅ Initialized {inserted_count} notification templates")
        return True
        
    except Exception as e:
        print(f"❌ Error initializing templates: {e}")
        return False

def verify_setup():
    """Verify the notification system setup"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        print("\n🔍 Verifying notification system setup...")
        
        # Check tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%notification%'")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        
        expected_tables = ['notifications', 'notification_templates', 'user_notification_preferences']
        
        for table in expected_tables:
            if table in table_names:
                print(f"  ✅ {table} table exists")
                
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"     Records: {count}")
            else:
                print(f"  ❌ {table} table missing")
        
        # Show template categories
        cursor.execute("SELECT DISTINCT category FROM notification_templates")
        categories = cursor.fetchall()
        if categories:
            print(f"  📂 Template categories: {', '.join([cat[0] for cat in categories])}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error verifying setup: {e}")
        return False

def create_test_notification():
    """Create a test notification"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        print("\n🧪 Creating test notification...")
        
        # Get first user
        cursor.execute("SELECT id, email FROM users LIMIT 1")
        user = cursor.fetchone()
        
        if not user:
            print("⚠️ No users found for testing")
            return True
        
        user_id, email = user
        
        # Create test notification
        cursor.execute("""
            INSERT INTO notifications 
            (user_id, title, message, category, priority, action_url, action_text)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            user_id,
            "Enhanced Notification System Active",
            "The enhanced notification system has been successfully set up and is now active!",
            "system",
            "medium",
            "/notifications",
            "View Notifications"
        ))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Test notification created for user: {email}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating test notification: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Manual Enhanced Notification System Setup")
    print("=" * 60)
    
    # Step 1: Create tables
    if not create_notification_tables():
        print("❌ Table creation failed!")
        return
    
    # Step 2: Initialize templates
    if not initialize_notification_templates():
        print("❌ Template initialization failed!")
        return
    
    # Step 3: Verify setup
    if not verify_setup():
        print("❌ Setup verification failed!")
        return
    
    # Step 4: Create test notification
    if not create_test_notification():
        print("❌ Test notification creation failed!")
        return
    
    print("\n" + "=" * 60)
    print("🎉 Enhanced Notification System Setup Complete!")
    print("✅ Database tables created successfully")
    print("✅ Notification templates initialized")
    print("✅ Test notification created")
    print("\nThe enhanced notification system is now ready!")
    print("\nFeatures available:")
    print("  • Multi-user real-time notifications")
    print("  • Persistent notification storage")
    print("  • Deep-linking with action URLs")
    print("  • User notification preferences")
    print("  • Comprehensive notification categories")
    print("\nYou can now start the FastAPI server and test the notification system.")

if __name__ == "__main__":
    main()
