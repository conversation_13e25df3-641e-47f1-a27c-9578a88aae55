#!/usr/bin/env python3
"""
Comprehensive system health diagnosis script for the expense tracking application
"""

import requests
import asyncio
import websockets
import json
import sys
import os
import sqlite3
import time
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:8000"
WS_BASE_URL = "ws://localhost:8000"
FRONTEND_URL = "http://localhost:3000"

class SystemHealthDiagnostic:
    def __init__(self):
        self.issues_found = []
        self.warnings = []
        self.successes = []

    def log_issue(self, message):
        self.issues_found.append(message)
        print(f"❌ {message}")

    def log_warning(self, message):
        self.warnings.append(message)
        print(f"⚠️ {message}")

    def log_success(self, message):
        self.successes.append(message)
        print(f"✅ {message}")

    def check_database_health(self):
        """Check SQLite database health and connection pool"""
        print("\n🔍 Checking Database Health...")
        
        try:
            # Check if database file exists
            db_path = "./expense-app/dev.db"
            if not os.path.exists(db_path):
                self.log_issue(f"Database file not found: {db_path}")
                return False
            
            # Check database connectivity
            conn = sqlite3.connect(db_path, timeout=5)
            cursor = conn.cursor()
            
            # Test basic query
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result[0] == 1:
                self.log_success("Database connectivity test passed")
            
            # Check if tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            table_names = [table[0] for table in tables]
            
            expected_tables = ['users', 'groups', 'expenses', 'shares', 'notifications']
            missing_tables = [table for table in expected_tables if table not in table_names]
            
            if missing_tables:
                self.log_warning(f"Missing tables: {missing_tables}")
            else:
                self.log_success("All essential tables present")
            
            # Check for connection pool issues (simulate multiple connections)
            connections = []
            try:
                for i in range(15):  # Try to create more connections than typical pool size
                    conn = sqlite3.connect(db_path, timeout=1)
                    connections.append(conn)
                    conn.execute("SELECT 1")
                
                self.log_success("Database can handle multiple connections")
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e):
                    self.log_issue("Database is locked - possible connection pool exhaustion")
                else:
                    self.log_warning(f"Database connection issue: {e}")
            finally:
                for conn in connections:
                    conn.close()
            
            conn.close()
            return True
            
        except Exception as e:
            self.log_issue(f"Database health check failed: {e}")
            return False

    def check_backend_health(self):
        """Check FastAPI backend health"""
        print("\n🔍 Checking Backend Health...")
        
        try:
            # Check main API endpoint
            response = requests.get(f"{API_BASE_URL}/", timeout=10)
            if response.status_code == 200:
                self.log_success("FastAPI backend is running")
            else:
                self.log_issue(f"Backend returned status {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            self.log_issue("FastAPI backend is not running or not accessible")
            return False
        except Exception as e:
            self.log_issue(f"Backend health check failed: {e}")
            return False
        
        # Check health endpoint
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                if health_data.get("status") == "healthy":
                    self.log_success("Backend health endpoint reports healthy")
                else:
                    self.log_warning(f"Backend health status: {health_data.get('status')}")
                    if health_data.get("database", {}).get("status") != "healthy":
                        self.log_issue("Database health check failed in backend")
            else:
                self.log_warning(f"Health endpoint returned status {response.status_code}")
        except Exception as e:
            self.log_warning(f"Health endpoint check failed: {e}")
        
        # Check WebSocket endpoints
        try:
            response = requests.get(f"{API_BASE_URL}/ws/health", timeout=10)
            if response.status_code == 200:
                ws_health = response.json()
                self.log_success(f"WebSocket service healthy - {ws_health.get('connected_users', 0)} users connected")
            else:
                self.log_warning(f"WebSocket health endpoint returned status {response.status_code}")
        except Exception as e:
            self.log_warning(f"WebSocket health check failed: {e}")
        
        return True

    async def check_websocket_connection(self):
        """Test WebSocket connection"""
        print("\n🔍 Checking WebSocket Connection...")
        
        try:
            # Test connection without token (should fail gracefully)
            ws_url = f"{WS_BASE_URL}/ws/notifications"
            
            try:
                async with websockets.connect(ws_url, timeout=5) as websocket:
                    self.log_warning("WebSocket connected without token (security issue)")
            except websockets.exceptions.ConnectionClosedError as e:
                if e.code == 1008:  # Policy violation
                    self.log_success("WebSocket correctly rejects unauthenticated connections")
                else:
                    self.log_warning(f"WebSocket closed with code {e.code}: {e.reason}")
            except Exception as e:
                if "403" in str(e) or "401" in str(e):
                    self.log_success("WebSocket correctly requires authentication")
                else:
                    self.log_issue(f"WebSocket connection test failed: {e}")
                    return False
            
            return True
            
        except Exception as e:
            self.log_issue(f"WebSocket test failed: {e}")
            return False

    def check_frontend_health(self):
        """Check React frontend health"""
        print("\n🔍 Checking Frontend Health...")
        
        try:
            response = requests.get(FRONTEND_URL, timeout=10)
            if response.status_code == 200:
                self.log_success("React frontend is accessible")
                
                # Check if it's actually the React app
                if "react" in response.text.lower() or "expense" in response.text.lower():
                    self.log_success("Frontend appears to be the expense tracking app")
                else:
                    self.log_warning("Frontend may not be the correct application")
                
                return True
            else:
                self.log_issue(f"Frontend returned status {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            self.log_issue("React frontend is not running or not accessible")
            return False
        except Exception as e:
            self.log_issue(f"Frontend health check failed: {e}")
            return False

    def check_api_endpoints(self):
        """Test critical API endpoints"""
        print("\n🔍 Checking Critical API Endpoints...")
        
        endpoints_to_test = [
            ("/auth/login", "POST", {"email": "test", "password": "test"}),
            ("/groups/", "GET", None),
            ("/expenses/balances", "GET", None),
            ("/notifications/", "GET", None),
        ]
        
        for endpoint, method, data in endpoints_to_test:
            try:
                url = f"{API_BASE_URL}{endpoint}"
                
                if method == "GET":
                    response = requests.get(url, timeout=5)
                elif method == "POST":
                    response = requests.post(url, json=data, timeout=5)
                
                # We expect 401/422 for unauthenticated requests, not 500
                if response.status_code in [200, 401, 422]:
                    self.log_success(f"{method} {endpoint} - Endpoint accessible")
                elif response.status_code == 500:
                    self.log_issue(f"{method} {endpoint} - Internal server error (500)")
                else:
                    self.log_warning(f"{method} {endpoint} - Status {response.status_code}")
                    
            except Exception as e:
                self.log_issue(f"{method} {endpoint} - Request failed: {e}")

    def check_system_resources(self):
        """Check system resources and configuration"""
        print("\n🔍 Checking System Resources...")
        
        # Check if ports are in use
        import socket
        
        ports_to_check = [8000, 3000]
        for port in ports_to_check:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                self.log_success(f"Port {port} is in use (service running)")
            else:
                self.log_warning(f"Port {port} is not in use (service may not be running)")

    def generate_recommendations(self):
        """Generate recommendations based on findings"""
        print("\n📋 Diagnosis Summary")
        print("=" * 60)
        
        print(f"✅ Successes: {len(self.successes)}")
        print(f"⚠️ Warnings: {len(self.warnings)}")
        print(f"❌ Issues: {len(self.issues_found)}")
        
        if self.issues_found:
            print("\n🔧 Critical Issues to Fix:")
            for i, issue in enumerate(self.issues_found, 1):
                print(f"   {i}. {issue}")
        
        if self.warnings:
            print("\n⚠️ Warnings to Address:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"   {i}. {warning}")
        
        print("\n💡 Recommendations:")
        
        if any("Database" in issue for issue in self.issues_found):
            print("   🗄️ Database Issues:")
            print("      - Check if SQLite database file exists and is accessible")
            print("      - Restart the FastAPI server to reset connection pool")
            print("      - Check for long-running transactions that might lock the database")
        
        if any("Backend" in issue for issue in self.issues_found):
            print("   🖥️ Backend Issues:")
            print("      - Start the FastAPI server: cd expense-app && uvicorn app.main:app --reload")
            print("      - Check server logs for detailed error messages")
            print("      - Verify database configuration in .env file")
        
        if any("WebSocket" in issue for issue in self.issues_found):
            print("   🔌 WebSocket Issues:")
            print("      - Ensure FastAPI server is running with WebSocket support")
            print("      - Check if authentication is working properly")
            print("      - Verify WebSocket endpoint is accessible")
        
        if any("Frontend" in issue for issue in self.issues_found):
            print("   🌐 Frontend Issues:")
            print("      - Start the React frontend: cd expense-frontend && npm start")
            print("      - Check for compilation errors in the frontend")
            print("      - Verify API endpoints are accessible from frontend")
        
        print("\n🚀 Quick Fix Commands:")
        print("   # Start backend")
        print("   cd expense-app && uvicorn app.main:app --reload")
        print("   ")
        print("   # Start frontend")
        print("   cd expense-frontend && npm start")
        print("   ")
        print("   # Check logs")
        print("   # Backend logs will show in the uvicorn terminal")
        print("   # Frontend logs will show in the npm start terminal")

async def main():
    """Main diagnostic function"""
    print("🏥 System Health Diagnostic")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    diagnostic = SystemHealthDiagnostic()
    
    # Run all health checks
    diagnostic.check_database_health()
    diagnostic.check_backend_health()
    await diagnostic.check_websocket_connection()
    diagnostic.check_frontend_health()
    diagnostic.check_api_endpoints()
    diagnostic.check_system_resources()
    
    # Generate recommendations
    diagnostic.generate_recommendations()
    
    # Return exit code based on issues found
    if diagnostic.issues_found:
        print(f"\n❌ Diagnostic completed with {len(diagnostic.issues_found)} critical issues")
        sys.exit(1)
    elif diagnostic.warnings:
        print(f"\n⚠️ Diagnostic completed with {len(diagnostic.warnings)} warnings")
        sys.exit(0)
    else:
        print("\n🎉 All systems healthy!")
        sys.exit(0)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ Diagnostic interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Diagnostic failed: {e}")
        sys.exit(1)
