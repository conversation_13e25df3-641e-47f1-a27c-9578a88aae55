{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect, useRef, useState, useCallback } from 'react';\n\n// WebSocket message types based on backend NotificationTypes\n\n// Enhanced notification message structure\n\n// WebSocket hook configuration\n\n// WebSocket hook return type\n\n// Custom hook for WebSocket connection\nexport const useWebSocket = (config = {}) => {\n  _s();\n  const {\n    onMessage,\n    onConnect,\n    onDisconnect,\n    onError,\n    autoReconnect = true,\n    maxReconnectAttempts = 5,\n    reconnectInterval = 1000\n  } = config;\n  const [isConnected, setIsConnected] = useState(false);\n  const [isConnecting, setIsConnecting] = useState(false);\n  const [connectionError, setConnectionError] = useState(null);\n  const [reconnectAttempts, setReconnectAttempts] = useState(0);\n  const websocketRef = useRef(null);\n  const reconnectTimeoutRef = useRef(null);\n  const shouldReconnectRef = useRef(true);\n  const connectionDebounceRef = useRef(null);\n  const lastConnectionAttemptRef = useRef(0);\n\n  // Get authentication token from localStorage\n  const getAuthToken = useCallback(() => {\n    return localStorage.getItem('token') || '';\n  }, []);\n\n  // Create WebSocket URL\n  const getWebSocketUrl = useCallback(() => {\n    const token = getAuthToken();\n    if (!token) {\n      throw new Error('No authentication token available');\n    }\n    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n    const wsHost = process.env.REACT_APP_WS_URL || 'localhost:8000';\n    const wsUrl = `${wsProtocol}//${wsHost}/ws/notifications?token=${encodeURIComponent(token)}`;\n    console.log('WebSocket URL:', wsUrl.replace(/token=[^&]+/, 'token=***'));\n    return wsUrl;\n  }, [getAuthToken]);\n\n  // Send message through WebSocket\n  const sendMessage = useCallback(message => {\n    if (websocketRef.current && websocketRef.current.readyState === WebSocket.OPEN) {\n      try {\n        websocketRef.current.send(JSON.stringify(message));\n      } catch (error) {\n        console.error('Failed to send WebSocket message:', error);\n      }\n    } else {\n      console.warn('WebSocket is not connected. Cannot send message:', message);\n    }\n  }, []);\n\n  // Handle WebSocket message\n  const handleMessage = useCallback(event => {\n    try {\n      const message = JSON.parse(event.data);\n\n      // Handle system messages\n      switch (message.type) {\n        case 'connection_established':\n          console.log('WebSocket connection established');\n          setConnectionError(null);\n          setReconnectAttempts(0);\n          break;\n        case 'heartbeat':\n          // Respond to heartbeat\n          sendMessage({\n            type: 'ping'\n          });\n          break;\n        case 'error':\n          console.error('WebSocket error message:', message);\n          setConnectionError(message.message || 'WebSocket error');\n          break;\n        default:\n          // Pass message to callback\n          if (onMessage) {\n            onMessage(message);\n          }\n          break;\n      }\n    } catch (error) {\n      console.error('Failed to parse WebSocket message:', error);\n      setConnectionError('Failed to parse message');\n    }\n  }, [onMessage, sendMessage]);\n\n  // Connect to WebSocket\n  const connect = useCallback(() => {\n    var _websocketRef$current, _websocketRef$current2;\n    // Prevent multiple simultaneous connections\n    if (((_websocketRef$current = websocketRef.current) === null || _websocketRef$current === void 0 ? void 0 : _websocketRef$current.readyState) === WebSocket.OPEN) {\n      console.log('WebSocket already connected, skipping connection attempt');\n      return; // Already connected\n    }\n    if (((_websocketRef$current2 = websocketRef.current) === null || _websocketRef$current2 === void 0 ? void 0 : _websocketRef$current2.readyState) === WebSocket.CONNECTING || isConnecting) {\n      console.log('WebSocket connection already in progress, skipping duplicate attempt');\n      return; // Connection in progress\n    }\n\n    // Clean up any existing connection in a bad state\n    if (websocketRef.current && websocketRef.current.readyState !== WebSocket.CLOSED) {\n      console.log('Cleaning up existing WebSocket connection');\n      websocketRef.current.close();\n      websocketRef.current = null;\n    }\n    const token = getAuthToken();\n    if (!token) {\n      console.warn('Cannot connect WebSocket: No authentication token available');\n      setConnectionError('No authentication token available');\n      return;\n    }\n    console.log('Initiating WebSocket connection...');\n    setIsConnecting(true);\n    setConnectionError(null);\n    try {\n      const wsUrl = getWebSocketUrl();\n      console.log('Creating WebSocket connection to:', wsUrl.replace(/token=[^&]+/, 'token=***'));\n      const ws = new WebSocket(wsUrl);\n      websocketRef.current = ws;\n      ws.onopen = () => {\n        console.log('WebSocket connected successfully');\n        setIsConnected(true);\n        setIsConnecting(false);\n        setConnectionError(null);\n        setReconnectAttempts(0);\n        if (onConnect) {\n          onConnect();\n        }\n      };\n      ws.onmessage = handleMessage;\n      ws.onclose = event => {\n        console.log('WebSocket disconnected:', event.code, event.reason);\n        setIsConnected(false);\n        setIsConnecting(false);\n        websocketRef.current = null;\n        if (onDisconnect) {\n          onDisconnect();\n        }\n\n        // Attempt to reconnect if not a normal closure and auto-reconnect is enabled\n        if (autoReconnect && shouldReconnectRef.current && event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {\n          const delay = Math.min(reconnectInterval * Math.pow(2, reconnectAttempts), 30000); // Cap at 30 seconds\n          console.log(`Attempting to reconnect in ${delay}ms (attempt ${reconnectAttempts + 1}/${maxReconnectAttempts})`);\n          setReconnectAttempts(prev => prev + 1);\n          reconnectTimeoutRef.current = setTimeout(() => {\n            if (shouldReconnectRef.current) {\n              // Check if we still have a valid token before reconnecting\n              const token = localStorage.getItem('token');\n              if (token) {\n                connect();\n              } else {\n                console.log('No token available, stopping reconnection attempts');\n                setConnectionError('Authentication token not available');\n                shouldReconnectRef.current = false;\n              }\n            }\n          }, delay);\n        } else if (reconnectAttempts >= maxReconnectAttempts) {\n          console.log('Maximum reconnection attempts reached, stopping reconnection');\n          setConnectionError('Maximum reconnection attempts reached');\n          shouldReconnectRef.current = false;\n        }\n      };\n      ws.onerror = error => {\n        console.error('WebSocket error:', error);\n        setConnectionError('WebSocket connection error');\n        setIsConnecting(false);\n        if (onError) {\n          onError(error);\n        }\n      };\n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error);\n      setConnectionError('Failed to create WebSocket connection');\n      setIsConnecting(false);\n    }\n  }, [isConnecting, getAuthToken, getWebSocketUrl, handleMessage, onConnect, onDisconnect, onError, autoReconnect, maxReconnectAttempts, reconnectInterval, reconnectAttempts]);\n\n  // Disconnect from WebSocket\n  const disconnect = useCallback(() => {\n    shouldReconnectRef.current = false;\n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n      reconnectTimeoutRef.current = null;\n    }\n    if (websocketRef.current) {\n      websocketRef.current.close(1000, 'User disconnected');\n      websocketRef.current = null;\n    }\n    setIsConnected(false);\n    setIsConnecting(false);\n    setConnectionError(null);\n    setReconnectAttempts(0);\n  }, []);\n\n  // Auto-connect on mount and when token changes\n  useEffect(() => {\n    const token = getAuthToken();\n    if (token && shouldReconnectRef.current) {\n      connect();\n    }\n    return () => {\n      shouldReconnectRef.current = false;\n      disconnect();\n    };\n  }, [connect, disconnect, getAuthToken]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      shouldReconnectRef.current = false;\n      if (reconnectTimeoutRef.current) {\n        clearTimeout(reconnectTimeoutRef.current);\n      }\n      if (websocketRef.current) {\n        websocketRef.current.close(1000, 'Component unmounted');\n      }\n    };\n  }, []);\n  return {\n    isConnected,\n    isConnecting,\n    connectionError,\n    sendMessage,\n    connect,\n    disconnect,\n    reconnectAttempts\n  };\n};\n_s(useWebSocket, \"187FOXDjJiV4RlYO4w0idogewhg=\");\nexport default useWebSocket;", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "useCallback", "useWebSocket", "config", "_s", "onMessage", "onConnect", "onDisconnect", "onError", "autoReconnect", "maxReconnectAttempts", "reconnectInterval", "isConnected", "setIsConnected", "isConnecting", "setIsConnecting", "connectionError", "setConnectionError", "reconnectAttempts", "setReconnectAttempts", "websocketRef", "reconnectTimeoutRef", "shouldReconnectRef", "connectionDebounceRef", "lastConnectionAttemptRef", "getAuthToken", "localStorage", "getItem", "getWebSocketUrl", "token", "Error", "wsProtocol", "window", "location", "protocol", "wsHost", "process", "env", "REACT_APP_WS_URL", "wsUrl", "encodeURIComponent", "console", "log", "replace", "sendMessage", "message", "current", "readyState", "WebSocket", "OPEN", "send", "JSON", "stringify", "error", "warn", "handleMessage", "event", "parse", "data", "type", "connect", "_websocketRef$current", "_websocketRef$current2", "CONNECTING", "CLOSED", "close", "ws", "onopen", "onmessage", "onclose", "code", "reason", "delay", "Math", "min", "pow", "prev", "setTimeout", "onerror", "disconnect", "clearTimeout"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/hooks/useWebSocket.ts"], "sourcesContent": ["import { useEffect, useRef, useState, useCallback } from 'react';\n\n// WebSocket message types based on backend NotificationTypes\nexport interface WebSocketMessage {\n  type: string;\n  title?: string;\n  message?: string;\n  action_url?: string;\n  action_text?: string;\n  priority?: 'low' | 'medium' | 'high' | 'urgent';\n  category?: 'expense' | 'group' | 'settlement' | 'approval' | 'system';\n  timestamp?: string;\n  data?: Record<string, any>;\n  expense_id?: number;\n  group_id?: number;\n  settlement_id?: number;\n  user_id?: number;\n}\n\n// Enhanced notification message structure\nexport interface EnhancedWebSocketMessage extends WebSocketMessage {\n  // Additional fields for enhanced notifications\n  status?: 'unread' | 'read' | 'archived';\n  expires_at?: string;\n}\n\n// WebSocket hook configuration\nexport interface UseWebSocketConfig {\n  onMessage?: (message: EnhancedWebSocketMessage) => void;\n  onConnect?: () => void;\n  onDisconnect?: () => void;\n  onError?: (error: Event) => void;\n  autoReconnect?: boolean;\n  maxReconnectAttempts?: number;\n  reconnectInterval?: number;\n}\n\n// WebSocket hook return type\nexport interface UseWebSocketReturn {\n  isConnected: boolean;\n  isConnecting: boolean;\n  connectionError: string | null;\n  sendMessage: (message: any) => void;\n  connect: () => void;\n  disconnect: () => void;\n  reconnectAttempts: number;\n}\n\n// Custom hook for WebSocket connection\nexport const useWebSocket = (config: UseWebSocketConfig = {}): UseWebSocketReturn => {\n  const {\n    onMessage,\n    onConnect,\n    onDisconnect,\n    onError,\n    autoReconnect = true,\n    maxReconnectAttempts = 5,\n    reconnectInterval = 1000,\n  } = config;\n\n  const [isConnected, setIsConnected] = useState(false);\n  const [isConnecting, setIsConnecting] = useState(false);\n  const [connectionError, setConnectionError] = useState<string | null>(null);\n  const [reconnectAttempts, setReconnectAttempts] = useState(0);\n\n  const websocketRef = useRef<WebSocket | null>(null);\n  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const shouldReconnectRef = useRef(true);\n  const connectionDebounceRef = useRef<NodeJS.Timeout | null>(null);\n  const lastConnectionAttemptRef = useRef<number>(0);\n\n  // Get authentication token from localStorage\n  const getAuthToken = useCallback(() => {\n    return localStorage.getItem('token') || '';\n  }, []);\n\n  // Create WebSocket URL\n  const getWebSocketUrl = useCallback(() => {\n    const token = getAuthToken();\n    if (!token) {\n      throw new Error('No authentication token available');\n    }\n\n    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n    const wsHost = process.env.REACT_APP_WS_URL || 'localhost:8000';\n    const wsUrl = `${wsProtocol}//${wsHost}/ws/notifications?token=${encodeURIComponent(token)}`;\n\n    console.log('WebSocket URL:', wsUrl.replace(/token=[^&]+/, 'token=***'));\n    return wsUrl;\n  }, [getAuthToken]);\n\n  // Send message through WebSocket\n  const sendMessage = useCallback((message: any) => {\n    if (websocketRef.current && websocketRef.current.readyState === WebSocket.OPEN) {\n      try {\n        websocketRef.current.send(JSON.stringify(message));\n      } catch (error) {\n        console.error('Failed to send WebSocket message:', error);\n      }\n    } else {\n      console.warn('WebSocket is not connected. Cannot send message:', message);\n    }\n  }, []);\n\n  // Handle WebSocket message\n  const handleMessage = useCallback((event: MessageEvent) => {\n    try {\n      const message: EnhancedWebSocketMessage = JSON.parse(event.data);\n      \n      // Handle system messages\n      switch (message.type) {\n        case 'connection_established':\n          console.log('WebSocket connection established');\n          setConnectionError(null);\n          setReconnectAttempts(0);\n          break;\n        \n        case 'heartbeat':\n          // Respond to heartbeat\n          sendMessage({ type: 'ping' });\n          break;\n        \n        case 'error':\n          console.error('WebSocket error message:', message);\n          setConnectionError(message.message || 'WebSocket error');\n          break;\n        \n        default:\n          // Pass message to callback\n          if (onMessage) {\n            onMessage(message);\n          }\n          break;\n      }\n    } catch (error) {\n      console.error('Failed to parse WebSocket message:', error);\n      setConnectionError('Failed to parse message');\n    }\n  }, [onMessage, sendMessage]);\n\n  // Connect to WebSocket\n  const connect = useCallback(() => {\n    // Prevent multiple simultaneous connections\n    if (websocketRef.current?.readyState === WebSocket.OPEN) {\n      console.log('WebSocket already connected, skipping connection attempt');\n      return; // Already connected\n    }\n\n    if (websocketRef.current?.readyState === WebSocket.CONNECTING || isConnecting) {\n      console.log('WebSocket connection already in progress, skipping duplicate attempt');\n      return; // Connection in progress\n    }\n\n    // Clean up any existing connection in a bad state\n    if (websocketRef.current && websocketRef.current.readyState !== WebSocket.CLOSED) {\n      console.log('Cleaning up existing WebSocket connection');\n      websocketRef.current.close();\n      websocketRef.current = null;\n    }\n\n    const token = getAuthToken();\n    if (!token) {\n      console.warn('Cannot connect WebSocket: No authentication token available');\n      setConnectionError('No authentication token available');\n      return;\n    }\n\n    console.log('Initiating WebSocket connection...');\n    setIsConnecting(true);\n    setConnectionError(null);\n\n    try {\n      const wsUrl = getWebSocketUrl();\n      console.log('Creating WebSocket connection to:', wsUrl.replace(/token=[^&]+/, 'token=***'));\n\n      const ws = new WebSocket(wsUrl);\n      websocketRef.current = ws;\n\n      ws.onopen = () => {\n        console.log('WebSocket connected successfully');\n        setIsConnected(true);\n        setIsConnecting(false);\n        setConnectionError(null);\n        setReconnectAttempts(0);\n        \n        if (onConnect) {\n          onConnect();\n        }\n      };\n\n      ws.onmessage = handleMessage;\n\n      ws.onclose = (event) => {\n        console.log('WebSocket disconnected:', event.code, event.reason);\n        setIsConnected(false);\n        setIsConnecting(false);\n        websocketRef.current = null;\n\n        if (onDisconnect) {\n          onDisconnect();\n        }\n\n        // Attempt to reconnect if not a normal closure and auto-reconnect is enabled\n        if (\n          autoReconnect &&\n          shouldReconnectRef.current &&\n          event.code !== 1000 &&\n          reconnectAttempts < maxReconnectAttempts\n        ) {\n          const delay = Math.min(reconnectInterval * Math.pow(2, reconnectAttempts), 30000); // Cap at 30 seconds\n          console.log(`Attempting to reconnect in ${delay}ms (attempt ${reconnectAttempts + 1}/${maxReconnectAttempts})`);\n\n          setReconnectAttempts(prev => prev + 1);\n\n          reconnectTimeoutRef.current = setTimeout(() => {\n            if (shouldReconnectRef.current) {\n              // Check if we still have a valid token before reconnecting\n              const token = localStorage.getItem('token');\n              if (token) {\n                connect();\n              } else {\n                console.log('No token available, stopping reconnection attempts');\n                setConnectionError('Authentication token not available');\n                shouldReconnectRef.current = false;\n              }\n            }\n          }, delay);\n        } else if (reconnectAttempts >= maxReconnectAttempts) {\n          console.log('Maximum reconnection attempts reached, stopping reconnection');\n          setConnectionError('Maximum reconnection attempts reached');\n          shouldReconnectRef.current = false;\n        }\n      };\n\n      ws.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        setConnectionError('WebSocket connection error');\n        setIsConnecting(false);\n        \n        if (onError) {\n          onError(error);\n        }\n      };\n\n    } catch (error) {\n      console.error('Failed to create WebSocket connection:', error);\n      setConnectionError('Failed to create WebSocket connection');\n      setIsConnecting(false);\n    }\n  }, [\n    isConnecting,\n    getAuthToken,\n    getWebSocketUrl,\n    handleMessage,\n    onConnect,\n    onDisconnect,\n    onError,\n    autoReconnect,\n    maxReconnectAttempts,\n    reconnectInterval,\n    reconnectAttempts,\n  ]);\n\n  // Disconnect from WebSocket\n  const disconnect = useCallback(() => {\n    shouldReconnectRef.current = false;\n    \n    if (reconnectTimeoutRef.current) {\n      clearTimeout(reconnectTimeoutRef.current);\n      reconnectTimeoutRef.current = null;\n    }\n\n    if (websocketRef.current) {\n      websocketRef.current.close(1000, 'User disconnected');\n      websocketRef.current = null;\n    }\n\n    setIsConnected(false);\n    setIsConnecting(false);\n    setConnectionError(null);\n    setReconnectAttempts(0);\n  }, []);\n\n  // Auto-connect on mount and when token changes\n  useEffect(() => {\n    const token = getAuthToken();\n    if (token && shouldReconnectRef.current) {\n      connect();\n    }\n\n    return () => {\n      shouldReconnectRef.current = false;\n      disconnect();\n    };\n  }, [connect, disconnect, getAuthToken]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      shouldReconnectRef.current = false;\n      if (reconnectTimeoutRef.current) {\n        clearTimeout(reconnectTimeoutRef.current);\n      }\n      if (websocketRef.current) {\n        websocketRef.current.close(1000, 'Component unmounted');\n      }\n    };\n  }, []);\n\n  return {\n    isConnected,\n    isConnecting,\n    connectionError,\n    sendMessage,\n    connect,\n    disconnect,\n    reconnectAttempts,\n  };\n};\n\nexport default useWebSocket;\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;;AAEhE;;AAiBA;;AAOA;;AAWA;;AAWA;AACA,OAAO,MAAMC,YAAY,GAAGA,CAACC,MAA0B,GAAG,CAAC,CAAC,KAAyB;EAAAC,EAAA;EACnF,MAAM;IACJC,SAAS;IACTC,SAAS;IACTC,YAAY;IACZC,OAAO;IACPC,aAAa,GAAG,IAAI;IACpBC,oBAAoB,GAAG,CAAC;IACxBC,iBAAiB,GAAG;EACtB,CAAC,GAAGR,MAAM;EAEV,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAM,CAACkB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAE7D,MAAMoB,YAAY,GAAGrB,MAAM,CAAmB,IAAI,CAAC;EACnD,MAAMsB,mBAAmB,GAAGtB,MAAM,CAAwB,IAAI,CAAC;EAC/D,MAAMuB,kBAAkB,GAAGvB,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMwB,qBAAqB,GAAGxB,MAAM,CAAwB,IAAI,CAAC;EACjE,MAAMyB,wBAAwB,GAAGzB,MAAM,CAAS,CAAC,CAAC;;EAElD;EACA,MAAM0B,YAAY,GAAGxB,WAAW,CAAC,MAAM;IACrC,OAAOyB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;EAC5C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,eAAe,GAAG3B,WAAW,CAAC,MAAM;IACxC,MAAM4B,KAAK,GAAGJ,YAAY,CAAC,CAAC;IAC5B,IAAI,CAACI,KAAK,EAAE;MACV,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;IACtD;IAEA,MAAMC,UAAU,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;IACzE,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB,IAAI,gBAAgB;IAC/D,MAAMC,KAAK,GAAG,GAAGR,UAAU,KAAKI,MAAM,2BAA2BK,kBAAkB,CAACX,KAAK,CAAC,EAAE;IAE5FY,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEH,KAAK,CAACI,OAAO,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;IACxE,OAAOJ,KAAK;EACd,CAAC,EAAE,CAACd,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMmB,WAAW,GAAG3C,WAAW,CAAE4C,OAAY,IAAK;IAChD,IAAIzB,YAAY,CAAC0B,OAAO,IAAI1B,YAAY,CAAC0B,OAAO,CAACC,UAAU,KAAKC,SAAS,CAACC,IAAI,EAAE;MAC9E,IAAI;QACF7B,YAAY,CAAC0B,OAAO,CAACI,IAAI,CAACC,IAAI,CAACC,SAAS,CAACP,OAAO,CAAC,CAAC;MACpD,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;IACF,CAAC,MAAM;MACLZ,OAAO,CAACa,IAAI,CAAC,kDAAkD,EAAET,OAAO,CAAC;IAC3E;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,aAAa,GAAGtD,WAAW,CAAEuD,KAAmB,IAAK;IACzD,IAAI;MACF,MAAMX,OAAiC,GAAGM,IAAI,CAACM,KAAK,CAACD,KAAK,CAACE,IAAI,CAAC;;MAEhE;MACA,QAAQb,OAAO,CAACc,IAAI;QAClB,KAAK,wBAAwB;UAC3BlB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/CzB,kBAAkB,CAAC,IAAI,CAAC;UACxBE,oBAAoB,CAAC,CAAC,CAAC;UACvB;QAEF,KAAK,WAAW;UACd;UACAyB,WAAW,CAAC;YAAEe,IAAI,EAAE;UAAO,CAAC,CAAC;UAC7B;QAEF,KAAK,OAAO;UACVlB,OAAO,CAACY,KAAK,CAAC,0BAA0B,EAAER,OAAO,CAAC;UAClD5B,kBAAkB,CAAC4B,OAAO,CAACA,OAAO,IAAI,iBAAiB,CAAC;UACxD;QAEF;UACE;UACA,IAAIxC,SAAS,EAAE;YACbA,SAAS,CAACwC,OAAO,CAAC;UACpB;UACA;MACJ;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1DpC,kBAAkB,CAAC,yBAAyB,CAAC;IAC/C;EACF,CAAC,EAAE,CAACZ,SAAS,EAAEuC,WAAW,CAAC,CAAC;;EAE5B;EACA,MAAMgB,OAAO,GAAG3D,WAAW,CAAC,MAAM;IAAA,IAAA4D,qBAAA,EAAAC,sBAAA;IAChC;IACA,IAAI,EAAAD,qBAAA,GAAAzC,YAAY,CAAC0B,OAAO,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBd,UAAU,MAAKC,SAAS,CAACC,IAAI,EAAE;MACvDR,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE,OAAO,CAAC;IACV;IAEA,IAAI,EAAAoB,sBAAA,GAAA1C,YAAY,CAAC0B,OAAO,cAAAgB,sBAAA,uBAApBA,sBAAA,CAAsBf,UAAU,MAAKC,SAAS,CAACe,UAAU,IAAIjD,YAAY,EAAE;MAC7E2B,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;MACnF,OAAO,CAAC;IACV;;IAEA;IACA,IAAItB,YAAY,CAAC0B,OAAO,IAAI1B,YAAY,CAAC0B,OAAO,CAACC,UAAU,KAAKC,SAAS,CAACgB,MAAM,EAAE;MAChFvB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxDtB,YAAY,CAAC0B,OAAO,CAACmB,KAAK,CAAC,CAAC;MAC5B7C,YAAY,CAAC0B,OAAO,GAAG,IAAI;IAC7B;IAEA,MAAMjB,KAAK,GAAGJ,YAAY,CAAC,CAAC;IAC5B,IAAI,CAACI,KAAK,EAAE;MACVY,OAAO,CAACa,IAAI,CAAC,6DAA6D,CAAC;MAC3ErC,kBAAkB,CAAC,mCAAmC,CAAC;MACvD;IACF;IAEAwB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IACjD3B,eAAe,CAAC,IAAI,CAAC;IACrBE,kBAAkB,CAAC,IAAI,CAAC;IAExB,IAAI;MACF,MAAMsB,KAAK,GAAGX,eAAe,CAAC,CAAC;MAC/Ba,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEH,KAAK,CAACI,OAAO,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;MAE3F,MAAMuB,EAAE,GAAG,IAAIlB,SAAS,CAACT,KAAK,CAAC;MAC/BnB,YAAY,CAAC0B,OAAO,GAAGoB,EAAE;MAEzBA,EAAE,CAACC,MAAM,GAAG,MAAM;QAChB1B,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C7B,cAAc,CAAC,IAAI,CAAC;QACpBE,eAAe,CAAC,KAAK,CAAC;QACtBE,kBAAkB,CAAC,IAAI,CAAC;QACxBE,oBAAoB,CAAC,CAAC,CAAC;QAEvB,IAAIb,SAAS,EAAE;UACbA,SAAS,CAAC,CAAC;QACb;MACF,CAAC;MAED4D,EAAE,CAACE,SAAS,GAAGb,aAAa;MAE5BW,EAAE,CAACG,OAAO,GAAIb,KAAK,IAAK;QACtBf,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEc,KAAK,CAACc,IAAI,EAAEd,KAAK,CAACe,MAAM,CAAC;QAChE1D,cAAc,CAAC,KAAK,CAAC;QACrBE,eAAe,CAAC,KAAK,CAAC;QACtBK,YAAY,CAAC0B,OAAO,GAAG,IAAI;QAE3B,IAAIvC,YAAY,EAAE;UAChBA,YAAY,CAAC,CAAC;QAChB;;QAEA;QACA,IACEE,aAAa,IACba,kBAAkB,CAACwB,OAAO,IAC1BU,KAAK,CAACc,IAAI,KAAK,IAAI,IACnBpD,iBAAiB,GAAGR,oBAAoB,EACxC;UACA,MAAM8D,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC/D,iBAAiB,GAAG8D,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEzD,iBAAiB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UACnFuB,OAAO,CAACC,GAAG,CAAC,8BAA8B8B,KAAK,eAAetD,iBAAiB,GAAG,CAAC,IAAIR,oBAAoB,GAAG,CAAC;UAE/GS,oBAAoB,CAACyD,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;UAEtCvD,mBAAmB,CAACyB,OAAO,GAAG+B,UAAU,CAAC,MAAM;YAC7C,IAAIvD,kBAAkB,CAACwB,OAAO,EAAE;cAC9B;cACA,MAAMjB,KAAK,GAAGH,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAIE,KAAK,EAAE;gBACT+B,OAAO,CAAC,CAAC;cACX,CAAC,MAAM;gBACLnB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;gBACjEzB,kBAAkB,CAAC,oCAAoC,CAAC;gBACxDK,kBAAkB,CAACwB,OAAO,GAAG,KAAK;cACpC;YACF;UACF,CAAC,EAAE0B,KAAK,CAAC;QACX,CAAC,MAAM,IAAItD,iBAAiB,IAAIR,oBAAoB,EAAE;UACpD+B,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;UAC3EzB,kBAAkB,CAAC,uCAAuC,CAAC;UAC3DK,kBAAkB,CAACwB,OAAO,GAAG,KAAK;QACpC;MACF,CAAC;MAEDoB,EAAE,CAACY,OAAO,GAAIzB,KAAK,IAAK;QACtBZ,OAAO,CAACY,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QACxCpC,kBAAkB,CAAC,4BAA4B,CAAC;QAChDF,eAAe,CAAC,KAAK,CAAC;QAEtB,IAAIP,OAAO,EAAE;UACXA,OAAO,CAAC6C,KAAK,CAAC;QAChB;MACF,CAAC;IAEH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9DpC,kBAAkB,CAAC,uCAAuC,CAAC;MAC3DF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC,EAAE,CACDD,YAAY,EACZW,YAAY,EACZG,eAAe,EACf2B,aAAa,EACbjD,SAAS,EACTC,YAAY,EACZC,OAAO,EACPC,aAAa,EACbC,oBAAoB,EACpBC,iBAAiB,EACjBO,iBAAiB,CAClB,CAAC;;EAEF;EACA,MAAM6D,UAAU,GAAG9E,WAAW,CAAC,MAAM;IACnCqB,kBAAkB,CAACwB,OAAO,GAAG,KAAK;IAElC,IAAIzB,mBAAmB,CAACyB,OAAO,EAAE;MAC/BkC,YAAY,CAAC3D,mBAAmB,CAACyB,OAAO,CAAC;MACzCzB,mBAAmB,CAACyB,OAAO,GAAG,IAAI;IACpC;IAEA,IAAI1B,YAAY,CAAC0B,OAAO,EAAE;MACxB1B,YAAY,CAAC0B,OAAO,CAACmB,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC;MACrD7C,YAAY,CAAC0B,OAAO,GAAG,IAAI;IAC7B;IAEAjC,cAAc,CAAC,KAAK,CAAC;IACrBE,eAAe,CAAC,KAAK,CAAC;IACtBE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,oBAAoB,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArB,SAAS,CAAC,MAAM;IACd,MAAM+B,KAAK,GAAGJ,YAAY,CAAC,CAAC;IAC5B,IAAII,KAAK,IAAIP,kBAAkB,CAACwB,OAAO,EAAE;MACvCc,OAAO,CAAC,CAAC;IACX;IAEA,OAAO,MAAM;MACXtC,kBAAkB,CAACwB,OAAO,GAAG,KAAK;MAClCiC,UAAU,CAAC,CAAC;IACd,CAAC;EACH,CAAC,EAAE,CAACnB,OAAO,EAAEmB,UAAU,EAAEtD,YAAY,CAAC,CAAC;;EAEvC;EACA3B,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXwB,kBAAkB,CAACwB,OAAO,GAAG,KAAK;MAClC,IAAIzB,mBAAmB,CAACyB,OAAO,EAAE;QAC/BkC,YAAY,CAAC3D,mBAAmB,CAACyB,OAAO,CAAC;MAC3C;MACA,IAAI1B,YAAY,CAAC0B,OAAO,EAAE;QACxB1B,YAAY,CAAC0B,OAAO,CAACmB,KAAK,CAAC,IAAI,EAAE,qBAAqB,CAAC;MACzD;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLrD,WAAW;IACXE,YAAY;IACZE,eAAe;IACf4B,WAAW;IACXgB,OAAO;IACPmB,UAAU;IACV7D;EACF,CAAC;AACH,CAAC;AAACd,EAAA,CA7QWF,YAAY;AA+QzB,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}