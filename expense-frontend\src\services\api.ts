import axios, { AxiosResponse } from 'axios';
import {
  User,
  Group,
  Expense,
  Balance,
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  CreateExpenseRequest,
  CreateGroupRequest,
  JoinGroupRequest,
  SettlementRequest,
  SettlementResult,
  NLPRequest,
  NLPResponse,
} from '../types';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401 || error.response?.status === 403) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (data: LoginRequest): Promise<AxiosResponse<AuthResponse>> =>
    api.post('/auth/login', data),
  
  register: (data: RegisterRequest): Promise<AxiosResponse<User>> =>
    api.post('/auth/register', data),
  
  getCurrentUser: (): Promise<AxiosResponse<User>> =>
    api.get('/auth/me'),
};

// Groups API
export const groupsAPI = {
  getMyGroups: (): Promise<AxiosResponse<Group[]>> =>
    api.get('/groups/my-groups'),
  
  createGroup: (data: CreateGroupRequest): Promise<AxiosResponse<Group>> =>
    api.post('/groups/create', data),
  
  joinGroup: (data: JoinGroupRequest): Promise<AxiosResponse<Group>> =>
    api.post('/groups/join', data),
};

// Enhanced Group Management API
export const groupManagementAPI = {
  // Group details and management
  getGroupDetails: (groupId: number): Promise<AxiosResponse<any>> =>
    api.get(`/group-management/groups/${groupId}/details`),

  updateGroup: (groupId: number, data: { name?: string; description?: string }): Promise<AxiosResponse<any>> =>
    api.put(`/group-management/groups/${groupId}`, data),

  // Member management
  removeMember: (groupId: number, userId: number): Promise<AxiosResponse<any>> =>
    api.delete(`/group-management/groups/${groupId}/members/${userId}`),

  leaveGroup: (groupId: number): Promise<AxiosResponse<any>> =>
    api.post(`/group-management/groups/${groupId}/leave`),

  transferOwnership: (groupId: number, newOwnerId: number): Promise<AxiosResponse<any>> =>
    api.post(`/group-management/groups/${groupId}/transfer-ownership`, { new_owner_id: newOwnerId }),

  checkCanLeave: (groupId: number): Promise<AxiosResponse<any>> =>
    api.get(`/group-management/groups/${groupId}/can-leave`),

  // Join request management
  requestToJoin: (groupId: number, message?: string): Promise<AxiosResponse<any>> =>
    api.post(`/group-management/groups/${groupId}/join-request`, { message }),

  getPendingJoinRequests: (groupId?: number): Promise<AxiosResponse<any[]>> =>
    api.get(`/group-management/groups/join-requests${groupId ? `?group_id=${groupId}` : ''}`),

  processJoinRequest: (requestId: number, approved: boolean): Promise<AxiosResponse<any>> =>
    api.post(`/group-management/groups/join-requests/${requestId}/process`, { approved }),
};

// Settings API
export const settingsAPI = {
  // User profile settings
  updateProfile: (data: { name?: string; email?: string }): Promise<AxiosResponse<any>> =>
    api.put('/settings/profile', data),

  changePassword: (data: { current_password: string; new_password: string }): Promise<AxiosResponse<any>> =>
    api.put('/settings/password', data),

  updateGroqApiKey: (data: { groq_api_key: string }): Promise<AxiosResponse<any>> =>
    api.put('/settings/groq-api-key', data),

  // Notification preferences
  getNotificationPreferences: (): Promise<AxiosResponse<any>> =>
    api.get('/settings/notifications'),

  updateNotificationPreferences: (data: any): Promise<AxiosResponse<any>> =>
    api.put('/settings/notifications', data),

  // Theme and display settings
  getDisplaySettings: (): Promise<AxiosResponse<any>> =>
    api.get('/settings/display'),

  updateDisplaySettings: (data: { theme?: string; currency?: string; language?: string }): Promise<AxiosResponse<any>> =>
    api.put('/settings/display', data),

  // Data export/import
  exportData: (): Promise<AxiosResponse<any>> =>
    api.get('/settings/export-data'),

  // Account management
  deleteAccount: (password: string): Promise<AxiosResponse<any>> =>
    api.delete('/settings/account', { data: { password } }),
};

// Expenses API
export const expensesAPI = {
  createExpense: (data: CreateExpenseRequest): Promise<AxiosResponse<Expense>> =>
    api.post('/expenses/create', data),

  getBalances: (): Promise<AxiosResponse<Balance[]>> =>
    api.get('/expenses/balances'),

  getDebts: (): Promise<AxiosResponse<Balance[]>> =>
    api.get('/expenses/debts'),

  getHistory: (groupId?: number, limit?: number): Promise<AxiosResponse<Expense[]>> =>
    api.get('/expenses/history', { params: { group_id: groupId, limit } }),

  settleDebt: (data: SettlementRequest): Promise<AxiosResponse<SettlementResult>> =>
    api.post('/expenses/settle', data),

  // Bulk operations
  bulkApprove: (data: { expense_ids: number[]; approved: boolean }): Promise<AxiosResponse<any>> =>
    api.post('/expenses/bulk/approve', data),

  bulkDelete: (data: { expense_ids: number[] }): Promise<AxiosResponse<any>> =>
    api.post('/expenses/bulk/delete', data),

  bulkCategorize: (data: { expense_ids: number[]; category: string }): Promise<AxiosResponse<any>> =>
    api.post('/expenses/bulk/categorize', data),

  // Advanced filtering
  getFilteredExpenses: (filters: any): Promise<AxiosResponse<any>> =>
    api.get('/expenses/filter', { params: filters }),

  // Export functionality
  getExportSummary: (filters: any): Promise<AxiosResponse<any>> =>
    api.get('/expenses/export/summary', { params: filters }),

  exportCSV: (filters: any): Promise<AxiosResponse<any>> =>
    api.get('/expenses/export/csv', { params: filters, responseType: 'blob' }),

  exportPDF: (filters: any): Promise<AxiosResponse<any>> =>
    api.get('/expenses/export/pdf', { params: filters, responseType: 'blob' }),
};

// Profile API
export const profileAPI = {
  getProfile: (): Promise<AxiosResponse<any>> =>
    api.get('/profile/me'),

  updateProfile: (data: any): Promise<AxiosResponse<any>> =>
    api.put('/profile/update', data),

  updateNotifications: (data: any): Promise<AxiosResponse<any>> =>
    api.put('/profile/notifications', data),

  uploadAvatar: (formData: FormData): Promise<AxiosResponse<any>> =>
    api.post('/profile/avatar/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    }),

  deleteAvatar: (): Promise<AxiosResponse<any>> =>
    api.delete('/profile/avatar'),

  changePassword: (data: { current_password: string; new_password: string }): Promise<AxiosResponse<any>> =>
    api.put('/profile/password', data),

  updateGroqApiKey: (data: { groq_api_key: string }): Promise<AxiosResponse<any>> =>
    api.put('/profile/groq-api-key', data),

  getStatistics: (): Promise<AxiosResponse<any>> =>
    api.get('/profile/statistics'),

  deleteAccount: (data: { password: string }): Promise<AxiosResponse<any>> =>
    api.delete('/profile/account', { data }),
};

// Recurring Expenses API
export const recurringExpensesAPI = {
  createTemplate: (data: any): Promise<AxiosResponse<any>> =>
    api.post('/recurring-expenses/templates', data),

  getTemplates: (): Promise<AxiosResponse<any>> =>
    api.get('/recurring-expenses/templates'),

  getTemplateDetails: (templateId: number): Promise<AxiosResponse<any>> =>
    api.get(`/recurring-expenses/templates/${templateId}`),

  updateTemplate: (templateId: number, data: any): Promise<AxiosResponse<any>> =>
    api.put(`/recurring-expenses/templates/${templateId}`, data),

  deleteTemplate: (templateId: number): Promise<AxiosResponse<any>> =>
    api.delete(`/recurring-expenses/templates/${templateId}`),

  executeTemplate: (templateId: number): Promise<AxiosResponse<any>> =>
    api.post(`/recurring-expenses/templates/${templateId}/execute`),

  getDueTemplates: (): Promise<AxiosResponse<any>> =>
    api.get('/recurring-expenses/due-templates'),

  processDueTemplates: (): Promise<AxiosResponse<any>> =>
    api.post('/recurring-expenses/process-due'),
};

// Categorization API
export const categorizationAPI = {
  // Categories
  createCategory: (data: any): Promise<AxiosResponse<any>> =>
    api.post('/categorization/categories', data),

  getCategories: (): Promise<AxiosResponse<any>> =>
    api.get('/categorization/categories'),

  updateCategory: (categoryId: number, data: any): Promise<AxiosResponse<any>> =>
    api.put(`/categorization/categories/${categoryId}`, data),

  deleteCategory: (categoryId: number): Promise<AxiosResponse<any>> =>
    api.delete(`/categorization/categories/${categoryId}`),

  // Tags
  createTag: (data: any): Promise<AxiosResponse<any>> =>
    api.post('/categorization/tags', data),

  getTags: (): Promise<AxiosResponse<any>> =>
    api.get('/categorization/tags'),

  updateTag: (tagId: number, data: any): Promise<AxiosResponse<any>> =>
    api.put(`/categorization/tags/${tagId}`, data),

  deleteTag: (tagId: number): Promise<AxiosResponse<any>> =>
    api.delete(`/categorization/tags/${tagId}`),

  // Expense categorization
  categorizeExpense: (expenseId: number, categoryId?: number, tagIds?: number[]): Promise<AxiosResponse<any>> =>
    api.put(`/categorization/expenses/${expenseId}/categorize`, null, {
      params: { category_id: categoryId, tag_ids: tagIds }
    }),

  // Analytics
  getAnalytics: (groupId?: number): Promise<AxiosResponse<any>> =>
    api.get('/categorization/analytics', { params: { group_id: groupId } }),

  // System categories
  initializeSystemCategories: (): Promise<AxiosResponse<any>> =>
    api.post('/categorization/initialize-system-categories'),
};

// Enhanced Notifications API
export const notificationsAPI = {
  // Get notifications
  getNotifications: (limit: number = 50, offset: number = 0, status?: string, category?: string): Promise<AxiosResponse<any>> =>
    api.get('/notifications/', { params: { limit, offset, status, category } }),

  getRecentNotifications: (limit: number = 10): Promise<AxiosResponse<any>> =>
    api.get('/notifications/recent', { params: { limit } }),

  getSummary: (): Promise<AxiosResponse<any>> =>
    api.get('/notifications/summary'),

  getUnreadCount: (): Promise<AxiosResponse<any>> =>
    api.get('/notifications/unread/count'),

  // Mark notifications as read
  markAsRead: (notificationId: number): Promise<AxiosResponse<any>> =>
    api.put(`/notifications/${notificationId}/read`),

  markBulkAsRead: (notificationIds: number[]): Promise<AxiosResponse<any>> =>
    api.put('/notifications/bulk/read', { notification_ids: notificationIds, status: 'read' }),

  // Notification preferences
  getPreferences: (): Promise<AxiosResponse<any>> =>
    api.get('/notifications/preferences'),

  updatePreferences: (preferences: any[]): Promise<AxiosResponse<any>> =>
    api.put('/notifications/preferences', preferences),

  // Templates and admin functions
  getTemplates: (): Promise<AxiosResponse<any>> =>
    api.get('/notifications/templates'),

  initializeTemplates: (): Promise<AxiosResponse<any>> =>
    api.post('/notifications/templates/initialize'),

  // Cleanup
  cleanupOldNotifications: (days: number = 30): Promise<AxiosResponse<any>> =>
    api.delete('/notifications/cleanup', { params: { days } }),
};

// NLP API
export const nlpAPI = {
  interpret: (data: NLPRequest): Promise<AxiosResponse<NLPResponse>> =>
    api.post('/nlp/interpret', data),

  getExamples: (): Promise<AxiosResponse<any>> =>
    api.get('/nlp/examples'),
};

// Approvals API
export const approvalsAPI = {
  getPendingApprovals: (): Promise<AxiosResponse<any[]>> =>
    api.get('/approvals/pending'),

  approveExpense: (expenseId: number, approved: boolean): Promise<AxiosResponse<any>> =>
    api.post(`/approvals/approve/${expenseId}?approved=${approved}`),

  bulkApproveExpenses: (expenseIds: number[], approved: boolean): Promise<AxiosResponse<any>> =>
    api.post('/approvals/bulk-approve', { expense_ids: expenseIds, approved }),

  getExpenseApprovals: (expenseId: number): Promise<AxiosResponse<any[]>> =>
    api.get(`/approvals/expense/${expenseId}/approvals`),

  // Settlement confirmations
  createSettlement: (data: { recipient_id: number; amount: number; description?: string }): Promise<AxiosResponse<any>> =>
    api.post('/approvals/settlements', data),

  getPendingSettlements: (): Promise<AxiosResponse<any[]>> =>
    api.get('/approvals/settlements/pending'),

  confirmSettlement: (settlementId: number, confirmed: boolean): Promise<AxiosResponse<any>> =>
    api.post(`/approvals/settlements/${settlementId}/confirm?confirmed=${confirmed}`),

  getSettlementHistory: (status?: string): Promise<AxiosResponse<any[]>> =>
    api.get('/approvals/settlements/history', { params: { status } }),
};

export default api;
