from fastapi import FastAP<PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from sqlalchemy.orm import Session
from .database import Base, engine, get_db, get_db_health
from .routers import auth, groups, expenses, nlp, approvals, group_management, settings, websocket, profile, recurring_expenses, categorization, notifications
# Create database tables on startup
Base.metadata.create_all(bind=engine)

# Initialize FastAPI app
app = FastAPI(
    title="Expense Tracker API",
    version="1.0.0",
    description="""
    ## Expense Tracker API with NLP Integration

    A comprehensive expense tracking system that supports:

    * **User Management**: Registration, authentication with JWT tokens
    * **Group Management**: Create and join expense-sharing groups
    * **Expense Tracking**: Add expenses split among group members
    * **Settlement System**: Settle debts between users with partial/full payments
    * **Natural Language Processing**: Use LLaMA 3.3 70B via Groq Cloud for natural language commands

    ### Natural Language Examples:
    * "Add lunch 25 split among roommates"
    * "Settle 500 with <PERSON>"
    * "What's my balance?"
    * "Show me expenses from work group"

    ### Authentication:
    All endpoints (except registration and login) require a Bearer token in the Authorization header.
    """,
    contact={
        "name": "Expense Tracker API",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
    },
)

# Include routers (remove duplicate prefixes since routers already have them)
app.include_router(auth.router)
app.include_router(groups.router)
app.include_router(expenses.router)
app.include_router(nlp.router)
app.include_router(approvals.router)
app.include_router(group_management.router)
app.include_router(settings.router)
app.include_router(websocket.router)
app.include_router(profile.router)
app.include_router(recurring_expenses.router)
app.include_router(categorization.router)
app.include_router(notifications.router)

def log_routes(app):
    for route in app.router.routes:
        print(f"ROUTE {route.name}: {route.path}")
log_routes(app)

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoints
@app.get("/")
def read_root():
    return {"message": "Expense Tracker API is running"}

@app.get("/health")
def health_check():
    """
    Comprehensive health check endpoint
    """
    db_health = get_db_health()
    return {
        "status": "healthy" if db_health["status"] == "healthy" else "unhealthy",
        "api": "running",
        "database": db_health,
        "version": "1.0.0"
    }

@app.get("/health/db")
def database_health_check():
    """
    Database-specific health check
    """
    return get_db_health()


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT"
        }
    }
    for path in openapi_schema["paths"].values():
        for method in path.values():
            method.setdefault("security", [{"BearerAuth": []}])
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi