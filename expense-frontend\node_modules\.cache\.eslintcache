[{"C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Dashboard.tsx": "3", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\ProtectedRoute.tsx": "5", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Auth\\LoginForm.tsx": "6", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\AIChat.tsx": "7", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Auth\\RegisterForm.tsx": "8", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\services\\api.ts": "9", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Layout\\Layout.tsx": "10", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Layout\\Header.tsx": "11", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Layout\\Sidebar.tsx": "12", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Groups.tsx": "13", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Settlements.tsx": "14", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Expenses.tsx": "15", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\utils\\formatters.ts": "16", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Approvals.tsx": "17", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\SettlementConfirmations.tsx": "18", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\services\\notificationService.ts": "19", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\hooks\\useAutoRefresh.ts": "20", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\GroupManagement\\GroupManagementModal.tsx": "21", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Settings.tsx": "22", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\UI\\ConfirmationDialog.tsx": "23", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\UI\\NotificationPanel.tsx": "24", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\contexts\\ThemeContext.tsx": "25", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\UI\\ThemeToggle.tsx": "26", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\contexts\\WebSocketContext.tsx": "27", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\hooks\\useWebSocket.ts": "28", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Debug\\WebSocketDebug.tsx": "29", "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\utils\\circuitBreaker.ts": "30"}, {"size": 274, "mtime": 1753437234701, "results": "31", "hashOfConfig": "32"}, {"size": 3849, "mtime": 1753506932268, "results": "33", "hashOfConfig": "32"}, {"size": 9548, "mtime": 1753508698144, "results": "34", "hashOfConfig": "32"}, {"size": 3362, "mtime": 1753509179698, "results": "35", "hashOfConfig": "32"}, {"size": 682, "mtime": 1753437181945, "results": "36", "hashOfConfig": "32"}, {"size": 4147, "mtime": 1753437003408, "results": "37", "hashOfConfig": "32"}, {"size": 8571, "mtime": 1753437172037, "results": "38", "hashOfConfig": "32"}, {"size": 5838, "mtime": 1753437038296, "results": "39", "hashOfConfig": "32"}, {"size": 13210, "mtime": 1753505296074, "results": "40", "hashOfConfig": "32"}, {"size": 826, "mtime": 1753473774295, "results": "41", "hashOfConfig": "32"}, {"size": 1553, "mtime": 1753473754253, "results": "42", "hashOfConfig": "32"}, {"size": 3160, "mtime": 1753473817045, "results": "43", "hashOfConfig": "32"}, {"size": 17364, "mtime": 1753470529092, "results": "44", "hashOfConfig": "32"}, {"size": 14757, "mtime": 1753456043289, "results": "45", "hashOfConfig": "32"}, {"size": 11815, "mtime": 1753456004382, "results": "46", "hashOfConfig": "32"}, {"size": 1713, "mtime": 1753444405103, "results": "47", "hashOfConfig": "32"}, {"size": 12373, "mtime": 1753456131120, "results": "48", "hashOfConfig": "32"}, {"size": 14285, "mtime": 1753462462527, "results": "49", "hashOfConfig": "32"}, {"size": 7159, "mtime": 1753509128948, "results": "50", "hashOfConfig": "32"}, {"size": 1029, "mtime": 1753451931063, "results": "51", "hashOfConfig": "32"}, {"size": 28569, "mtime": 1753471467771, "results": "52", "hashOfConfig": "32"}, {"size": 36914, "mtime": 1753463413844, "results": "53", "hashOfConfig": "32"}, {"size": 4586, "mtime": 1753464280031, "results": "54", "hashOfConfig": "32"}, {"size": 6507, "mtime": 1753473026664, "results": "55", "hashOfConfig": "32"}, {"size": 3290, "mtime": 1753473642989, "results": "56", "hashOfConfig": "32"}, {"size": 3512, "mtime": 1753473663575, "results": "57", "hashOfConfig": "32"}, {"size": 6228, "mtime": 1753509293608, "results": "58", "hashOfConfig": "32"}, {"size": 10948, "mtime": 1753509278581, "results": "59", "hashOfConfig": "32"}, {"size": 6780, "mtime": 1753506862702, "results": "60", "hashOfConfig": "32"}, {"size": 4460, "mtime": 1753508632722, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "11ylyi8", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Dashboard.tsx", ["152"], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\contexts\\AuthContext.tsx", ["153"], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Auth\\LoginForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\AIChat.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Auth\\RegisterForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Groups.tsx", ["154"], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Settlements.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Expenses.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\utils\\formatters.ts", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Approvals.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\SettlementConfirmations.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\services\\notificationService.ts", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\hooks\\useAutoRefresh.ts", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\GroupManagement\\GroupManagementModal.tsx", ["155"], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\pages\\Settings.tsx", ["156", "157", "158", "159"], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\UI\\ConfirmationDialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\UI\\NotificationPanel.tsx", ["160"], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\contexts\\ThemeContext.tsx", ["161", "162"], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\UI\\ThemeToggle.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\contexts\\WebSocketContext.tsx", ["163"], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\hooks\\useWebSocket.ts", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\components\\Debug\\WebSocketDebug.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Folio3\\expense-frontend\\src\\utils\\circuitBreaker.ts", [], [], {"ruleId": "164", "severity": 1, "message": "165", "line": 104, "column": 6, "nodeType": "166", "endLine": 104, "endColumn": 8, "suggestions": "167"}, {"ruleId": "168", "severity": 1, "message": "169", "line": 4, "column": 10, "nodeType": "170", "messageId": "171", "endLine": 4, "endColumn": 29}, {"ruleId": "168", "severity": 1, "message": "172", "line": 68, "column": 13, "nodeType": "170", "messageId": "171", "endLine": 68, "endColumn": 21}, {"ruleId": "164", "severity": 1, "message": "173", "line": 109, "column": 6, "nodeType": "166", "endLine": 109, "endColumn": 23, "suggestions": "174"}, {"ruleId": "168", "severity": 1, "message": "175", "line": 20, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 20, "endColumn": 14}, {"ruleId": "168", "severity": 1, "message": "176", "line": 21, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 21, "endColumn": 7}, {"ruleId": "168", "severity": 1, "message": "177", "line": 22, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 22, "endColumn": 6}, {"ruleId": "168", "severity": 1, "message": "178", "line": 23, "column": 3, "nodeType": "170", "messageId": "171", "endLine": 23, "endColumn": 13}, {"ruleId": "168", "severity": 1, "message": "179", "line": 2, "column": 19, "nodeType": "170", "messageId": "171", "endLine": 2, "endColumn": 24}, {"ruleId": "164", "severity": 1, "message": "180", "line": 69, "column": 6, "nodeType": "166", "endLine": 69, "endColumn": 13, "suggestions": "181"}, {"ruleId": "164", "severity": 1, "message": "180", "line": 92, "column": 6, "nodeType": "166", "endLine": 92, "endColumn": 13, "suggestions": "182"}, {"ruleId": "164", "severity": 1, "message": "183", "line": 174, "column": 6, "nodeType": "166", "endLine": 174, "endColumn": 65, "suggestions": "184"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["185"], "@typescript-eslint/no-unused-vars", "'notificationService' is defined but never used.", "Identifier", "unusedVar", "'response' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'checkCanLeave', 'isOwner', 'loadGroupDetails', and 'loadJoinRequests'. Either include them or remove the dependency array.", ["186"], "'CheckCircle' is defined but never used.", "'Moon' is defined but never used.", "'Sun' is defined but never used.", "'DollarSign' is defined but never used.", "'Check' is defined but never used.", "React Hook useEffect has a missing dependency: 'calculateActualTheme'. Either include it or remove the dependency array.", ["187"], ["188"], "React Hook useEffect has a missing dependency: 'webSocketReturn'. Either include it or remove the dependency array.", ["189"], {"desc": "190", "fix": "191"}, {"desc": "192", "fix": "193"}, {"desc": "194", "fix": "195"}, {"desc": "194", "fix": "196"}, {"desc": "197", "fix": "198"}, "Update the dependencies array to be: [loadDashboardData]", {"range": "199", "text": "200"}, "Update the dependencies array to be: [isOpen, groupId, loadGroupDetails, isOwner, checkCanLeave, loadJoinRequests]", {"range": "201", "text": "202"}, "Update the dependencies array to be: [calculateActualTheme, theme]", {"range": "203", "text": "204"}, {"range": "205", "text": "204"}, "Update the dependencies array to be: [user, webSocketReturn, webSocketReturn.connect, webSocketReturn.disconnect]", {"range": "206", "text": "207"}, [3021, 3023], "[loadDashboardData]", [2691, 2708], "[isOpen, groupId, loadGroupDetails, isOwner, checkCanLeave, loadJoinRequests]", [2043, 2050], "[calculateActualTheme, theme]", [2755, 2762], [5617, 5676], "[user, webSocketReturn, webSocketReturn.connect, webSocketReturn.disconnect]"]