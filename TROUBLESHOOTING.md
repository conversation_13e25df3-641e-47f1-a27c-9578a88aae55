# Troubleshooting Guide

This guide helps resolve common issues when running the expense tracking application.

## Quick Fix Commands

### 1. Install All Dependencies

**Backend:**
```bash
cd expense-app
pip install -r requirements.txt
```

**Frontend:**
```bash
cd expense-frontend
npm install
```

### 2. Run Database Migrations

```bash
cd expense-app
python run_migrations.py
```

Or manually:
```bash
cd expense-app
alembic upgrade head
```

### 3. Initialize System Categories

Start the backend server and make a POST request to:
```
POST http://localhost:8000/categorization/initialize-system-categories
```

## Common Issues and Solutions

### Backend Issues

#### ImportError: cannot import name 'get_password_hash'
**Solution:** The security module has been updated with the missing function. Restart the server.

#### Database connection errors
**Solution:** 
1. Ensure PostgreSQL is running
2. Update DATABASE_URL in `.env` file
3. Run migrations: `python run_migrations.py`

#### Alembic migration errors
**Solution:**
```bash
cd expense-app
alembic stamp head  # Mark current state as up-to-date
alembic upgrade head  # Apply any pending migrations
```

### Frontend Issues

#### TypeScript compilation errors about User type
**Solution:** The User interface has been updated in `src/types/index.ts` with all new profile fields.

#### toast.info is not a function
**Solution:** Fixed by replacing `toast.info()` with `toast()` with custom icon.

#### Module not found errors
**Solution:**
```bash
cd expense-frontend
rm -rf node_modules package-lock.json
npm install
```

### Database Issues

#### Table doesn't exist errors
**Solution:** Run all migrations in order:
```bash
cd expense-app
alembic upgrade 001  # Initial tables
alembic upgrade 002  # Settlements
alembic upgrade 003  # User profile fields
alembic upgrade 004  # Recurring expenses
alembic upgrade 005  # Categorization
```

#### Foreign key constraint errors
**Solution:** Ensure migrations are run in the correct order and all referenced tables exist.

## Development Setup

### 1. Environment Variables

**Backend (.env):**
```env
DATABASE_URL=postgresql://username:password@localhost/expense_tracker
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALLOWED_ORIGINS=http://localhost:3000
```

**Frontend (.env):**
```env
REACT_APP_API_URL=http://localhost:8000
```

### 2. Starting the Application

**Terminal 1 - Backend:**
```bash
cd expense-app
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**Terminal 2 - Frontend:**
```bash
cd expense-frontend
npm start
```

### 3. Database Setup

1. Create PostgreSQL database:
```sql
CREATE DATABASE expense_tracker;
```

2. Run migrations:
```bash
cd expense-app
python run_migrations.py
```

3. Initialize system categories:
```bash
curl -X POST http://localhost:8000/categorization/initialize-system-categories \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Feature Testing

### 1. User Profile Management
- Register/login with new user
- Upload avatar image
- Update profile information
- Change notification preferences

### 2. Recurring Expenses
- Create recurring expense template
- View execution history
- Manually execute template

### 3. Categorization
- Create custom categories
- Create tags
- Categorize expenses
- View analytics

### 4. Real-time Notifications
- Open multiple browser tabs
- Create expense in one tab
- Verify notification appears in other tabs

### 5. Bulk Operations
- Select multiple expenses
- Bulk approve/reject
- Bulk delete

## API Testing

Use tools like Postman or curl to test API endpoints:

```bash
# Get user profile
curl -X GET http://localhost:8000/profile/me \
  -H "Authorization: Bearer YOUR_TOKEN"

# Create category
curl -X POST http://localhost:8000/categorization/categories \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name": "Test Category", "color": "#FF0000"}'

# Create recurring template
curl -X POST http://localhost:8000/recurring-expenses/templates \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Monthly Rent",
    "description": "Office rent",
    "amount": 50000,
    "frequency": "monthly",
    "start_date": "2024-01-01T00:00:00Z",
    "group_id": 1
  }'
```

## Performance Optimization

### Database Indexes
Ensure these indexes exist for better performance:
```sql
CREATE INDEX idx_expenses_category_id ON expenses(category_id);
CREATE INDEX idx_expenses_created_at ON expenses(created_at);
CREATE INDEX idx_recurring_templates_next_execution ON recurring_expense_templates(next_execution);
```

### Frontend Optimization
- Use React.memo for expensive components
- Implement virtual scrolling for large lists
- Use debounced search inputs

## Monitoring and Logging

### Backend Logging
Add to `app/main.py`:
```python
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
```

### Frontend Error Boundary
Implement error boundaries to catch React errors gracefully.

## Security Considerations

1. **API Keys:** Store Groq API keys securely
2. **CORS:** Configure proper CORS origins
3. **Rate Limiting:** Implement rate limiting for API endpoints
4. **Input Validation:** Validate all user inputs
5. **File Uploads:** Validate file types and sizes for avatar uploads

## Getting Help

If you encounter issues not covered here:

1. Check the browser console for JavaScript errors
2. Check the backend logs for Python errors
3. Verify all environment variables are set correctly
4. Ensure all dependencies are installed
5. Try restarting both frontend and backend servers

For database issues, check PostgreSQL logs and ensure the database user has proper permissions.
