export interface User {
  id: number;
  email: string;
  groq_api_key?: string;

  // Profile fields
  full_name?: string;
  avatar_url?: string;
  phone?: string;
  timezone?: string;
  currency?: string;
  language?: string;

  // Notification preferences
  email_notifications?: boolean;
  expense_notifications?: boolean;
  approval_notifications?: boolean;
  settlement_notifications?: boolean;

  // Timestamps
  created_at?: string;
  updated_at?: string;
}

export interface Group {
  id: number;
  name: string;
  creator_id: number;
  members: User[];
}

export interface Expense {
  id: number;
  payer_id: number;
  group_id: number;
  total: number | string; // Backend may send as string due to Decimal serialization
  description: string;
  created_at?: string;
  shares: Share[];
}

export interface Share {
  id?: number;
  user_id: number;
  amount: number | string; // Backend may send as string due to Decimal serialization
  paid: boolean;
}

export interface Balance {
  user_id: number;
  email: string;
  amount: number | string; // Backend may send as string due to Decimal serialization
}

export interface SettlementRequest {
  target_user_id: number;
  amount: number;
}

export interface SettlementResult {
  total_paid: number;
  target_user_email: string;
  settlements: {
    share_id: number;
    paid_amount: number;
    remaining_amount: number;
  }[];
  message: string;
}

export interface NLPRequest {
  message: string;
}

export interface NLPResponse {
  success: boolean;
  intent: string;
  message: string;
  result?: any;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  groq_api_key: string;
}

export interface CreateExpenseRequest {
  group_id: number;
  total: number;
  description: string;
}

export interface CreateGroupRequest {
  name: string;
}

export interface JoinGroupRequest {
  group_id: number;
}

export interface ApiError {
  detail: string;
}
