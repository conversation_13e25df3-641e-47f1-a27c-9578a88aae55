{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add auth token to requests\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Handle auth errors\napi.interceptors.response.use(response => response, error => {\n  var _error$response, _error$response2;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 403) {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// Auth API\nexport const authAPI = {\n  login: data => api.post('/auth/login', data),\n  register: data => api.post('/auth/register', data),\n  getCurrentUser: () => api.get('/auth/me')\n};\n\n// Groups API\nexport const groupsAPI = {\n  getMyGroups: () => api.get('/groups/my-groups'),\n  createGroup: data => api.post('/groups/create', data),\n  joinGroup: data => api.post('/groups/join', data)\n};\n\n// Enhanced Group Management API\nexport const groupManagementAPI = {\n  // Group details and management\n  getGroupDetails: groupId => api.get(`/group-management/groups/${groupId}/details`),\n  updateGroup: (groupId, data) => api.put(`/group-management/groups/${groupId}`, data),\n  // Member management\n  removeMember: (groupId, userId) => api.delete(`/group-management/groups/${groupId}/members/${userId}`),\n  leaveGroup: groupId => api.post(`/group-management/groups/${groupId}/leave`),\n  transferOwnership: (groupId, newOwnerId) => api.post(`/group-management/groups/${groupId}/transfer-ownership`, {\n    new_owner_id: newOwnerId\n  }),\n  checkCanLeave: groupId => api.get(`/group-management/groups/${groupId}/can-leave`),\n  // Join request management\n  requestToJoin: (groupId, message) => api.post(`/group-management/groups/${groupId}/join-request`, {\n    message\n  }),\n  getPendingJoinRequests: groupId => api.get(`/group-management/groups/join-requests${groupId ? `?group_id=${groupId}` : ''}`),\n  processJoinRequest: (requestId, approved) => api.post(`/group-management/groups/join-requests/${requestId}/process`, {\n    approved\n  })\n};\n\n// Settings API\nexport const settingsAPI = {\n  // User profile settings\n  updateProfile: data => api.put('/settings/profile', data),\n  changePassword: data => api.put('/settings/password', data),\n  updateGroqApiKey: data => api.put('/settings/groq-api-key', data),\n  // Notification preferences\n  getNotificationPreferences: () => api.get('/settings/notifications'),\n  updateNotificationPreferences: data => api.put('/settings/notifications', data),\n  // Theme and display settings\n  getDisplaySettings: () => api.get('/settings/display'),\n  updateDisplaySettings: data => api.put('/settings/display', data),\n  // Data export/import\n  exportData: () => api.get('/settings/export-data'),\n  // Account management\n  deleteAccount: password => api.delete('/settings/account', {\n    data: {\n      password\n    }\n  })\n};\n\n// Expenses API\nexport const expensesAPI = {\n  createExpense: data => api.post('/expenses/create', data),\n  getBalances: () => api.get('/expenses/balances'),\n  getDebts: () => api.get('/expenses/debts'),\n  getHistory: (groupId, limit) => api.get('/expenses/history', {\n    params: {\n      group_id: groupId,\n      limit\n    }\n  }),\n  settleDebt: data => api.post('/expenses/settle', data),\n  // Bulk operations\n  bulkApprove: data => api.post('/expenses/bulk/approve', data),\n  bulkDelete: data => api.post('/expenses/bulk/delete', data),\n  bulkCategorize: data => api.post('/expenses/bulk/categorize', data),\n  // Advanced filtering\n  getFilteredExpenses: filters => api.get('/expenses/filter', {\n    params: filters\n  }),\n  // Export functionality\n  getExportSummary: filters => api.get('/expenses/export/summary', {\n    params: filters\n  }),\n  exportCSV: filters => api.get('/expenses/export/csv', {\n    params: filters,\n    responseType: 'blob'\n  }),\n  exportPDF: filters => api.get('/expenses/export/pdf', {\n    params: filters,\n    responseType: 'blob'\n  })\n};\n\n// Profile API\nexport const profileAPI = {\n  getProfile: () => api.get('/profile/me'),\n  updateProfile: data => api.put('/profile/update', data),\n  updateNotifications: data => api.put('/profile/notifications', data),\n  uploadAvatar: formData => api.post('/profile/avatar/upload', formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  }),\n  deleteAvatar: () => api.delete('/profile/avatar'),\n  changePassword: data => api.put('/profile/password', data),\n  updateGroqApiKey: data => api.put('/profile/groq-api-key', data),\n  getStatistics: () => api.get('/profile/statistics'),\n  deleteAccount: data => api.delete('/profile/account', {\n    data\n  })\n};\n\n// Recurring Expenses API\nexport const recurringExpensesAPI = {\n  createTemplate: data => api.post('/recurring-expenses/templates', data),\n  getTemplates: () => api.get('/recurring-expenses/templates'),\n  getTemplateDetails: templateId => api.get(`/recurring-expenses/templates/${templateId}`),\n  updateTemplate: (templateId, data) => api.put(`/recurring-expenses/templates/${templateId}`, data),\n  deleteTemplate: templateId => api.delete(`/recurring-expenses/templates/${templateId}`),\n  executeTemplate: templateId => api.post(`/recurring-expenses/templates/${templateId}/execute`),\n  getDueTemplates: () => api.get('/recurring-expenses/due-templates'),\n  processDueTemplates: () => api.post('/recurring-expenses/process-due')\n};\n\n// Categorization API\nexport const categorizationAPI = {\n  // Categories\n  createCategory: data => api.post('/categorization/categories', data),\n  getCategories: () => api.get('/categorization/categories'),\n  updateCategory: (categoryId, data) => api.put(`/categorization/categories/${categoryId}`, data),\n  deleteCategory: categoryId => api.delete(`/categorization/categories/${categoryId}`),\n  // Tags\n  createTag: data => api.post('/categorization/tags', data),\n  getTags: () => api.get('/categorization/tags'),\n  updateTag: (tagId, data) => api.put(`/categorization/tags/${tagId}`, data),\n  deleteTag: tagId => api.delete(`/categorization/tags/${tagId}`),\n  // Expense categorization\n  categorizeExpense: (expenseId, categoryId, tagIds) => api.put(`/categorization/expenses/${expenseId}/categorize`, null, {\n    params: {\n      category_id: categoryId,\n      tag_ids: tagIds\n    }\n  }),\n  // Analytics\n  getAnalytics: groupId => api.get('/categorization/analytics', {\n    params: {\n      group_id: groupId\n    }\n  }),\n  // System categories\n  initializeSystemCategories: () => api.post('/categorization/initialize-system-categories')\n};\n\n// Enhanced Notifications API\nexport const notificationsAPI = {\n  // Get notifications\n  getNotifications: (limit = 50, offset = 0, status, category) => api.get('/notifications/', {\n    params: {\n      limit,\n      offset,\n      status,\n      category\n    }\n  }),\n  getRecentNotifications: (limit = 10) => api.get('/notifications/recent', {\n    params: {\n      limit\n    }\n  }),\n  getSummary: () => api.get('/notifications/summary'),\n  getUnreadCount: () => api.get('/notifications/unread/count'),\n  // Mark notifications as read\n  markAsRead: notificationId => api.put(`/notifications/${notificationId}/read`),\n  markBulkAsRead: notificationIds => api.put('/notifications/bulk/read', {\n    notification_ids: notificationIds,\n    status: 'read'\n  }),\n  // Notification preferences\n  getPreferences: () => api.get('/notifications/preferences'),\n  updatePreferences: preferences => api.put('/notifications/preferences', preferences),\n  // Templates and admin functions\n  getTemplates: () => api.get('/notifications/templates'),\n  initializeTemplates: () => api.post('/notifications/templates/initialize'),\n  // Cleanup\n  cleanupOldNotifications: (days = 30) => api.delete('/notifications/cleanup', {\n    params: {\n      days\n    }\n  })\n};\n\n// NLP API\nexport const nlpAPI = {\n  interpret: data => api.post('/nlp/interpret', data),\n  getExamples: () => api.get('/nlp/examples')\n};\n\n// Approvals API\nexport const approvalsAPI = {\n  getPendingApprovals: () => api.get('/approvals/pending'),\n  approveExpense: (expenseId, approved) => api.post(`/approvals/approve/${expenseId}?approved=${approved}`),\n  bulkApproveExpenses: (expenseIds, approved) => api.post('/approvals/bulk-approve', {\n    expense_ids: expenseIds,\n    approved\n  }),\n  getExpenseApprovals: expenseId => api.get(`/approvals/expense/${expenseId}/approvals`),\n  // Settlement confirmations\n  createSettlement: data => api.post('/approvals/settlements', data),\n  getPendingSettlements: () => api.get('/approvals/settlements/pending'),\n  confirmSettlement: (settlementId, confirmed) => api.post(`/approvals/settlements/${settlementId}/confirm?confirmed=${confirmed}`),\n  getSettlementHistory: status => api.get('/approvals/settlements/history', {\n    params: {\n      status\n    }\n  })\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "response", "error", "_error$response", "_error$response2", "status", "removeItem", "window", "location", "href", "Promise", "reject", "authAPI", "login", "data", "post", "register", "getCurrentUser", "get", "groupsAPI", "getMyGroups", "createGroup", "joinGroup", "groupManagementAPI", "getGroupDetails", "groupId", "updateGroup", "put", "removeMember", "userId", "delete", "leaveGroup", "transferOwnership", "newOwnerId", "new_owner_id", "checkCanLeave", "requestToJoin", "message", "getPendingJoinRequests", "processJoinRequest", "requestId", "approved", "settingsAPI", "updateProfile", "changePassword", "updateGroqApiKey", "getNotificationPreferences", "updateNotificationPreferences", "getDisplaySettings", "updateDisplaySettings", "exportData", "deleteAccount", "password", "expensesAPI", "createExpense", "getBalances", "getDebts", "getHistory", "limit", "params", "group_id", "settleDebt", "bulkApprove", "bulkDelete", "bulkCategorize", "getFilteredExpenses", "filters", "getExportSummary", "exportCSV", "responseType", "exportPDF", "profileAPI", "getProfile", "updateNotifications", "uploadAvatar", "formData", "deleteAvatar", "getStatistics", "recurringExpensesAPI", "createTemplate", "getTemplates", "getTemplateDetails", "templateId", "updateTemplate", "deleteTemplate", "executeTemplate", "getDueTemplates", "processDueTemplates", "categorizationAPI", "createCategory", "getCategories", "updateCategory", "categoryId", "deleteCategory", "createTag", "getTags", "updateTag", "tagId", "deleteTag", "categorizeExpense", "expenseId", "tagIds", "category_id", "tag_ids", "getAnalytics", "initializeSystemCategories", "notificationsAPI", "getNotifications", "offset", "category", "getRecentNotifications", "getSummary", "getUnreadCount", "mark<PERSON><PERSON><PERSON>", "notificationId", "markBulkAsRead", "notificationIds", "notification_ids", "getPreferences", "updatePreferences", "preferences", "initializeTemplates", "cleanupOldNotifications", "days", "nlpAPI", "interpret", "getExamples", "approvalsAPI", "getPendingApprovals", "approveExpense", "bulkApproveExpenses", "expenseIds", "expense_ids", "getExpenseApprovals", "createSettlement", "getPendingSettlements", "confirmSettlement", "settlementId", "confirmed", "getSettlementHistory"], "sources": ["C:/Users/<USER>/Documents/Folio3/expense-frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosResponse } from 'axios';\nimport {\n  User,\n  Group,\n  Expense,\n  Balance,\n  AuthResponse,\n  LoginRequest,\n  RegisterRequest,\n  CreateExpenseRequest,\n  CreateGroupRequest,\n  JoinGroupRequest,\n  SettlementRequest,\n  SettlementResult,\n  NLPRequest,\n  NLPResponse,\n} from '../types';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\n// Create axios instance\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add auth token to requests\napi.interceptors.request.use((config) => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\n\n// Handle auth errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401 || error.response?.status === 403) {\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  login: (data: LoginRequest): Promise<AxiosResponse<AuthResponse>> =>\n    api.post('/auth/login', data),\n  \n  register: (data: RegisterRequest): Promise<AxiosResponse<User>> =>\n    api.post('/auth/register', data),\n  \n  getCurrentUser: (): Promise<AxiosResponse<User>> =>\n    api.get('/auth/me'),\n};\n\n// Groups API\nexport const groupsAPI = {\n  getMyGroups: (): Promise<AxiosResponse<Group[]>> =>\n    api.get('/groups/my-groups'),\n  \n  createGroup: (data: CreateGroupRequest): Promise<AxiosResponse<Group>> =>\n    api.post('/groups/create', data),\n  \n  joinGroup: (data: JoinGroupRequest): Promise<AxiosResponse<Group>> =>\n    api.post('/groups/join', data),\n};\n\n// Enhanced Group Management API\nexport const groupManagementAPI = {\n  // Group details and management\n  getGroupDetails: (groupId: number): Promise<AxiosResponse<any>> =>\n    api.get(`/group-management/groups/${groupId}/details`),\n\n  updateGroup: (groupId: number, data: { name?: string; description?: string }): Promise<AxiosResponse<any>> =>\n    api.put(`/group-management/groups/${groupId}`, data),\n\n  // Member management\n  removeMember: (groupId: number, userId: number): Promise<AxiosResponse<any>> =>\n    api.delete(`/group-management/groups/${groupId}/members/${userId}`),\n\n  leaveGroup: (groupId: number): Promise<AxiosResponse<any>> =>\n    api.post(`/group-management/groups/${groupId}/leave`),\n\n  transferOwnership: (groupId: number, newOwnerId: number): Promise<AxiosResponse<any>> =>\n    api.post(`/group-management/groups/${groupId}/transfer-ownership`, { new_owner_id: newOwnerId }),\n\n  checkCanLeave: (groupId: number): Promise<AxiosResponse<any>> =>\n    api.get(`/group-management/groups/${groupId}/can-leave`),\n\n  // Join request management\n  requestToJoin: (groupId: number, message?: string): Promise<AxiosResponse<any>> =>\n    api.post(`/group-management/groups/${groupId}/join-request`, { message }),\n\n  getPendingJoinRequests: (groupId?: number): Promise<AxiosResponse<any[]>> =>\n    api.get(`/group-management/groups/join-requests${groupId ? `?group_id=${groupId}` : ''}`),\n\n  processJoinRequest: (requestId: number, approved: boolean): Promise<AxiosResponse<any>> =>\n    api.post(`/group-management/groups/join-requests/${requestId}/process`, { approved }),\n};\n\n// Settings API\nexport const settingsAPI = {\n  // User profile settings\n  updateProfile: (data: { name?: string; email?: string }): Promise<AxiosResponse<any>> =>\n    api.put('/settings/profile', data),\n\n  changePassword: (data: { current_password: string; new_password: string }): Promise<AxiosResponse<any>> =>\n    api.put('/settings/password', data),\n\n  updateGroqApiKey: (data: { groq_api_key: string }): Promise<AxiosResponse<any>> =>\n    api.put('/settings/groq-api-key', data),\n\n  // Notification preferences\n  getNotificationPreferences: (): Promise<AxiosResponse<any>> =>\n    api.get('/settings/notifications'),\n\n  updateNotificationPreferences: (data: any): Promise<AxiosResponse<any>> =>\n    api.put('/settings/notifications', data),\n\n  // Theme and display settings\n  getDisplaySettings: (): Promise<AxiosResponse<any>> =>\n    api.get('/settings/display'),\n\n  updateDisplaySettings: (data: { theme?: string; currency?: string; language?: string }): Promise<AxiosResponse<any>> =>\n    api.put('/settings/display', data),\n\n  // Data export/import\n  exportData: (): Promise<AxiosResponse<any>> =>\n    api.get('/settings/export-data'),\n\n  // Account management\n  deleteAccount: (password: string): Promise<AxiosResponse<any>> =>\n    api.delete('/settings/account', { data: { password } }),\n};\n\n// Expenses API\nexport const expensesAPI = {\n  createExpense: (data: CreateExpenseRequest): Promise<AxiosResponse<Expense>> =>\n    api.post('/expenses/create', data),\n\n  getBalances: (): Promise<AxiosResponse<Balance[]>> =>\n    api.get('/expenses/balances'),\n\n  getDebts: (): Promise<AxiosResponse<Balance[]>> =>\n    api.get('/expenses/debts'),\n\n  getHistory: (groupId?: number, limit?: number): Promise<AxiosResponse<Expense[]>> =>\n    api.get('/expenses/history', { params: { group_id: groupId, limit } }),\n\n  settleDebt: (data: SettlementRequest): Promise<AxiosResponse<SettlementResult>> =>\n    api.post('/expenses/settle', data),\n\n  // Bulk operations\n  bulkApprove: (data: { expense_ids: number[]; approved: boolean }): Promise<AxiosResponse<any>> =>\n    api.post('/expenses/bulk/approve', data),\n\n  bulkDelete: (data: { expense_ids: number[] }): Promise<AxiosResponse<any>> =>\n    api.post('/expenses/bulk/delete', data),\n\n  bulkCategorize: (data: { expense_ids: number[]; category: string }): Promise<AxiosResponse<any>> =>\n    api.post('/expenses/bulk/categorize', data),\n\n  // Advanced filtering\n  getFilteredExpenses: (filters: any): Promise<AxiosResponse<any>> =>\n    api.get('/expenses/filter', { params: filters }),\n\n  // Export functionality\n  getExportSummary: (filters: any): Promise<AxiosResponse<any>> =>\n    api.get('/expenses/export/summary', { params: filters }),\n\n  exportCSV: (filters: any): Promise<AxiosResponse<any>> =>\n    api.get('/expenses/export/csv', { params: filters, responseType: 'blob' }),\n\n  exportPDF: (filters: any): Promise<AxiosResponse<any>> =>\n    api.get('/expenses/export/pdf', { params: filters, responseType: 'blob' }),\n};\n\n// Profile API\nexport const profileAPI = {\n  getProfile: (): Promise<AxiosResponse<any>> =>\n    api.get('/profile/me'),\n\n  updateProfile: (data: any): Promise<AxiosResponse<any>> =>\n    api.put('/profile/update', data),\n\n  updateNotifications: (data: any): Promise<AxiosResponse<any>> =>\n    api.put('/profile/notifications', data),\n\n  uploadAvatar: (formData: FormData): Promise<AxiosResponse<any>> =>\n    api.post('/profile/avatar/upload', formData, {\n      headers: { 'Content-Type': 'multipart/form-data' }\n    }),\n\n  deleteAvatar: (): Promise<AxiosResponse<any>> =>\n    api.delete('/profile/avatar'),\n\n  changePassword: (data: { current_password: string; new_password: string }): Promise<AxiosResponse<any>> =>\n    api.put('/profile/password', data),\n\n  updateGroqApiKey: (data: { groq_api_key: string }): Promise<AxiosResponse<any>> =>\n    api.put('/profile/groq-api-key', data),\n\n  getStatistics: (): Promise<AxiosResponse<any>> =>\n    api.get('/profile/statistics'),\n\n  deleteAccount: (data: { password: string }): Promise<AxiosResponse<any>> =>\n    api.delete('/profile/account', { data }),\n};\n\n// Recurring Expenses API\nexport const recurringExpensesAPI = {\n  createTemplate: (data: any): Promise<AxiosResponse<any>> =>\n    api.post('/recurring-expenses/templates', data),\n\n  getTemplates: (): Promise<AxiosResponse<any>> =>\n    api.get('/recurring-expenses/templates'),\n\n  getTemplateDetails: (templateId: number): Promise<AxiosResponse<any>> =>\n    api.get(`/recurring-expenses/templates/${templateId}`),\n\n  updateTemplate: (templateId: number, data: any): Promise<AxiosResponse<any>> =>\n    api.put(`/recurring-expenses/templates/${templateId}`, data),\n\n  deleteTemplate: (templateId: number): Promise<AxiosResponse<any>> =>\n    api.delete(`/recurring-expenses/templates/${templateId}`),\n\n  executeTemplate: (templateId: number): Promise<AxiosResponse<any>> =>\n    api.post(`/recurring-expenses/templates/${templateId}/execute`),\n\n  getDueTemplates: (): Promise<AxiosResponse<any>> =>\n    api.get('/recurring-expenses/due-templates'),\n\n  processDueTemplates: (): Promise<AxiosResponse<any>> =>\n    api.post('/recurring-expenses/process-due'),\n};\n\n// Categorization API\nexport const categorizationAPI = {\n  // Categories\n  createCategory: (data: any): Promise<AxiosResponse<any>> =>\n    api.post('/categorization/categories', data),\n\n  getCategories: (): Promise<AxiosResponse<any>> =>\n    api.get('/categorization/categories'),\n\n  updateCategory: (categoryId: number, data: any): Promise<AxiosResponse<any>> =>\n    api.put(`/categorization/categories/${categoryId}`, data),\n\n  deleteCategory: (categoryId: number): Promise<AxiosResponse<any>> =>\n    api.delete(`/categorization/categories/${categoryId}`),\n\n  // Tags\n  createTag: (data: any): Promise<AxiosResponse<any>> =>\n    api.post('/categorization/tags', data),\n\n  getTags: (): Promise<AxiosResponse<any>> =>\n    api.get('/categorization/tags'),\n\n  updateTag: (tagId: number, data: any): Promise<AxiosResponse<any>> =>\n    api.put(`/categorization/tags/${tagId}`, data),\n\n  deleteTag: (tagId: number): Promise<AxiosResponse<any>> =>\n    api.delete(`/categorization/tags/${tagId}`),\n\n  // Expense categorization\n  categorizeExpense: (expenseId: number, categoryId?: number, tagIds?: number[]): Promise<AxiosResponse<any>> =>\n    api.put(`/categorization/expenses/${expenseId}/categorize`, null, {\n      params: { category_id: categoryId, tag_ids: tagIds }\n    }),\n\n  // Analytics\n  getAnalytics: (groupId?: number): Promise<AxiosResponse<any>> =>\n    api.get('/categorization/analytics', { params: { group_id: groupId } }),\n\n  // System categories\n  initializeSystemCategories: (): Promise<AxiosResponse<any>> =>\n    api.post('/categorization/initialize-system-categories'),\n};\n\n// Enhanced Notifications API\nexport const notificationsAPI = {\n  // Get notifications\n  getNotifications: (limit: number = 50, offset: number = 0, status?: string, category?: string): Promise<AxiosResponse<any>> =>\n    api.get('/notifications/', { params: { limit, offset, status, category } }),\n\n  getRecentNotifications: (limit: number = 10): Promise<AxiosResponse<any>> =>\n    api.get('/notifications/recent', { params: { limit } }),\n\n  getSummary: (): Promise<AxiosResponse<any>> =>\n    api.get('/notifications/summary'),\n\n  getUnreadCount: (): Promise<AxiosResponse<any>> =>\n    api.get('/notifications/unread/count'),\n\n  // Mark notifications as read\n  markAsRead: (notificationId: number): Promise<AxiosResponse<any>> =>\n    api.put(`/notifications/${notificationId}/read`),\n\n  markBulkAsRead: (notificationIds: number[]): Promise<AxiosResponse<any>> =>\n    api.put('/notifications/bulk/read', { notification_ids: notificationIds, status: 'read' }),\n\n  // Notification preferences\n  getPreferences: (): Promise<AxiosResponse<any>> =>\n    api.get('/notifications/preferences'),\n\n  updatePreferences: (preferences: any[]): Promise<AxiosResponse<any>> =>\n    api.put('/notifications/preferences', preferences),\n\n  // Templates and admin functions\n  getTemplates: (): Promise<AxiosResponse<any>> =>\n    api.get('/notifications/templates'),\n\n  initializeTemplates: (): Promise<AxiosResponse<any>> =>\n    api.post('/notifications/templates/initialize'),\n\n  // Cleanup\n  cleanupOldNotifications: (days: number = 30): Promise<AxiosResponse<any>> =>\n    api.delete('/notifications/cleanup', { params: { days } }),\n};\n\n// NLP API\nexport const nlpAPI = {\n  interpret: (data: NLPRequest): Promise<AxiosResponse<NLPResponse>> =>\n    api.post('/nlp/interpret', data),\n\n  getExamples: (): Promise<AxiosResponse<any>> =>\n    api.get('/nlp/examples'),\n};\n\n// Approvals API\nexport const approvalsAPI = {\n  getPendingApprovals: (): Promise<AxiosResponse<any[]>> =>\n    api.get('/approvals/pending'),\n\n  approveExpense: (expenseId: number, approved: boolean): Promise<AxiosResponse<any>> =>\n    api.post(`/approvals/approve/${expenseId}?approved=${approved}`),\n\n  bulkApproveExpenses: (expenseIds: number[], approved: boolean): Promise<AxiosResponse<any>> =>\n    api.post('/approvals/bulk-approve', { expense_ids: expenseIds, approved }),\n\n  getExpenseApprovals: (expenseId: number): Promise<AxiosResponse<any[]>> =>\n    api.get(`/approvals/expense/${expenseId}/approvals`),\n\n  // Settlement confirmations\n  createSettlement: (data: { recipient_id: number; amount: number; description?: string }): Promise<AxiosResponse<any>> =>\n    api.post('/approvals/settlements', data),\n\n  getPendingSettlements: (): Promise<AxiosResponse<any[]>> =>\n    api.get('/approvals/settlements/pending'),\n\n  confirmSettlement: (settlementId: number, confirmed: boolean): Promise<AxiosResponse<any>> =>\n    api.post(`/approvals/settlements/${settlementId}/confirm?confirmed=${confirmed}`),\n\n  getSettlementHistory: (status?: string): Promise<AxiosResponse<any[]>> =>\n    api.get('/approvals/settlements/history', { params: { status } }),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAyB,OAAO;AAkB5C,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAE7E;AACA,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACvC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACI,YAAY,CAACQ,QAAQ,CAACN,GAAG,CAC1BM,QAAQ,IAAKA,QAAQ,EACrBC,KAAK,IAAK;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EACT,IAAI,EAAAD,eAAA,GAAAD,KAAK,CAACD,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,EAAAD,gBAAA,GAAAF,KAAK,CAACD,QAAQ,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IACpEP,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;IAChCR,YAAY,CAACQ,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOC,OAAO,CAACC,MAAM,CAACT,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,OAAO,GAAG;EACrBC,KAAK,EAAGC,IAAkB,IACxBzB,GAAG,CAAC0B,IAAI,CAAC,aAAa,EAAED,IAAI,CAAC;EAE/BE,QAAQ,EAAGF,IAAqB,IAC9BzB,GAAG,CAAC0B,IAAI,CAAC,gBAAgB,EAAED,IAAI,CAAC;EAElCG,cAAc,EAAEA,CAAA,KACd5B,GAAG,CAAC6B,GAAG,CAAC,UAAU;AACtB,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,WAAW,EAAEA,CAAA,KACX/B,GAAG,CAAC6B,GAAG,CAAC,mBAAmB,CAAC;EAE9BG,WAAW,EAAGP,IAAwB,IACpCzB,GAAG,CAAC0B,IAAI,CAAC,gBAAgB,EAAED,IAAI,CAAC;EAElCQ,SAAS,EAAGR,IAAsB,IAChCzB,GAAG,CAAC0B,IAAI,CAAC,cAAc,EAAED,IAAI;AACjC,CAAC;;AAED;AACA,OAAO,MAAMS,kBAAkB,GAAG;EAChC;EACAC,eAAe,EAAGC,OAAe,IAC/BpC,GAAG,CAAC6B,GAAG,CAAC,4BAA4BO,OAAO,UAAU,CAAC;EAExDC,WAAW,EAAEA,CAACD,OAAe,EAAEX,IAA6C,KAC1EzB,GAAG,CAACsC,GAAG,CAAC,4BAA4BF,OAAO,EAAE,EAAEX,IAAI,CAAC;EAEtD;EACAc,YAAY,EAAEA,CAACH,OAAe,EAAEI,MAAc,KAC5CxC,GAAG,CAACyC,MAAM,CAAC,4BAA4BL,OAAO,YAAYI,MAAM,EAAE,CAAC;EAErEE,UAAU,EAAGN,OAAe,IAC1BpC,GAAG,CAAC0B,IAAI,CAAC,4BAA4BU,OAAO,QAAQ,CAAC;EAEvDO,iBAAiB,EAAEA,CAACP,OAAe,EAAEQ,UAAkB,KACrD5C,GAAG,CAAC0B,IAAI,CAAC,4BAA4BU,OAAO,qBAAqB,EAAE;IAAES,YAAY,EAAED;EAAW,CAAC,CAAC;EAElGE,aAAa,EAAGV,OAAe,IAC7BpC,GAAG,CAAC6B,GAAG,CAAC,4BAA4BO,OAAO,YAAY,CAAC;EAE1D;EACAW,aAAa,EAAEA,CAACX,OAAe,EAAEY,OAAgB,KAC/ChD,GAAG,CAAC0B,IAAI,CAAC,4BAA4BU,OAAO,eAAe,EAAE;IAAEY;EAAQ,CAAC,CAAC;EAE3EC,sBAAsB,EAAGb,OAAgB,IACvCpC,GAAG,CAAC6B,GAAG,CAAC,yCAAyCO,OAAO,GAAG,aAAaA,OAAO,EAAE,GAAG,EAAE,EAAE,CAAC;EAE3Fc,kBAAkB,EAAEA,CAACC,SAAiB,EAAEC,QAAiB,KACvDpD,GAAG,CAAC0B,IAAI,CAAC,0CAA0CyB,SAAS,UAAU,EAAE;IAAEC;EAAS,CAAC;AACxF,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzB;EACAC,aAAa,EAAG7B,IAAuC,IACrDzB,GAAG,CAACsC,GAAG,CAAC,mBAAmB,EAAEb,IAAI,CAAC;EAEpC8B,cAAc,EAAG9B,IAAwD,IACvEzB,GAAG,CAACsC,GAAG,CAAC,oBAAoB,EAAEb,IAAI,CAAC;EAErC+B,gBAAgB,EAAG/B,IAA8B,IAC/CzB,GAAG,CAACsC,GAAG,CAAC,wBAAwB,EAAEb,IAAI,CAAC;EAEzC;EACAgC,0BAA0B,EAAEA,CAAA,KAC1BzD,GAAG,CAAC6B,GAAG,CAAC,yBAAyB,CAAC;EAEpC6B,6BAA6B,EAAGjC,IAAS,IACvCzB,GAAG,CAACsC,GAAG,CAAC,yBAAyB,EAAEb,IAAI,CAAC;EAE1C;EACAkC,kBAAkB,EAAEA,CAAA,KAClB3D,GAAG,CAAC6B,GAAG,CAAC,mBAAmB,CAAC;EAE9B+B,qBAAqB,EAAGnC,IAA8D,IACpFzB,GAAG,CAACsC,GAAG,CAAC,mBAAmB,EAAEb,IAAI,CAAC;EAEpC;EACAoC,UAAU,EAAEA,CAAA,KACV7D,GAAG,CAAC6B,GAAG,CAAC,uBAAuB,CAAC;EAElC;EACAiC,aAAa,EAAGC,QAAgB,IAC9B/D,GAAG,CAACyC,MAAM,CAAC,mBAAmB,EAAE;IAAEhB,IAAI,EAAE;MAAEsC;IAAS;EAAE,CAAC;AAC1D,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,aAAa,EAAGxC,IAA0B,IACxCzB,GAAG,CAAC0B,IAAI,CAAC,kBAAkB,EAAED,IAAI,CAAC;EAEpCyC,WAAW,EAAEA,CAAA,KACXlE,GAAG,CAAC6B,GAAG,CAAC,oBAAoB,CAAC;EAE/BsC,QAAQ,EAAEA,CAAA,KACRnE,GAAG,CAAC6B,GAAG,CAAC,iBAAiB,CAAC;EAE5BuC,UAAU,EAAEA,CAAChC,OAAgB,EAAEiC,KAAc,KAC3CrE,GAAG,CAAC6B,GAAG,CAAC,mBAAmB,EAAE;IAAEyC,MAAM,EAAE;MAAEC,QAAQ,EAAEnC,OAAO;MAAEiC;IAAM;EAAE,CAAC,CAAC;EAExEG,UAAU,EAAG/C,IAAuB,IAClCzB,GAAG,CAAC0B,IAAI,CAAC,kBAAkB,EAAED,IAAI,CAAC;EAEpC;EACAgD,WAAW,EAAGhD,IAAkD,IAC9DzB,GAAG,CAAC0B,IAAI,CAAC,wBAAwB,EAAED,IAAI,CAAC;EAE1CiD,UAAU,EAAGjD,IAA+B,IAC1CzB,GAAG,CAAC0B,IAAI,CAAC,uBAAuB,EAAED,IAAI,CAAC;EAEzCkD,cAAc,EAAGlD,IAAiD,IAChEzB,GAAG,CAAC0B,IAAI,CAAC,2BAA2B,EAAED,IAAI,CAAC;EAE7C;EACAmD,mBAAmB,EAAGC,OAAY,IAChC7E,GAAG,CAAC6B,GAAG,CAAC,kBAAkB,EAAE;IAAEyC,MAAM,EAAEO;EAAQ,CAAC,CAAC;EAElD;EACAC,gBAAgB,EAAGD,OAAY,IAC7B7E,GAAG,CAAC6B,GAAG,CAAC,0BAA0B,EAAE;IAAEyC,MAAM,EAAEO;EAAQ,CAAC,CAAC;EAE1DE,SAAS,EAAGF,OAAY,IACtB7E,GAAG,CAAC6B,GAAG,CAAC,sBAAsB,EAAE;IAAEyC,MAAM,EAAEO,OAAO;IAAEG,YAAY,EAAE;EAAO,CAAC,CAAC;EAE5EC,SAAS,EAAGJ,OAAY,IACtB7E,GAAG,CAAC6B,GAAG,CAAC,sBAAsB,EAAE;IAAEyC,MAAM,EAAEO,OAAO;IAAEG,YAAY,EAAE;EAAO,CAAC;AAC7E,CAAC;;AAED;AACA,OAAO,MAAME,UAAU,GAAG;EACxBC,UAAU,EAAEA,CAAA,KACVnF,GAAG,CAAC6B,GAAG,CAAC,aAAa,CAAC;EAExByB,aAAa,EAAG7B,IAAS,IACvBzB,GAAG,CAACsC,GAAG,CAAC,iBAAiB,EAAEb,IAAI,CAAC;EAElC2D,mBAAmB,EAAG3D,IAAS,IAC7BzB,GAAG,CAACsC,GAAG,CAAC,wBAAwB,EAAEb,IAAI,CAAC;EAEzC4D,YAAY,EAAGC,QAAkB,IAC/BtF,GAAG,CAAC0B,IAAI,CAAC,wBAAwB,EAAE4D,QAAQ,EAAE;IAC3CnF,OAAO,EAAE;MAAE,cAAc,EAAE;IAAsB;EACnD,CAAC,CAAC;EAEJoF,YAAY,EAAEA,CAAA,KACZvF,GAAG,CAACyC,MAAM,CAAC,iBAAiB,CAAC;EAE/Bc,cAAc,EAAG9B,IAAwD,IACvEzB,GAAG,CAACsC,GAAG,CAAC,mBAAmB,EAAEb,IAAI,CAAC;EAEpC+B,gBAAgB,EAAG/B,IAA8B,IAC/CzB,GAAG,CAACsC,GAAG,CAAC,uBAAuB,EAAEb,IAAI,CAAC;EAExC+D,aAAa,EAAEA,CAAA,KACbxF,GAAG,CAAC6B,GAAG,CAAC,qBAAqB,CAAC;EAEhCiC,aAAa,EAAGrC,IAA0B,IACxCzB,GAAG,CAACyC,MAAM,CAAC,kBAAkB,EAAE;IAAEhB;EAAK,CAAC;AAC3C,CAAC;;AAED;AACA,OAAO,MAAMgE,oBAAoB,GAAG;EAClCC,cAAc,EAAGjE,IAAS,IACxBzB,GAAG,CAAC0B,IAAI,CAAC,+BAA+B,EAAED,IAAI,CAAC;EAEjDkE,YAAY,EAAEA,CAAA,KACZ3F,GAAG,CAAC6B,GAAG,CAAC,+BAA+B,CAAC;EAE1C+D,kBAAkB,EAAGC,UAAkB,IACrC7F,GAAG,CAAC6B,GAAG,CAAC,iCAAiCgE,UAAU,EAAE,CAAC;EAExDC,cAAc,EAAEA,CAACD,UAAkB,EAAEpE,IAAS,KAC5CzB,GAAG,CAACsC,GAAG,CAAC,iCAAiCuD,UAAU,EAAE,EAAEpE,IAAI,CAAC;EAE9DsE,cAAc,EAAGF,UAAkB,IACjC7F,GAAG,CAACyC,MAAM,CAAC,iCAAiCoD,UAAU,EAAE,CAAC;EAE3DG,eAAe,EAAGH,UAAkB,IAClC7F,GAAG,CAAC0B,IAAI,CAAC,iCAAiCmE,UAAU,UAAU,CAAC;EAEjEI,eAAe,EAAEA,CAAA,KACfjG,GAAG,CAAC6B,GAAG,CAAC,mCAAmC,CAAC;EAE9CqE,mBAAmB,EAAEA,CAAA,KACnBlG,GAAG,CAAC0B,IAAI,CAAC,iCAAiC;AAC9C,CAAC;;AAED;AACA,OAAO,MAAMyE,iBAAiB,GAAG;EAC/B;EACAC,cAAc,EAAG3E,IAAS,IACxBzB,GAAG,CAAC0B,IAAI,CAAC,4BAA4B,EAAED,IAAI,CAAC;EAE9C4E,aAAa,EAAEA,CAAA,KACbrG,GAAG,CAAC6B,GAAG,CAAC,4BAA4B,CAAC;EAEvCyE,cAAc,EAAEA,CAACC,UAAkB,EAAE9E,IAAS,KAC5CzB,GAAG,CAACsC,GAAG,CAAC,8BAA8BiE,UAAU,EAAE,EAAE9E,IAAI,CAAC;EAE3D+E,cAAc,EAAGD,UAAkB,IACjCvG,GAAG,CAACyC,MAAM,CAAC,8BAA8B8D,UAAU,EAAE,CAAC;EAExD;EACAE,SAAS,EAAGhF,IAAS,IACnBzB,GAAG,CAAC0B,IAAI,CAAC,sBAAsB,EAAED,IAAI,CAAC;EAExCiF,OAAO,EAAEA,CAAA,KACP1G,GAAG,CAAC6B,GAAG,CAAC,sBAAsB,CAAC;EAEjC8E,SAAS,EAAEA,CAACC,KAAa,EAAEnF,IAAS,KAClCzB,GAAG,CAACsC,GAAG,CAAC,wBAAwBsE,KAAK,EAAE,EAAEnF,IAAI,CAAC;EAEhDoF,SAAS,EAAGD,KAAa,IACvB5G,GAAG,CAACyC,MAAM,CAAC,wBAAwBmE,KAAK,EAAE,CAAC;EAE7C;EACAE,iBAAiB,EAAEA,CAACC,SAAiB,EAAER,UAAmB,EAAES,MAAiB,KAC3EhH,GAAG,CAACsC,GAAG,CAAC,4BAA4ByE,SAAS,aAAa,EAAE,IAAI,EAAE;IAChEzC,MAAM,EAAE;MAAE2C,WAAW,EAAEV,UAAU;MAAEW,OAAO,EAAEF;IAAO;EACrD,CAAC,CAAC;EAEJ;EACAG,YAAY,EAAG/E,OAAgB,IAC7BpC,GAAG,CAAC6B,GAAG,CAAC,2BAA2B,EAAE;IAAEyC,MAAM,EAAE;MAAEC,QAAQ,EAAEnC;IAAQ;EAAE,CAAC,CAAC;EAEzE;EACAgF,0BAA0B,EAAEA,CAAA,KAC1BpH,GAAG,CAAC0B,IAAI,CAAC,8CAA8C;AAC3D,CAAC;;AAED;AACA,OAAO,MAAM2F,gBAAgB,GAAG;EAC9B;EACAC,gBAAgB,EAAEA,CAACjD,KAAa,GAAG,EAAE,EAAEkD,MAAc,GAAG,CAAC,EAAEvG,MAAe,EAAEwG,QAAiB,KAC3FxH,GAAG,CAAC6B,GAAG,CAAC,iBAAiB,EAAE;IAAEyC,MAAM,EAAE;MAAED,KAAK;MAAEkD,MAAM;MAAEvG,MAAM;MAAEwG;IAAS;EAAE,CAAC,CAAC;EAE7EC,sBAAsB,EAAEA,CAACpD,KAAa,GAAG,EAAE,KACzCrE,GAAG,CAAC6B,GAAG,CAAC,uBAAuB,EAAE;IAAEyC,MAAM,EAAE;MAAED;IAAM;EAAE,CAAC,CAAC;EAEzDqD,UAAU,EAAEA,CAAA,KACV1H,GAAG,CAAC6B,GAAG,CAAC,wBAAwB,CAAC;EAEnC8F,cAAc,EAAEA,CAAA,KACd3H,GAAG,CAAC6B,GAAG,CAAC,6BAA6B,CAAC;EAExC;EACA+F,UAAU,EAAGC,cAAsB,IACjC7H,GAAG,CAACsC,GAAG,CAAC,kBAAkBuF,cAAc,OAAO,CAAC;EAElDC,cAAc,EAAGC,eAAyB,IACxC/H,GAAG,CAACsC,GAAG,CAAC,0BAA0B,EAAE;IAAE0F,gBAAgB,EAAED,eAAe;IAAE/G,MAAM,EAAE;EAAO,CAAC,CAAC;EAE5F;EACAiH,cAAc,EAAEA,CAAA,KACdjI,GAAG,CAAC6B,GAAG,CAAC,4BAA4B,CAAC;EAEvCqG,iBAAiB,EAAGC,WAAkB,IACpCnI,GAAG,CAACsC,GAAG,CAAC,4BAA4B,EAAE6F,WAAW,CAAC;EAEpD;EACAxC,YAAY,EAAEA,CAAA,KACZ3F,GAAG,CAAC6B,GAAG,CAAC,0BAA0B,CAAC;EAErCuG,mBAAmB,EAAEA,CAAA,KACnBpI,GAAG,CAAC0B,IAAI,CAAC,qCAAqC,CAAC;EAEjD;EACA2G,uBAAuB,EAAEA,CAACC,IAAY,GAAG,EAAE,KACzCtI,GAAG,CAACyC,MAAM,CAAC,wBAAwB,EAAE;IAAE6B,MAAM,EAAE;MAAEgE;IAAK;EAAE,CAAC;AAC7D,CAAC;;AAED;AACA,OAAO,MAAMC,MAAM,GAAG;EACpBC,SAAS,EAAG/G,IAAgB,IAC1BzB,GAAG,CAAC0B,IAAI,CAAC,gBAAgB,EAAED,IAAI,CAAC;EAElCgH,WAAW,EAAEA,CAAA,KACXzI,GAAG,CAAC6B,GAAG,CAAC,eAAe;AAC3B,CAAC;;AAED;AACA,OAAO,MAAM6G,YAAY,GAAG;EAC1BC,mBAAmB,EAAEA,CAAA,KACnB3I,GAAG,CAAC6B,GAAG,CAAC,oBAAoB,CAAC;EAE/B+G,cAAc,EAAEA,CAAC7B,SAAiB,EAAE3D,QAAiB,KACnDpD,GAAG,CAAC0B,IAAI,CAAC,sBAAsBqF,SAAS,aAAa3D,QAAQ,EAAE,CAAC;EAElEyF,mBAAmB,EAAEA,CAACC,UAAoB,EAAE1F,QAAiB,KAC3DpD,GAAG,CAAC0B,IAAI,CAAC,yBAAyB,EAAE;IAAEqH,WAAW,EAAED,UAAU;IAAE1F;EAAS,CAAC,CAAC;EAE5E4F,mBAAmB,EAAGjC,SAAiB,IACrC/G,GAAG,CAAC6B,GAAG,CAAC,sBAAsBkF,SAAS,YAAY,CAAC;EAEtD;EACAkC,gBAAgB,EAAGxH,IAAoE,IACrFzB,GAAG,CAAC0B,IAAI,CAAC,wBAAwB,EAAED,IAAI,CAAC;EAE1CyH,qBAAqB,EAAEA,CAAA,KACrBlJ,GAAG,CAAC6B,GAAG,CAAC,gCAAgC,CAAC;EAE3CsH,iBAAiB,EAAEA,CAACC,YAAoB,EAAEC,SAAkB,KAC1DrJ,GAAG,CAAC0B,IAAI,CAAC,0BAA0B0H,YAAY,sBAAsBC,SAAS,EAAE,CAAC;EAEnFC,oBAAoB,EAAGtI,MAAe,IACpChB,GAAG,CAAC6B,GAAG,CAAC,gCAAgC,EAAE;IAAEyC,MAAM,EAAE;MAAEtD;IAAO;EAAE,CAAC;AACpE,CAAC;AAED,eAAehB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}