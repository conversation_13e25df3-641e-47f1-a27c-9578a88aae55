from pydantic import BaseModel, EmailStr
from decimal import Decimal
from typing import List, Optional, Dict
from datetime import datetime

class UserCreate(BaseModel):
    email: EmailStr
    password: str
    groq_api_key: str

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "securepassword123",
                "groq_api_key": "gsk_your_groq_api_key_here"
            }
        }

class UserOut(BaseModel):
    id: int
    email: EmailStr
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    phone: Optional[str] = None
    timezone: str = "UTC"
    currency: str = "PKR"
    language: str = "en"
    email_notifications: bool = True
    expense_notifications: bool = True
    approval_notifications: bool = True
    settlement_notifications: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Token(BaseModel):
    access_token: str
    token_type: str

class UserLogin(BaseModel):
    email: EmailStr
    password: str

    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "securepassword123"
            }
        }

from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class GroupBase(BaseModel):
    name: str

class GroupCreate(GroupBase):
    pass

class GroupResponse(GroupBase):
    id: int
    creator_id: int
    members: List[UserOut] = []

    class Config:
        from_attributes = True 

class JoinGroup(BaseModel):
    group_id: int

class ExpenseCreate(BaseModel):
    group_id: int
    total: Decimal
    description: str

    class Config:
        json_schema_extra = {
            "example": {
                "group_id": 1,
                "total": 25.50,
                "description": "Lunch at restaurant"
            }
        }

class ShareOut(BaseModel):
    user_id: int
    amount: Decimal
    paid: bool

    class Config:
        from_attributes = True 

class ExpenseOut(BaseModel):
    id: int
    payer_id: int
    group_id: int
    total: Decimal
    description: str
    shares: List[ShareOut]

    class Config:
        from_attributes = True 

class Balance(BaseModel):
    user_id: int
    email: EmailStr
    amount: Decimal  # positive: they owe you; negative: you owe them

    class Config:
        from_attributes = True 


class SettlementRequest(BaseModel):
    target_user_id: int    # who you’re paying
    amount: Decimal        # how much you’re paying

class SettlementResult(BaseModel):
    share_id: int
    paid_amount: Decimal   # how much was applied to this share
    remaining_amount: Decimal  # what’s left on that share

    class Config:
        from_attributes = True

class SettlementSummary(BaseModel):
    total_paid: Decimal
    target_user_email: str
    settlements: List[SettlementResult]
    message: str

    class Config:
        from_attributes = True

# NLP Schemas
class NLPRequest(BaseModel):
    message: str

    class Config:
        json_schema_extra = {
            "example": {
                "message": "Add lunch 25 split among roommates"
            }
        }

class NLPResponse(BaseModel):
    success: bool
    intent: str
    message: str
    result: Optional[dict] = None

    class Config:
        from_attributes = True

# Expense Approval Schemas
class ExpenseApprovalBase(BaseModel):
    approved: Optional[bool] = None

class ExpenseApprovalCreate(ExpenseApprovalBase):
    expense_id: int

class ExpenseApprovalUpdate(BaseModel):
    approved: bool

class ExpenseApprovalOut(ExpenseApprovalBase):
    id: int
    expense_id: int
    user_id: int
    approved_at: Optional[datetime] = None
    created_at: datetime
    user: UserOut

    class Config:
        from_attributes = True

# Settlement Schemas
class SettlementBase(BaseModel):
    amount: float
    description: Optional[str] = None

class SettlementCreate(SettlementBase):
    recipient_id: int

class SettlementUpdate(BaseModel):
    status: str

class SettlementOut(SettlementBase):
    id: int
    payer_id: int
    recipient_id: int
    status: str
    created_at: datetime
    confirmed_at: Optional[datetime] = None
    payer: UserOut
    recipient: UserOut

    class Config:
        from_attributes = True

# Updated Expense Schema with Approval Status
class ExpenseOutWithApprovals(ExpenseOut):
    status: str
    approved_at: Optional[datetime] = None
    approvals: List[ExpenseApprovalOut] = []

    class Config:
        from_attributes = True

# Settings schemas
class UserProfileUpdate(BaseModel):
    full_name: Optional[str] = None
    phone: Optional[str] = None
    timezone: Optional[str] = None
    currency: Optional[str] = None
    language: Optional[str] = None

class AvatarUpload(BaseModel):
    avatar_url: str

class PasswordChange(BaseModel):
    current_password: str
    new_password: str

class GroqApiKeyUpdate(BaseModel):
    groq_api_key: str

class NotificationPreferences(BaseModel):
    email_notifications: bool = True
    expense_notifications: bool = True
    approval_notifications: bool = True
    settlement_notifications: bool = True

class DisplaySettings(BaseModel):
    theme: Optional[str] = None
    currency: Optional[str] = None
    language: Optional[str] = None
    timezone: Optional[str] = None

class AccountDeletion(BaseModel):
    password: str

# Profile management schemas
class ProfileSettings(BaseModel):
    profile: UserProfileUpdate
    notifications: NotificationPreferences
    display: DisplaySettings

class ProfileResponse(BaseModel):
    user: UserOut
    message: str

    class Config:
        from_attributes = True

# Recurring expense schemas
class RecurringExpenseTemplateCreate(BaseModel):
    name: str
    description: str
    amount: float
    frequency: str  # daily, weekly, monthly, yearly
    interval_count: int = 1
    start_date: datetime
    end_date: Optional[datetime] = None
    group_id: int

class RecurringExpenseTemplateUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    amount: Optional[float] = None
    frequency: Optional[str] = None
    interval_count: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_active: Optional[bool] = None

class RecurringExpenseTemplateOut(BaseModel):
    id: int
    name: str
    description: str
    amount: float
    frequency: str
    interval_count: int
    start_date: datetime
    end_date: Optional[datetime] = None
    next_execution: datetime
    is_active: bool
    creator_id: int
    group_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class RecurringExpenseExecutionOut(BaseModel):
    id: int
    template_id: int
    expense_id: Optional[int] = None
    execution_date: datetime
    status: str
    error_message: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True

class RecurringExpenseTemplateWithExecutions(BaseModel):
    template: RecurringExpenseTemplateOut
    recent_executions: List[RecurringExpenseExecutionOut]
    next_execution_date: datetime
    total_executions: int
    successful_executions: int
    failed_executions: int

    class Config:
        from_attributes = True

# Categorization schemas
class ExpenseCategoryCreate(BaseModel):
    name: str
    description: Optional[str] = None
    color: str = "#6B7280"
    icon: Optional[str] = None
    parent_id: Optional[int] = None

class ExpenseCategoryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    color: Optional[str] = None
    icon: Optional[str] = None
    parent_id: Optional[int] = None

class ExpenseCategoryOut(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    color: str
    icon: Optional[str] = None
    parent_id: Optional[int] = None
    is_system: bool
    created_by: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ExpenseTagCreate(BaseModel):
    name: str
    color: str = "#3B82F6"

class ExpenseTagUpdate(BaseModel):
    name: Optional[str] = None
    color: Optional[str] = None

class ExpenseTagOut(BaseModel):
    id: int
    name: str
    color: str
    created_by: int
    created_at: datetime

    class Config:
        from_attributes = True

class ExpenseWithCategorization(BaseModel):
    id: int
    description: str
    total: float
    status: str
    created_at: datetime
    category: Optional[ExpenseCategoryOut] = None
    tags: List[ExpenseTagOut] = []
    payer_email: str
    group_name: str

    class Config:
        from_attributes = True

class CategoryAnalytics(BaseModel):
    category: ExpenseCategoryOut
    total_amount: float
    expense_count: int
    percentage: float
    trend: str  # "up", "down", "stable"

class TagAnalytics(BaseModel):
    tag: ExpenseTagOut
    total_amount: float
    expense_count: int
    percentage: float

class CategorizationAnalytics(BaseModel):
    total_categorized: int
    total_uncategorized: int
    categorization_percentage: float
    category_breakdown: List[CategoryAnalytics]
    tag_breakdown: List[TagAnalytics]
    top_categories: List[CategoryAnalytics]
    top_tags: List[TagAnalytics]

    class Config:
        from_attributes = True

# Enhanced Notification Schemas
class NotificationCreate(BaseModel):
    title: str
    message: str
    category: str  # expense, group, settlement, approval, system
    priority: str = "medium"  # low, medium, high, urgent
    action_url: Optional[str] = None
    action_text: Optional[str] = None
    expense_id: Optional[int] = None
    group_id: Optional[int] = None
    settlement_id: Optional[int] = None
    data: Optional[str] = None
    expires_at: Optional[datetime] = None

class NotificationUpdate(BaseModel):
    status: Optional[str] = None  # unread, read, archived
    read_at: Optional[datetime] = None

class NotificationOut(BaseModel):
    id: int
    title: str
    message: str
    category: str
    priority: str
    status: str
    action_url: Optional[str] = None
    action_text: Optional[str] = None
    expense_id: Optional[int] = None
    group_id: Optional[int] = None
    settlement_id: Optional[int] = None
    data: Optional[str] = None
    created_at: datetime
    read_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class NotificationSummary(BaseModel):
    total_count: int
    unread_count: int
    categories: Dict[str, int]
    priorities: Dict[str, int]

class NotificationPreferenceUpdate(BaseModel):
    template_key: str
    enabled: bool = True
    email_enabled: bool = False
    push_enabled: bool = True

class NotificationPreferenceOut(BaseModel):
    template_key: str
    enabled: bool
    email_enabled: bool
    push_enabled: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class BulkNotificationUpdate(BaseModel):
    notification_ids: List[int]
    status: str  # read, archived

class NotificationTemplateOut(BaseModel):
    template_key: str
    category: str
    title_template: str
    message_template: str
    action_text_template: Optional[str] = None
    action_url_template: Optional[str] = None
    priority: str
    is_active: bool

    class Config:
        from_attributes = True

# Group management schemas
class TransferOwnershipRequest(BaseModel):
    new_owner_id: int

# Bulk operation schemas
class BulkExpenseAction(BaseModel):
    expense_ids: List[int]
    action: str  # 'approve', 'reject', 'delete'

class BulkApprovalRequest(BaseModel):
    expense_ids: List[int]
    approved: bool

class BulkDeleteRequest(BaseModel):
    expense_ids: List[int]

class BulkCategorizeRequest(BaseModel):
    expense_ids: List[int]
    category: str

class BulkOperationResult(BaseModel):
    success_count: int
    failed_count: int
    total_count: int
    failed_items: List[dict] = []
    message: str

    class Config:
        from_attributes = True

# Advanced filtering schemas
class ExpenseFilter(BaseModel):
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    min_amount: Optional[float] = None
    max_amount: Optional[float] = None
    status: Optional[str] = None
    payer_id: Optional[int] = None
    group_id: Optional[int] = None
    category: Optional[str] = None
    description_contains: Optional[str] = None
    page: int = 1
    page_size: int = 20
    sort_by: Optional[str] = "created_at"
    sort_order: Optional[str] = "desc"

class PaginatedExpenseResponse(BaseModel):
    items: List[ExpenseOut]
    total: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_prev: bool

    class Config:
        from_attributes = True

# Export schemas
class ExportRequest(BaseModel):
    format: str  # 'csv' or 'pdf'
    filters: ExpenseFilter

class ExportSummary(BaseModel):
    total_expenses: int
    total_amount: float
    average_amount: float
    date_range: Optional[dict] = None
    groups: List[str]
    status_breakdown: dict

    class Config:
        from_attributes = True
