from sqlalchemy import Column, Integer, String, ForeignKey, Table, Numeric, DateTime, Boolean, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
import enum

class ExpenseStatus(enum.Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"

class SettlementStatus(enum.Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    DISPUTED = "disputed"

class JoinRequestStatus(enum.Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    groq_api_key = Column(String, nullable=False)

    # Profile fields
    full_name = Column(String, nullable=True)
    avatar_url = Column(String, nullable=True)
    phone = Column(String, nullable=True)
    timezone = Column(String, default="UTC")
    currency = Column(String, default="PKR")
    language = Column(String, default="en")

    # Notification preferences
    email_notifications = Column(Boolean, default=True)
    expense_notifications = Column(Boolean, default=True)
    approval_notifications = Column(Boolean, default=True)
    settlement_notifications = Column(Boolean, default=True)

    # Profile timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

# Association table between users and groups
user_group_association = Table(
    "group_memberships",
    Base.metadata,
    Column("user_id", Integer, ForeignKey("users.id")),
    Column("group_id", Integer, ForeignKey("groups.id"))
)

class Group(Base):
    __tablename__ = "groups"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, nullable=False)
    description = Column(String, nullable=True)
    creator_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    creator = relationship("User", backref="created_groups")
    members = relationship("User", secondary=user_group_association, backref="groups")
    join_requests = relationship("GroupJoinRequest", back_populates="group")

from sqlalchemy import Column, Integer, ForeignKey, Numeric, String, DateTime, Boolean
from sqlalchemy.orm import relationship

class Expense(Base):
    __tablename__ = "expenses"

    id          = Column(Integer, primary_key=True, index=True)
    payer_id    = Column(Integer, ForeignKey("users.id"), nullable=False)
    group_id    = Column(Integer, ForeignKey("groups.id"), nullable=False)
    total       = Column(Numeric(12, 2), nullable=False)
    description = Column(String, nullable=True)
    status      = Column(String, default=ExpenseStatus.PENDING.value)
    created_at  = Column(DateTime(timezone=True), server_default=func.now())
    approved_at = Column(DateTime(timezone=True), nullable=True)

    # Categorization fields
    category_id = Column(Integer, ForeignKey("expense_categories.id"), nullable=True)

    payer   = relationship("User", backref="paid_expenses")
    group   = relationship("Group", backref="expenses")
    shares  = relationship("Share", back_populates="expense")
    approvals = relationship("ExpenseApproval", back_populates="expense")
    category = relationship("ExpenseCategory", back_populates="expenses")
    tags = relationship("ExpenseTag", secondary="expense_tag_associations", backref="expenses")

class Share(Base):
    __tablename__ = "shares"

    id          = Column(Integer, primary_key=True, index=True)
    expense_id  = Column(Integer, ForeignKey("expenses.id"), nullable=False)
    user_id     = Column(Integer, ForeignKey("users.id"), nullable=False)
    amount      = Column(Numeric(12,2), nullable=False)
    paid        = Column(Boolean, default=False)

    expense = relationship("Expense", back_populates="shares")
    user    = relationship("User", backref="shares")

class ExpenseApproval(Base):
    __tablename__ = "expense_approvals"

    id          = Column(Integer, primary_key=True, index=True)
    expense_id  = Column(Integer, ForeignKey("expenses.id"), nullable=False)
    user_id     = Column(Integer, ForeignKey("users.id"), nullable=False)
    approved    = Column(Boolean, nullable=True)  # None=pending, True=approved, False=rejected
    approved_at = Column(DateTime(timezone=True), nullable=True)
    created_at  = Column(DateTime(timezone=True), server_default=func.now())

    expense = relationship("Expense", back_populates="approvals")
    user    = relationship("User", backref="expense_approvals")

class Settlement(Base):
    __tablename__ = "settlements"

    id              = Column(Integer, primary_key=True, index=True)
    payer_id        = Column(Integer, ForeignKey("users.id"), nullable=False)
    recipient_id    = Column(Integer, ForeignKey("users.id"), nullable=False)
    amount          = Column(Numeric(12, 2), nullable=False)
    status          = Column(String, default=SettlementStatus.PENDING.value)
    created_at      = Column(DateTime(timezone=True), server_default=func.now())
    confirmed_at    = Column(DateTime(timezone=True), nullable=True)
    description     = Column(String, nullable=True)

    payer     = relationship("User", foreign_keys=[payer_id], backref="sent_settlements")
    recipient = relationship("User", foreign_keys=[recipient_id], backref="received_settlements")

class GroupJoinRequest(Base):
    __tablename__ = "group_join_requests"

    id          = Column(Integer, primary_key=True, index=True)
    group_id    = Column(Integer, ForeignKey("groups.id"), nullable=False)
    user_id     = Column(Integer, ForeignKey("users.id"), nullable=False)
    status      = Column(String, default=JoinRequestStatus.PENDING.value)
    message     = Column(String, nullable=True)
    created_at  = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True), nullable=True)
    processed_by = Column(Integer, ForeignKey("users.id"), nullable=True)

    group = relationship("Group", back_populates="join_requests")
    user = relationship("User", foreign_keys=[user_id], backref="join_requests")
    processor = relationship("User", foreign_keys=[processed_by], backref="processed_join_requests")


# Recurring expense models
class RecurrenceFrequency(enum.Enum):
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    YEARLY = "yearly"


class RecurringExpenseTemplate(Base):
    __tablename__ = "recurring_expense_templates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=False)
    amount = Column(Numeric(10, 2), nullable=False)
    frequency = Column(Enum(RecurrenceFrequency), nullable=False)
    interval_count = Column(Integer, default=1)  # e.g., every 2 weeks
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=True)  # Optional end date
    next_execution = Column(DateTime(timezone=True), nullable=False)
    is_active = Column(Boolean, default=True)

    # Foreign keys
    creator_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    group_id = Column(Integer, ForeignKey("groups.id"), nullable=False)

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    creator = relationship("User", backref="recurring_templates")
    group = relationship("Group", backref="recurring_templates")
    executions = relationship("RecurringExpenseExecution", back_populates="template", cascade="all, delete-orphan")


class RecurringExpenseExecution(Base):
    __tablename__ = "recurring_expense_executions"

    id = Column(Integer, primary_key=True, index=True)
    template_id = Column(Integer, ForeignKey("recurring_expense_templates.id"), nullable=False)
    expense_id = Column(Integer, ForeignKey("expenses.id"), nullable=True)  # Null if execution failed
    execution_date = Column(DateTime(timezone=True), nullable=False)
    status = Column(String, default="pending")  # pending, success, failed
    error_message = Column(String, nullable=True)

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    template = relationship("RecurringExpenseTemplate", back_populates="executions")
    expense = relationship("Expense", backref="recurring_execution")


# Categorization models
class ExpenseCategory(Base):
    __tablename__ = "expense_categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    color = Column(String, default="#6B7280")  # Hex color code
    icon = Column(String, nullable=True)  # Icon name or emoji
    parent_id = Column(Integer, ForeignKey("expense_categories.id"), nullable=True)  # For hierarchical categories
    is_system = Column(Boolean, default=False)  # System vs user-created categories

    # Foreign keys
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)  # Null for system categories

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    creator = relationship("User", backref="created_categories")
    parent = relationship("ExpenseCategory", remote_side=[id], backref="subcategories")
    expenses = relationship("Expense", back_populates="category")


class ExpenseTag(Base):
    __tablename__ = "expense_tags"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    color = Column(String, default="#3B82F6")  # Hex color code

    # Foreign keys
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    creator = relationship("User", backref="created_tags")


# Association table for many-to-many relationship between expenses and tags
expense_tags = Table(
    'expense_tag_associations',
    Base.metadata,
    Column('expense_id', Integer, ForeignKey('expenses.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('expense_tags.id'), primary_key=True)
)


# Enhanced Notification System Models
class NotificationCategory(enum.Enum):
    EXPENSE = "expense"
    GROUP = "group"
    SETTLEMENT = "settlement"
    APPROVAL = "approval"
    SYSTEM = "system"


class NotificationPriority(enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class NotificationStatus(enum.Enum):
    UNREAD = "unread"
    READ = "read"
    ARCHIVED = "archived"


class Notification(Base):
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)

    # Recipient information
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Notification content
    title = Column(String, nullable=False)
    message = Column(String, nullable=False)
    category = Column(Enum(NotificationCategory), nullable=False)
    priority = Column(Enum(NotificationPriority), default=NotificationPriority.MEDIUM)
    status = Column(Enum(NotificationStatus), default=NotificationStatus.UNREAD)

    # Action and navigation
    action_url = Column(String, nullable=True)  # Deep link URL for navigation
    action_text = Column(String, nullable=True)  # Call-to-action text

    # Related entities (for context and filtering)
    expense_id = Column(Integer, ForeignKey("expenses.id"), nullable=True)
    group_id = Column(Integer, ForeignKey("groups.id"), nullable=True)
    settlement_id = Column(Integer, nullable=True)  # For future settlement tracking

    # Metadata
    data = Column(String, nullable=True)  # JSON string for additional data
    expires_at = Column(DateTime(timezone=True), nullable=True)  # Optional expiration

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    read_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", backref="notifications")
    expense = relationship("Expense", backref="notifications")
    group = relationship("Group", backref="notifications")


class NotificationTemplate(Base):
    __tablename__ = "notification_templates"

    id = Column(Integer, primary_key=True, index=True)

    # Template identification
    template_key = Column(String, unique=True, nullable=False)  # e.g., "expense_created"
    category = Column(Enum(NotificationCategory), nullable=False)

    # Template content
    title_template = Column(String, nullable=False)  # e.g., "New Expense: {expense_description}"
    message_template = Column(String, nullable=False)
    action_text_template = Column(String, nullable=True)
    action_url_template = Column(String, nullable=True)

    # Settings
    priority = Column(Enum(NotificationPriority), default=NotificationPriority.MEDIUM)
    is_active = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())


class UserNotificationPreference(Base):
    __tablename__ = "user_notification_preferences"

    id = Column(Integer, primary_key=True, index=True)

    # User and notification type
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    template_key = Column(String, nullable=False)  # Links to NotificationTemplate.template_key

    # Preferences
    enabled = Column(Boolean, default=True)
    email_enabled = Column(Boolean, default=False)
    push_enabled = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    user = relationship("User", backref="notification_preferences")

    # Unique constraint
    __table_args__ = (
        Column('user_id', Integer, ForeignKey('users.id')),
        Column('template_key', String),
        {'extend_existing': True}
    )
