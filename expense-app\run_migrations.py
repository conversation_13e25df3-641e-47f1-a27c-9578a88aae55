#!/usr/bin/env python3
"""
Script to run database migrations
"""

import os
import sys
import sqlite3
from alembic.config import Config
from alembic import command

def check_database_exists():
    """Check if database file exists"""
    db_path = "./dev.db"
    return os.path.exists(db_path)

def run_migrations():
    """Run all pending database migrations"""

    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Path to alembic.ini
    alembic_cfg_path = os.path.join(script_dir, "alembic.ini")

    if not os.path.exists(alembic_cfg_path):
        print("Error: alembic.ini not found!")
        sys.exit(1)

    # Create Alembic configuration
    alembic_cfg = Config(alembic_cfg_path)

    try:
        print("🔄 Running database migrations...")

        # Check current revision
        try:
            current_rev = command.current(alembic_cfg)
            print(f"📍 Current revision: {current_rev}")
        except:
            print("📍 No current revision found (new database)")

        # Run migrations
        command.upgrade(alembic_cfg, "head")

        print("✅ Database migrations completed successfully!")

        # Verify the schema
        verify_user_schema()

    except Exception as e:
        print(f"❌ Error running migrations: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Make sure the database file is not locked")
        print("2. Try deleting dev.db and running again")
        print("3. Check that all migration files are valid")
        sys.exit(1)

def verify_user_schema():
    """Verify that the users table has all required columns"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()

        cursor.execute("PRAGMA table_info(users);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        required_columns = [
            'id', 'email', 'hashed_password', 'groq_api_key',
            'full_name', 'avatar_url', 'phone', 'timezone', 'currency', 'language',
            'email_notifications', 'expense_notifications', 'approval_notifications',
            'settlement_notifications', 'created_at', 'updated_at'
        ]

        missing_columns = [col for col in required_columns if col not in column_names]

        if missing_columns:
            print(f"⚠️ Warning: Missing columns in users table: {', '.join(missing_columns)}")
        else:
            print("✅ Users table schema is complete")

        conn.close()

    except Exception as e:
        print(f"⚠️ Could not verify schema: {e}")

if __name__ == "__main__":
    run_migrations()
