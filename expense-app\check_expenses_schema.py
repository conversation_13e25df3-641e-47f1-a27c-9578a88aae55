#!/usr/bin/env python3
"""
Check the current expenses table schema and categorization tables
"""

import sqlite3
import os

def check_expenses_table():
    """Check the current expenses table schema"""
    db_path = "./dev.db"
    
    if not os.path.exists(db_path):
        print("❌ Database file 'dev.db' does not exist!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check expenses table schema
        cursor.execute("PRAGMA table_info(expenses);")
        columns = cursor.fetchall()
        
        print("📋 Current expenses table schema:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
        
        column_names = [col[1] for col in columns]
        
        # Check for category_id column
        if 'category_id' in column_names:
            print("✅ category_id column exists in expenses table")
        else:
            print("❌ category_id column MISSING from expenses table")
        
        return 'category_id' in column_names
        
    except Exception as e:
        print(f"❌ Error checking expenses table: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def check_categorization_tables():
    """Check if categorization tables exist"""
    db_path = "./dev.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        
        categorization_tables = [
            'expense_categories',
            'expense_tags', 
            'expense_tag_associations'
        ]
        
        print("\n📋 Categorization tables status:")
        for table in categorization_tables:
            if table in table_names:
                print(f"  ✅ {table} - EXISTS")
                
                # Show table schema
                cursor.execute(f"PRAGMA table_info({table});")
                columns = cursor.fetchall()
                print(f"     Columns: {', '.join([col[1] for col in columns])}")
            else:
                print(f"  ❌ {table} - MISSING")
        
        return all(table in table_names for table in categorization_tables)
        
    except Exception as e:
        print(f"❌ Error checking categorization tables: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def test_problematic_query():
    """Test the query that's causing the error"""
    try:
        conn = sqlite3.connect("./dev.db")
        cursor = conn.cursor()
        
        print("\n🧪 Testing problematic query...")
        
        # This is the query that's failing
        cursor.execute("SELECT expenses.category_id FROM expenses LIMIT 1")
        result = cursor.fetchone()
        
        print("✅ Query 'SELECT expenses.category_id FROM expenses' successful!")
        if result:
            print(f"   Sample category_id: {result[0]}")
        else:
            print("   No expenses found, but query structure is valid")
        
        return True
        
    except Exception as e:
        print(f"❌ Query failed: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def main():
    """Main function"""
    print("🔍 Checking Expenses Table and Categorization Schema")
    print("=" * 60)
    
    expenses_ok = check_expenses_table()
    categorization_ok = check_categorization_tables()
    
    if expenses_ok:
        query_ok = test_problematic_query()
    else:
        query_ok = False
    
    print("\n" + "=" * 60)
    print("📊 Summary:")
    print(f"  Expenses table has category_id: {'✅' if expenses_ok else '❌'}")
    print(f"  Categorization tables exist: {'✅' if categorization_ok else '❌'}")
    print(f"  Problematic query works: {'✅' if query_ok else '❌'}")
    
    if not expenses_ok or not categorization_ok:
        print("\n🔧 Action needed: Run categorization migration fix")

if __name__ == "__main__":
    main()
