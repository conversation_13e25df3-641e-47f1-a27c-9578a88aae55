"""Add recurring expenses tables

Revision ID: 004_add_recurring_expenses
Revises: 003_add_user_profile_fields
Create Date: 2024-01-15 11:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '004_add_recurring_expenses'
down_revision = '003_add_user_profile_fields'
branch_labels = None
depends_on = None


def upgrade():
    # Create recurring_expense_templates table
    op.create_table('recurring_expense_templates',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('description', sa.String(), nullable=False),
        sa.Column('amount', sa.Numeric(10, 2), nullable=False),
        sa.Column('frequency', sa.String(), nullable=False),  # Store as string for SQLite
        sa.Column('interval_count', sa.Integer(), nullable=True),
        sa.Column('start_date', sa.DateTime(), nullable=False),
        sa.Column('end_date', sa.DateTime(), nullable=True),
        sa.Column('next_execution', sa.DateTime(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('creator_id', sa.Integer(), nullable=False),
        sa.Column('group_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['creator_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['group_id'], ['groups.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_recurring_expense_templates_id'), 'recurring_expense_templates', ['id'], unique=False)

    # Create recurring_expense_executions table
    op.create_table('recurring_expense_executions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('template_id', sa.Integer(), nullable=False),
        sa.Column('expense_id', sa.Integer(), nullable=True),
        sa.Column('execution_date', sa.DateTime(), nullable=False),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('error_message', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['expense_id'], ['expenses.id'], ),
        sa.ForeignKeyConstraint(['template_id'], ['recurring_expense_templates.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_recurring_expense_executions_id'), 'recurring_expense_executions', ['id'], unique=False)

    # Set default values for new columns (SQLite compatible)
    op.execute("UPDATE recurring_expense_templates SET interval_count = 1 WHERE interval_count IS NULL")
    op.execute("UPDATE recurring_expense_templates SET is_active = 1 WHERE is_active IS NULL")
    op.execute("UPDATE recurring_expense_executions SET status = 'pending' WHERE status IS NULL")
    op.execute("UPDATE recurring_expense_templates SET created_at = datetime('now') WHERE created_at IS NULL")
    op.execute("UPDATE recurring_expense_templates SET updated_at = datetime('now') WHERE updated_at IS NULL")
    op.execute("UPDATE recurring_expense_executions SET created_at = datetime('now') WHERE created_at IS NULL")


def downgrade():
    # Drop tables
    op.drop_index(op.f('ix_recurring_expense_executions_id'), table_name='recurring_expense_executions')
    op.drop_table('recurring_expense_executions')
    op.drop_index(op.f('ix_recurring_expense_templates_id'), table_name='recurring_expense_templates')
    op.drop_table('recurring_expense_templates')
