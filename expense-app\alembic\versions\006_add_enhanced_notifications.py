"""Add enhanced notification system tables

Revision ID: 006_add_enhanced_notifications
Revises: 005_add_categorization_tables
Create Date: 2024-01-15 13:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '006_add_enhanced_notifications'
down_revision = '005_add_categorization_tables'
branch_labels = None
depends_on = None


def upgrade():
    # Create notifications table
    op.create_table('notifications',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(), nullable=False),
        sa.Column('message', sa.String(), nullable=False),
        sa.Column('category', sa.String(), nullable=False),
        sa.Column('priority', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('action_url', sa.String(), nullable=True),
        sa.Column('action_text', sa.String(), nullable=True),
        sa.Column('expense_id', sa.Integer(), nullable=True),
        sa.Column('group_id', sa.Integer(), nullable=True),
        sa.Column('settlement_id', sa.Integer(), nullable=True),
        sa.Column('data', sa.String(), nullable=True),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('read_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['expense_id'], ['expenses.id'], ),
        sa.ForeignKeyConstraint(['group_id'], ['groups.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notifications_id'), 'notifications', ['id'], unique=False)
    op.create_index('ix_notifications_user_status', 'notifications', ['user_id', 'status'], unique=False)
    op.create_index('ix_notifications_created_at', 'notifications', ['created_at'], unique=False)

    # Create notification_templates table
    op.create_table('notification_templates',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('template_key', sa.String(), nullable=False),
        sa.Column('category', sa.String(), nullable=False),
        sa.Column('title_template', sa.String(), nullable=False),
        sa.Column('message_template', sa.String(), nullable=False),
        sa.Column('action_text_template', sa.String(), nullable=True),
        sa.Column('action_url_template', sa.String(), nullable=True),
        sa.Column('priority', sa.String(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('template_key')
    )
    op.create_index(op.f('ix_notification_templates_id'), 'notification_templates', ['id'], unique=False)

    # Create user_notification_preferences table
    op.create_table('user_notification_preferences',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('template_key', sa.String(), nullable=False),
        sa.Column('enabled', sa.Boolean(), nullable=True),
        sa.Column('email_enabled', sa.Boolean(), nullable=True),
        sa.Column('push_enabled', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_notification_preferences_id'), 'user_notification_preferences', ['id'], unique=False)
    op.create_index('ix_user_notification_preferences_user_template', 'user_notification_preferences', ['user_id', 'template_key'], unique=True)

    # Set default values
    op.execute("UPDATE notifications SET priority = 'medium' WHERE priority IS NULL")
    op.execute("UPDATE notifications SET status = 'unread' WHERE status IS NULL")
    op.execute("UPDATE notifications SET created_at = datetime('now') WHERE created_at IS NULL")
    
    op.execute("UPDATE notification_templates SET priority = 'medium' WHERE priority IS NULL")
    op.execute("UPDATE notification_templates SET is_active = 1 WHERE is_active IS NULL")
    op.execute("UPDATE notification_templates SET created_at = datetime('now') WHERE created_at IS NULL")
    op.execute("UPDATE notification_templates SET updated_at = datetime('now') WHERE updated_at IS NULL")
    
    op.execute("UPDATE user_notification_preferences SET enabled = 1 WHERE enabled IS NULL")
    op.execute("UPDATE user_notification_preferences SET email_enabled = 0 WHERE email_enabled IS NULL")
    op.execute("UPDATE user_notification_preferences SET push_enabled = 1 WHERE push_enabled IS NULL")
    op.execute("UPDATE user_notification_preferences SET created_at = datetime('now') WHERE created_at IS NULL")
    op.execute("UPDATE user_notification_preferences SET updated_at = datetime('now') WHERE updated_at IS NULL")


def downgrade():
    # Drop tables
    op.drop_index('ix_user_notification_preferences_user_template', table_name='user_notification_preferences')
    op.drop_index(op.f('ix_user_notification_preferences_id'), table_name='user_notification_preferences')
    op.drop_table('user_notification_preferences')
    
    op.drop_index(op.f('ix_notification_templates_id'), table_name='notification_templates')
    op.drop_table('notification_templates')
    
    op.drop_index('ix_notifications_created_at', table_name='notifications')
    op.drop_index('ix_notifications_user_status', table_name='notifications')
    op.drop_index(op.f('ix_notifications_id'), table_name='notifications')
    op.drop_table('notifications')
